import React, { useState, useEffect } from "react";
import {
  getMyResearchGroups,
  getGroupMembers,
  createGroupInvitation,
  getRoleTypes,
  getMyInvitations,
  startSupervisionProject
} from "../services/api";

import { Users, Mail, RefreshCw, Link, Copy, Briefcase } from "lucide-react"; // Import icons
import WorkspaceComponent from "../components/WorkspaceComponent";

const AdminDashboard = () => {

  // Data State
  const [researchGroup, setResearchGroup] = useState(null);
  const [members, setMembers] = useState([]);
  const [roleTypes, setRoleTypes] = useState([]);
  const [invitations, setInvitations] = useState([]);

  // UI State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState("workspace");

  // Form State
  const [inviteEmail, setInviteEmail] = useState("");
  const [selectedRole, setSelectedRole] = useState("");
  const [inviteStatus, setInviteStatus] = useState({ type: "", message: "" });

  const fetchData = async () => {
    setLoading(true);
    setError("");
    try {
      const groupsResponse = await getMyResearchGroups();

      if (groupsResponse.data && groupsResponse.data.results && groupsResponse.data.results.length > 0) {
        const firstGroup = groupsResponse.data.results[0];
        setResearchGroup(firstGroup);

        try {
          // Call them separately to see which one fails
          let membersResponse, rolesResponse;

          try {
            membersResponse = await getGroupMembers();
            setMembers(membersResponse.data || []);
          } catch (membersErr) {
            setError(`Failed to load members: ${membersErr.message}`);
          }

          try {
            rolesResponse = await getRoleTypes();

            // Handle both paginated and non-paginated responses
            let rolesArray = [];
            if (Array.isArray(rolesResponse.data)) {
              rolesArray = rolesResponse.data;
            } else if (rolesResponse.data && rolesResponse.data.results) {
              rolesArray = rolesResponse.data.results;
            }

            setRoleTypes(rolesArray);

            // Set a default role for the invite form
            if (rolesArray && rolesArray.length > 0) {
              const studentRole = rolesArray.find((r) => r.name.toLowerCase() === "student");
              if (studentRole) {
                setSelectedRole(studentRole.id);
              }
            }
          } catch (rolesErr) {
            setError(`Failed to load roles: ${rolesErr.message}`);
          }

          try {
            const invitationsResponse = await getMyInvitations();

            // Handle both paginated and non-paginated responses
            let invitationsArray = [];
            if (Array.isArray(invitationsResponse.data)) {
              invitationsArray = invitationsResponse.data;
            } else if (invitationsResponse.data && invitationsResponse.data.results) {
              invitationsArray = invitationsResponse.data.results;
            }

            setInvitations(invitationsArray);
          } catch {
            // Don't set error for invitations failure, just log it
          }

        } catch (secondaryErr) {
          setError(`Unexpected error: ${secondaryErr.message}`);
        }
      } else {
        setError("You are not an active member of any research groups.");
      }
    } catch (err) {
      setError(err.response?.data?.detail || err.message || "Could not load your workspace data.");
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on initial component load
  useEffect(() => {
    fetchData();
  }, []);

  const copyInvitationLink = (invitationCode) => {
    const invitationUrl = `${window.location.origin}/register?code=${invitationCode}`;
    navigator.clipboard.writeText(invitationUrl).then(() => {
      alert("Invitation link copied to clipboard!");
    }).catch(() => {
      alert("Failed to copy link. Please copy manually: " + invitationUrl);
    });
  };

  const handleInviteSubmit = async (e) => {
    e.preventDefault();
    if (!researchGroup?.id || !selectedRole) {
      setInviteStatus({ type: "error", message: "Error: A group and role must be selected." });
      return;
    }

    setInviteStatus({ type: "loading", message: "Sending..." });
    try {
      await createGroupInvitation(researchGroup.id, inviteEmail, selectedRole);
      setInviteStatus({ type: "success", message: `Invitation successfully created for ${inviteEmail}!` });
      setInviteEmail(""); // Clear input on success

      // Refresh the invitations list
      try {
        const invitationsResponse = await getMyInvitations();
        let invitationsArray = [];
        if (Array.isArray(invitationsResponse.data)) {
          invitationsArray = invitationsResponse.data;
        } else if (invitationsResponse.data && invitationsResponse.data.results) {
          invitationsArray = invitationsResponse.data.results;
        }
        setInvitations(invitationsArray);
      } catch {
        // Don't show error to user for refresh failure
      }
    } catch (error) {

      // Extract detailed error message
      let errorMessage = "Failed to create invitation.";
      if (error.response?.data) {
        if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.email) {
          errorMessage = `Email error: ${error.response.data.email[0]}`;
        } else if (error.response.data.intended_role) {
          errorMessage = `Role error: ${error.response.data.intended_role[0]}`;
        } else if (error.response.data.research_group) {
          errorMessage = `Group error: ${error.response.data.research_group[0]}`;
        } else if (error.response.data.non_field_errors) {
          errorMessage = error.response.data.non_field_errors[0];
        } else {
          errorMessage = JSON.stringify(error.response.data);
        }
      }

      setInviteStatus({ type: "error", message: `Error: ${errorMessage}` });
    }
  };

  const handleStartSupervision = async (studentId, studentName) => {
    if (window.confirm(`Are you sure you want to start a new supervision project for ${studentName}?`)) {
      try {
        // Call the new API endpoint
        const response = await startSupervisionProject(studentId);
        const newProject = response.data;

        // On success, show success message
        alert(`Supervision project '${newProject.title}' created successfully!`);
      } catch (error) {
        alert(`Error: ${error.response?.data?.error || "Could not start the supervision project."}`);
      }
    }
  };

  if (loading) {
    return (
      <div style={{ ...styles.card, margin: "20px auto", maxWidth: "1200px", textAlign: "center" }}>
        Loading Administrator Dashboard...
      </div>
    );
  }

  return (
    <div style={styles.container}>
      <div style={{ ...styles.card, ...styles.header }}>
        <div>
          <h1 style={styles.title}>Administrator Dashboard</h1>
          <h2 style={styles.subtitle}>{researchGroup?.name || "No Group Found"}</h2>
        </div>
        <div style={{ display: "flex", gap: "12px" }}>
          <button onClick={fetchData} title="Refresh Data" style={styles.buttonSecondary}>
            <RefreshCw size={14} />
          </button>
        </div>
      </div>

      {error && <p style={{ ...styles.card, color: "red" }}>{error}</p>}


      <div style={styles.tabs}>
        <button
          onClick={() => setActiveTab("workspace")}
          style={activeTab === "workspace" ? styles.tabActive : styles.tab}
        >
          <Briefcase size={16} /> Workspace
        </button>
        <button onClick={() => setActiveTab("members")} style={activeTab === "members" ? styles.tabActive : styles.tab}>
          <Users size={16} /> Members ({members.length})
        </button>
        <button
          onClick={() => setActiveTab("invitations")}
          style={activeTab === "invitations" ? styles.tabActive : styles.tab}
        >
          <Link size={16} /> Invitations ({invitations.length})
        </button>
        <button onClick={() => setActiveTab("invite")} style={activeTab === "invite" ? styles.tabActive : styles.tab}>
          <Mail size={16} /> Invite Members
        </button>
      </div>

      {activeTab === "workspace" ? (
        <WorkspaceComponent />
      ) : (
        <div style={styles.card}>
          {activeTab === "members" && (
            <div>
              <h2>Member Roster</h2>
              <table style={styles.table}>
                <thead>
                  <tr>
                    <th style={styles.th}>Name</th>
                    <th style={styles.th}>Username</th>
                    <th style={styles.th}>Email</th>
                    <th style={styles.th}>Primary Role</th>
                    <th style={styles.th}>Actions</th>
                  </tr>
                </thead>

                <tbody>
                  {members.length > 0 ? (
                    members.map((member) => (
                      <tr key={member.id}>
                        {/* Data columns remain the same */}
                        <td style={styles.td}>{`${member.first_name} ${member.last_name}`}</td>
                        <td style={styles.td}>{member.username}</td>
                        <td style={styles.td}>{member.email}</td>
                        <td style={styles.td}>{member.primary_role_name || "N/A"}</td>

                        {/* --- ADD THIS NEW ACTIONS COLUMN --- */}
                        <td style={styles.td}>
                          {/* 
                          This is the conditional rendering logic.
                          The button will ONLY be rendered if the member's primary role is 'Student'.
                          For Professors or other roles, this cell will be empty.
                        */}
                          {member.primary_role_name === "Student" && (
                            <button
                              onClick={() =>
                                handleStartSupervision(member.id, `${member.first_name} ${member.last_name}`.trim())
                              }
                              style={styles.button} // Use a consistent button style
                            >
                              Start Supervision
                            </button>
                          )}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="5" style={{ textAlign: "center", padding: "20px" }}>
                        There are no members in this research group yet.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
          {activeTab === "invitations" && (
            <div>
              <h2>Pending Invitations</h2>
              <p style={styles.description}>
                These are invitations that have been sent but not yet accepted. Click "Copy Link" to get the
                registration URL.
              </p>
              {invitations.length === 0 ? (
                <p style={{ textAlign: "center", color: "#64748b", marginTop: "40px" }}>
                  No pending invitations found.
                </p>
              ) : (
                <table style={styles.table}>
                  <thead>
                    <tr>
                      <th style={styles.th}>Email</th>
                      <th style={styles.th}>Role</th>
                      <th style={styles.th}>Status</th>
                      <th style={styles.th}>Created</th>
                      <th style={styles.th}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invitations.map((invitation) => (
                      <tr key={invitation.id}>
                        <td style={styles.td}>{invitation.invitee_email}</td>
                        <td style={styles.td}>{invitation.intended_role?.name || "N/A"}</td>
                        <td style={styles.td}>
                          <span
                            style={{
                              ...styles.statusBadge,
                              backgroundColor: invitation.status === "PENDING" ? "#fef3c7" : "#fee2e2",
                              color: invitation.status === "PENDING" ? "#92400e" : "#991b1b",
                            }}
                          >
                            {invitation.status}
                          </span>
                        </td>
                        <td style={styles.td}>{new Date(invitation.created_at).toLocaleDateString()}</td>
                        <td style={styles.td}>
                          {invitation.status === "PENDING" && (
                            <button
                              onClick={() => copyInvitationLink(invitation.code)}
                              style={styles.buttonSecondary}
                              title="Copy registration link"
                            >
                              <Copy size={14} /> Copy Link
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          )}
          {activeTab === "invite" && (
            <div>
              <h2>Invite a New Member</h2>
              <p style={styles.description}>
                An invitation with a unique registration code will be created. You must send the code to the user.
              </p>
              <form onSubmit={handleInviteSubmit} style={{ marginTop: "20px", maxWidth: "600px" }}>
                <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                  <input
                    type="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                    style={styles.input}
                  />
                  <select value={selectedRole} onChange={(e) => setSelectedRole(e.target.value)} style={styles.input}>
                    <option value="">Select Role...</option>
                    {Array.isArray(roleTypes)
                      ? roleTypes.map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.name}
                          </option>
                        ))
                      : null}
                  </select>
                  <button type="submit" style={styles.button} disabled={inviteStatus.type === "loading"}>
                    {inviteStatus.type === "loading" ? "Creating..." : "Create Invitation"}
                  </button>
                </div>
              </form>
              {inviteStatus.message && (
                <p style={{ ...styles.statusMessage, color: inviteStatus.type === "error" ? "#dc2626" : "#059669" }}>
                  {inviteStatus.message}
                </p>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// --- STYLES ---
const styles = {
  container: { maxWidth: "1200px", margin: "20px auto", fontFamily: "system-ui, sans-serif" },
  card: { background: "white", padding: "25px", borderRadius: "12px", boxShadow: "0 4px 20px rgba(0,0,0,0.05)" },
  header: { display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" },
  title: { margin: 0, fontSize: "1.8rem" },
  subtitle: { margin: "5px 0 0 0", color: "#64748b", fontWeight: "normal" },
  tabs: { display: "flex", gap: "4px", marginBottom: "20px" },
  tab: {
    padding: "10px 20px",
    border: "none",
    background: "#f1f5f9",
    cursor: "pointer",
    color: "#64748b",
    borderRadius: "8px 8px 0 0",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    fontWeight: "500",
  },
  tabActive: {
    padding: "10px 20px",
    border: "none",
    background: "white",
    cursor: "pointer",
    color: "#0f172a",
    borderRadius: "8px 8px 0 0",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    fontWeight: "600",
    boxShadow: "0 -2px 5px rgba(0,0,0,0.03)",
  },
  description: { color: "#64748b", marginTop: "-10px", marginBottom: "20px" },
  table: { width: "100%", borderCollapse: "collapse", marginTop: "15px" },
  th: {
    textAlign: "left",
    padding: "12px",
    borderBottom: "2px solid #f1f5f9",
    background: "#f8fafc",
    color: "#64748b",
    textTransform: "uppercase",
    fontSize: "0.8rem",
  },
  td: { padding: "12px", borderBottom: "1px solid #f1f5f9" },
  input: { flex: 1, padding: "10px", borderRadius: "6px", border: "1px solid #cbd5e1" },
  button: {
    background: "#3b82f6",
    color: "white",
    border: "none",
    padding: "10px 15px",
    borderRadius: "6px",
    cursor: "pointer",
    fontWeight: "bold",
  },
  buttonSecondary: {
    background: "white",
    color: "#334155",
    border: "1px solid #cbd5e1",
    padding: "8px",
    borderRadius: "6px",
    cursor: "pointer",
  },
  statusMessage: { marginTop: "15px", padding: "10px", borderRadius: "6px", border: "1px solid transparent" },
  statusBadge: {
    padding: "4px 8px",
    borderRadius: "4px",
    fontSize: "0.75rem",
    fontWeight: "500",
    textTransform: "uppercase"
  },
};

export default AdminDashboard;


