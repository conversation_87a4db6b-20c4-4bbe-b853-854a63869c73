import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const AuthRedirect = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>; // Or a spinner
  }

  // Check user roles and redirect accordingly
  const isProfessor = user && (
    user.primary_role === 'Professor' ||
    user.can_supervise
  );

  const isAdmin = user && (
    user.can_invite ||
    user.primary_role === 'Administrator'
  );

  console.log("🔍 DEBUG AuthRedirect: User:", user);
  console.log("🔍 DEBUG AuthRedirect: Is professor?", isProfessor);
  console.log("🔍 DEBUG AuthRedirect: Is admin?", isAdmin);

  if (isProfessor) {
    // If they are a professor, send them to the professor dashboard.
    return <Navigate to="/professor/dashboard" replace />;
  } else if (isAdmin) {
    // If they are an admin, send them to the admin dashboard.
    return <Navigate to="/admin/dashboard" replace />;
  } else {
    // Otherwise, send them to the student dashboard.
    return <Navigate to="/dashboard" replace />;
  }
};

export default AuthRedirect;


