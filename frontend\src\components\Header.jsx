// frontend/src/components/Header.jsx
import React from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const Header = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/login"); // Redirect to login page after logout
  };

  // Check if the user's primary role is 'Professor' (or a more robust check)
  const isAdmin = user && user.primary_role === "Professor";

  return (
    <header style={styles.header}>
      <div style={styles.container}>
        <div style={styles.logo}>
          <NavLink to="/" style={styles.navLink}>
            AcademiaSpace
          </NavLink>
        </div>
        <nav style={styles.nav}>
          <NavLink to="/dashboard" style={styles.navLink}>
            My Dashboard
          </NavLink>
          <NavLink to="/projects" style={styles.navLink}>
            My Projects
          </NavLink>

          {/* --- ROLE-BASED NAVIGATION --- */}
          {user?.primary_role === 'Professor' && (
            <NavLink to="/professor/dashboard" style={styles.navLinkAdmin}>
              Professor Panel
            </NavLink>
          )}
          {isAdmin && (
            <NavLink to="/admin/dashboard" style={styles.navLinkAdmin}>
              Admin Panel
            </NavLink>
          )}
        </nav>
        <div style={styles.userActions}>
          <span>Welcome, {user.username}</span>
          <button onClick={handleLogout} style={styles.logoutButton}>
            Logout
          </button>
        </div>
      </div>
    </header>
  );
};

// Styles for the new header
const styles = {
  header: { background: "white", borderBottom: "1px solid #e2e8f0", padding: "0 20px" },
  container: {
    maxWidth: "1200px",
    margin: "0 auto",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    height: "64px",
  },
  logo: { fontWeight: "bold", fontSize: "1.2rem" },
  nav: { display: "flex", gap: "25px" },
  navLink: { textDecoration: "none", color: "#334155" },
  navLinkAdmin: { textDecoration: "none", color: "#3b82f6", fontWeight: "bold" },
  userActions: { display: "flex", alignItems: "center", gap: "15px" },
  logoutButton: {
    background: "#14b8a6", // Teal background
    color: "white",
    border: "none",
    padding: "5px 10px",
    borderRadius: "6px",
    cursor: "pointer",
    transition: "background-color 0.2s ease",
  },
};

export default Header;



