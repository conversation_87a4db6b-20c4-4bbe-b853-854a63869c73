{"version": 3, "sources": ["../../src/row/index.ts", "../../src/row/table-row.ts"], "sourcesContent": ["export * from './table-row.js'\n", "import '../types.js'\n\nimport { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableRowOptions {\n  /**\n   * The HTML attributes for a table row node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\n/**\n * This extension allows you to create table rows.\n * @see https://www.tiptap.dev/api/nodes/table-row\n */\nexport const TableRow = Node.create<TableRowOptions>({\n  name: 'tableRow',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: '(tableCell | tableHeader)*',\n\n  tableRole: 'row',\n\n  parseHTML() {\n    return [{ tag: 'tr' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['tr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,kBAAsC;AAe/B,IAAM,WAAW,iBAAK,OAAwB;AAAA,EACnD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,EAET,WAAW;AAAA,EAEX,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;AAAA,EACvB;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,UAAM,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EAC/E;AACF,CAAC;", "names": []}