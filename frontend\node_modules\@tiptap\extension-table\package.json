{"name": "@tiptap/extension-table", "description": "table extension for tiptap", "version": "3.4.3", "homepage": "https://tiptap.dev", "keywords": ["tiptap", "tiptap extension"], "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/ueberdosis"}, "type": "module", "exports": {".": {"types": {"import": "./dist/index.d.ts", "require": "./dist/index.d.cts"}, "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./table": {"types": {"import": "./dist/table/index.d.ts", "require": "./dist/table/index.d.cts"}, "import": "./dist/table/index.js", "require": "./dist/table/index.cjs"}, "./cell": {"types": {"import": "./dist/cell/index.d.ts", "require": "./dist/cell/index.d.cts"}, "import": "./dist/cell/index.js", "require": "./dist/cell/index.cjs"}, "./header": {"types": {"import": "./dist/header/index.d.ts", "require": "./dist/header/index.d.cts"}, "import": "./dist/header/index.js", "require": "./dist/header/index.cjs"}, "./kit": {"types": {"import": "./dist/kit/index.d.ts", "require": "./dist/kit/index.d.cts"}, "import": "./dist/kit/index.js", "require": "./dist/kit/index.cjs"}, "./row": {"types": {"import": "./dist/row/index.d.ts", "require": "./dist/row/index.d.cts"}, "import": "./dist/row/index.js", "require": "./dist/row/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["src", "dist"], "devDependencies": {"@tiptap/core": "^3.4.3", "@tiptap/pm": "^3.4.3"}, "peerDependencies": {"@tiptap/core": "^3.4.3", "@tiptap/pm": "^3.4.3"}, "repository": {"type": "git", "url": "https://github.com/ueberdosis/tiptap", "directory": "packages/extension-table"}, "scripts": {"build": "tsup", "lint": "prettier ./src/ --check && eslint --cache --quiet --no-error-on-unmatched-pattern ./src/"}}