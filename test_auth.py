#!/usr/bin/env python3
import requests
import json
from jwt import decode

# Test the authentication endpoint
url = "http://127.0.0.1:8000/api/auth/token/"

# Test data for AcademiaSpace user
test_data = {
    "username": "AcademiaSpace",
    "password": "admin"  # Updated password
}

print("Testing AcademiaSpace login...")
print(f"URL: {url}")
print(f"Data: {test_data}")

try:
    response = requests.post(url, json=test_data)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("Login successful!")
        print(f"Response: {json.dumps(data, indent=2)}")
        
        # Decode the JWT token to see the user info
        if 'access' in data:
            token = data['access']
            try:
                # Decode without verification for testing
                decoded = decode(token, options={"verify_signature": False})
                print("\nDecoded JWT token:")
                print(json.dumps(decoded, indent=2))
                
                # Check the role information
                print(f"\nUser Role Information:")
                print(f"Username: {decoded.get('username')}")
                print(f"Primary Role: {decoded.get('primary_role')}")
                print(f"Can Supervise: {decoded.get('can_supervise')}")
                print(f"Can Invite: {decoded.get('can_invite')}")
                
            except Exception as e:
                print(f"Error decoding token: {e}")
    else:
        print("Login failed!")
        print(f"Response: {response.text}")
        
except Exception as e:
    print(f"Error: {e}")
