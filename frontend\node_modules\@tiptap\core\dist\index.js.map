{"version": 3, "sources": ["../src/helpers/createChainableState.ts", "../src/CommandManager.ts", "../src/Editor.ts", "../src/EventEmitter.ts", "../src/ExtensionManager.ts", "../src/helpers/combineTransactionSteps.ts", "../src/helpers/createNodeFromContent.ts", "../src/utilities/elementFromString.ts", "../src/helpers/createDocument.ts", "../src/helpers/defaultBlockAt.ts", "../src/helpers/findChildren.ts", "../src/helpers/findChildrenInRange.ts", "../src/helpers/findParentNodeClosestToPos.ts", "../src/helpers/findParentNode.ts", "../src/helpers/getExtensionField.ts", "../src/helpers/flattenExtensions.ts", "../src/helpers/generateHTML.ts", "../src/helpers/getHTMLFromFragment.ts", "../src/helpers/getSchemaByResolvedExtensions.ts", "../src/utilities/isFunction.ts", "../src/utilities/callOrReturn.ts", "../src/utilities/isEmptyObject.ts", "../src/helpers/splitExtensions.ts", "../src/helpers/getAttributesFromExtensions.ts", "../src/utilities/mergeAttributes.ts", "../src/helpers/getRenderedAttributes.ts", "../src/utilities/fromString.ts", "../src/helpers/injectExtensionAttributesToParseRule.ts", "../src/utilities/findDuplicates.ts", "../src/helpers/sortExtensions.ts", "../src/helpers/resolveExtensions.ts", "../src/helpers/getSchema.ts", "../src/helpers/generateJSON.ts", "../src/helpers/generateText.ts", "../src/helpers/getTextBetween.ts", "../src/helpers/getText.ts", "../src/helpers/getTextSerializersFromSchema.ts", "../src/helpers/getMarkType.ts", "../src/helpers/getMarkAttributes.ts", "../src/helpers/getNodeType.ts", "../src/helpers/getNodeAttributes.ts", "../src/helpers/getSchemaTypeNameByName.ts", "../src/helpers/getAttributes.ts", "../src/utilities/removeDuplicates.ts", "../src/helpers/getChangedRanges.ts", "../src/helpers/getDebugJSON.ts", "../src/utilities/isRegExp.ts", "../src/utilities/objectIncludes.ts", "../src/helpers/getMarkRange.ts", "../src/helpers/getMarksBetween.ts", "../src/helpers/getNodeAtPosition.ts", "../src/helpers/getSchemaTypeByName.ts", "../src/helpers/getSplittedAttributes.ts", "../src/helpers/getTextContentFromNodes.ts", "../src/helpers/isMarkActive.ts", "../src/helpers/isNodeActive.ts", "../src/helpers/isActive.ts", "../src/helpers/isAtEndOfNode.ts", "../src/helpers/isAtStartOfNode.ts", "../src/helpers/isExtensionRulesEnabled.ts", "../src/helpers/isList.ts", "../src/helpers/isNodeEmpty.ts", "../src/helpers/isNodeSelection.ts", "../src/helpers/isTextSelection.ts", "../src/utilities/minMax.ts", "../src/helpers/posToDOMRect.ts", "../src/helpers/resolveFocusPosition.ts", "../src/helpers/rewriteUnknownContent.ts", "../src/helpers/selectionToInsertionEnd.ts", "../src/InputRule.ts", "../src/utilities/isPlainObject.ts", "../src/utilities/mergeDeep.ts", "../src/Extendable.ts", "../src/Mark.ts", "../src/PasteRule.ts", "../src/utilities/isNumber.ts", "../src/extensions/index.ts", "../src/extensions/clipboardTextSerializer.ts", "../src/Extension.ts", "../src/commands/index.ts", "../src/commands/blur.ts", "../src/commands/clearContent.ts", "../src/commands/clearNodes.ts", "../src/commands/command.ts", "../src/commands/createParagraphNear.ts", "../src/commands/cut.ts", "../src/commands/deleteCurrentNode.ts", "../src/commands/deleteNode.ts", "../src/commands/deleteRange.ts", "../src/commands/deleteSelection.ts", "../src/commands/enter.ts", "../src/commands/exitCode.ts", "../src/commands/extendMarkRange.ts", "../src/commands/first.ts", "../src/utilities/isAndroid.ts", "../src/utilities/isiOS.ts", "../src/commands/focus.ts", "../src/commands/forEach.ts", "../src/commands/insertContent.ts", "../src/commands/insertContentAt.ts", "../src/commands/join.ts", "../src/commands/joinItemBackward.ts", "../src/commands/joinItemForward.ts", "../src/commands/joinTextblockBackward.ts", "../src/commands/joinTextblockForward.ts", "../src/utilities/isMacOS.ts", "../src/commands/keyboardShortcut.ts", "../src/commands/lift.ts", "../src/commands/liftEmptyBlock.ts", "../src/commands/liftListItem.ts", "../src/commands/newlineInCode.ts", "../src/utilities/deleteProps.ts", "../src/commands/resetAttributes.ts", "../src/commands/scrollIntoView.ts", "../src/commands/selectAll.ts", "../src/commands/selectNodeBackward.ts", "../src/commands/selectNodeForward.ts", "../src/commands/selectParentNode.ts", "../src/commands/selectTextblockEnd.ts", "../src/commands/selectTextblockStart.ts", "../src/commands/setContent.ts", "../src/commands/setMark.ts", "../src/commands/setMeta.ts", "../src/commands/setNode.ts", "../src/commands/setNodeSelection.ts", "../src/commands/setTextSelection.ts", "../src/commands/sinkListItem.ts", "../src/commands/splitBlock.ts", "../src/commands/splitListItem.ts", "../src/commands/toggleList.ts", "../src/commands/toggleMark.ts", "../src/commands/toggleNode.ts", "../src/commands/toggleWrap.ts", "../src/commands/undoInputRule.ts", "../src/commands/unsetAllMarks.ts", "../src/commands/unsetMark.ts", "../src/commands/updateAttributes.ts", "../src/commands/wrapIn.ts", "../src/commands/wrapInList.ts", "../src/extensions/commands.ts", "../src/extensions/delete.ts", "../src/extensions/drop.ts", "../src/extensions/editable.ts", "../src/extensions/focusEvents.ts", "../src/extensions/keymap.ts", "../src/extensions/paste.ts", "../src/extensions/tabindex.ts", "../src/NodePos.ts", "../src/style.ts", "../src/utilities/createStyleTag.ts", "../src/inputRules/markInputRule.ts", "../src/inputRules/nodeInputRule.ts", "../src/inputRules/textblockTypeInputRule.ts", "../src/inputRules/textInputRule.ts", "../src/inputRules/wrappingInputRule.ts", "../src/jsx-runtime.ts", "../src/utilities/canInsertNode.ts", "../src/utilities/escapeForRegEx.ts", "../src/utilities/isString.ts", "../src/MarkView.ts", "../src/Node.ts", "../src/NodeView.ts", "../src/pasteRules/markPasteRule.ts", "../src/pasteRules/nodePasteRule.ts", "../src/pasteRules/textPasteRule.ts", "../src/Tracker.ts"], "sourcesContent": ["import type { EditorState, Transaction } from '@tiptap/pm/state'\n\n/**\n * Takes a Transaction & Editor State and turns it into a chainable state object\n * @param config The transaction and state to create the chainable state from\n * @returns A chainable Editor state object\n */\nexport function createChainableState(config: { transaction: Transaction; state: EditorState }): EditorState {\n  const { state, transaction } = config\n  let { selection } = transaction\n  let { doc } = transaction\n  let { storedMarks } = transaction\n\n  return {\n    ...state,\n    apply: state.apply.bind(state),\n    applyTransaction: state.applyTransaction.bind(state),\n    plugins: state.plugins,\n    schema: state.schema,\n    reconfigure: state.reconfigure.bind(state),\n    toJSON: state.toJSON.bind(state),\n    get storedMarks() {\n      return storedMarks\n    },\n    get selection() {\n      return selection\n    },\n    get doc() {\n      return doc\n    },\n    get tr() {\n      selection = transaction.selection\n      doc = transaction.doc\n      storedMarks = transaction.storedMarks\n\n      return transaction\n    },\n  }\n}\n", "import type { EditorState, Transaction } from '@tiptap/pm/state'\n\nimport type { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport type { AnyCommands, CanCommands, ChainedCommands, CommandProps, SingleCommands } from './types.js'\n\nexport class CommandManager {\n  editor: Editor\n\n  rawCommands: AnyCommands\n\n  customState?: EditorState\n\n  constructor(props: { editor: Editor; state?: EditorState }) {\n    this.editor = props.editor\n    this.rawCommands = this.editor.extensionManager.commands\n    this.customState = props.state\n  }\n\n  get hasCustomState(): boolean {\n    return !!this.customState\n  }\n\n  get state(): EditorState {\n    return this.customState || this.editor.state\n  }\n\n  get commands(): SingleCommands {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n    const { tr } = state\n    const props = this.buildProps(tr)\n\n    return Object.fromEntries(\n      Object.entries(rawCommands).map(([name, command]) => {\n        const method = (...args: any[]) => {\n          const callback = command(...args)(props)\n\n          if (!tr.getMeta('preventDispatch') && !this.hasCustomState) {\n            view.dispatch(tr)\n          }\n\n          return callback\n        }\n\n        return [name, method]\n      }),\n    ) as unknown as SingleCommands\n  }\n\n  get chain(): () => ChainedCommands {\n    return () => this.createChain()\n  }\n\n  get can(): () => CanCommands {\n    return () => this.createCan()\n  }\n\n  public createChain(startTr?: Transaction, shouldDispatch = true): ChainedCommands {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n    const callbacks: boolean[] = []\n    const hasStartTransaction = !!startTr\n    const tr = startTr || state.tr\n\n    const run = () => {\n      if (!hasStartTransaction && shouldDispatch && !tr.getMeta('preventDispatch') && !this.hasCustomState) {\n        view.dispatch(tr)\n      }\n\n      return callbacks.every(callback => callback === true)\n    }\n\n    const chain = {\n      ...Object.fromEntries(\n        Object.entries(rawCommands).map(([name, command]) => {\n          const chainedCommand = (...args: never[]) => {\n            const props = this.buildProps(tr, shouldDispatch)\n            const callback = command(...args)(props)\n\n            callbacks.push(callback)\n\n            return chain\n          }\n\n          return [name, chainedCommand]\n        }),\n      ),\n      run,\n    } as unknown as ChainedCommands\n\n    return chain\n  }\n\n  public createCan(startTr?: Transaction): CanCommands {\n    const { rawCommands, state } = this\n    const dispatch = false\n    const tr = startTr || state.tr\n    const props = this.buildProps(tr, dispatch)\n    const formattedCommands = Object.fromEntries(\n      Object.entries(rawCommands).map(([name, command]) => {\n        return [name, (...args: never[]) => command(...args)({ ...props, dispatch: undefined })]\n      }),\n    ) as unknown as SingleCommands\n\n    return {\n      ...formattedCommands,\n      chain: () => this.createChain(tr, dispatch),\n    } as CanCommands\n  }\n\n  public buildProps(tr: Transaction, shouldDispatch = true): CommandProps {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n\n    const props: CommandProps = {\n      tr,\n      editor,\n      view,\n      state: createChainableState({\n        state,\n        transaction: tr,\n      }),\n      dispatch: shouldDispatch ? () => undefined : undefined,\n      chain: () => this.createChain(tr, shouldDispatch),\n      can: () => this.createCan(tr),\n      get commands() {\n        return Object.fromEntries(\n          Object.entries(rawCommands).map(([name, command]) => {\n            return [name, (...args: never[]) => command(...args)(props)]\n          }),\n        ) as unknown as SingleCommands\n      },\n    }\n\n    return props\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-empty-object-type */\nimport type { MarkType, Node as ProseMirrorNode, NodeType, Schema } from '@tiptap/pm/model'\nimport type { Plugin, PluginKey, Transaction } from '@tiptap/pm/state'\nimport { EditorState } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\n\nimport { CommandManager } from './CommandManager.js'\nimport { EventEmitter } from './EventEmitter.js'\nimport { ExtensionManager } from './ExtensionManager.js'\nimport {\n  ClipboardTextSerializer,\n  Commands,\n  Delete,\n  Drop,\n  Editable,\n  FocusEvents,\n  Keymap,\n  Paste,\n  Tabindex,\n} from './extensions/index.js'\nimport { createDocument } from './helpers/createDocument.js'\nimport { getAttributes } from './helpers/getAttributes.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport { getText } from './helpers/getText.js'\nimport { getTextSerializersFromSchema } from './helpers/getTextSerializersFromSchema.js'\nimport { isActive } from './helpers/isActive.js'\nimport { isNodeEmpty } from './helpers/isNodeEmpty.js'\nimport { resolveFocusPosition } from './helpers/resolveFocusPosition.js'\nimport type { Storage } from './index.js'\nimport { NodePos } from './NodePos.js'\nimport { style } from './style.js'\nimport type {\n  CanCommands,\n  ChainedCommands,\n  DocumentType,\n  EditorEvents,\n  EditorOptions,\n  NodeType as TNodeType,\n  SingleCommands,\n  TextSerializer,\n  TextType as TTextType,\n} from './types.js'\nimport { createStyleTag } from './utilities/createStyleTag.js'\nimport { isFunction } from './utilities/isFunction.js'\n\nexport * as extensions from './extensions/index.js'\n\n// @ts-ignore\nexport interface TiptapEditorHTMLElement extends HTMLElement {\n  editor?: Editor\n}\n\nexport class Editor extends EventEmitter<EditorEvents> {\n  private commandManager!: CommandManager\n\n  public extensionManager!: ExtensionManager\n\n  private css: HTMLStyleElement | null = null\n\n  public schema!: Schema\n\n  private editorView: EditorView | null = null\n\n  public isFocused = false\n\n  private editorState!: EditorState\n\n  /**\n   * The editor is considered initialized after the `create` event has been emitted.\n   */\n  public isInitialized = false\n\n  public extensionStorage: Storage = {} as Storage\n\n  /**\n   * A unique ID for this editor instance.\n   */\n  public instanceId = Math.random().toString(36).slice(2, 9)\n\n  public options: EditorOptions = {\n    element: typeof document !== 'undefined' ? document.createElement('div') : null,\n    content: '',\n    injectCSS: true,\n    injectNonce: undefined,\n    extensions: [],\n    autofocus: false,\n    editable: true,\n    editorProps: {},\n    parseOptions: {},\n    coreExtensionOptions: {},\n    enableInputRules: true,\n    enablePasteRules: true,\n    enableCoreExtensions: true,\n    enableContentCheck: false,\n    emitContentError: false,\n    onBeforeCreate: () => null,\n    onCreate: () => null,\n    onMount: () => null,\n    onUnmount: () => null,\n    onUpdate: () => null,\n    onSelectionUpdate: () => null,\n    onTransaction: () => null,\n    onFocus: () => null,\n    onBlur: () => null,\n    onDestroy: () => null,\n    onContentError: ({ error }) => {\n      throw error\n    },\n    onPaste: () => null,\n    onDrop: () => null,\n    onDelete: () => null,\n  }\n\n  constructor(options: Partial<EditorOptions> = {}) {\n    super()\n    this.setOptions(options)\n    this.createExtensionManager()\n    this.createCommandManager()\n    this.createSchema()\n    this.on('beforeCreate', this.options.onBeforeCreate)\n    this.emit('beforeCreate', { editor: this })\n    this.on('mount', this.options.onMount)\n    this.on('unmount', this.options.onUnmount)\n    this.on('contentError', this.options.onContentError)\n    this.on('create', this.options.onCreate)\n    this.on('update', this.options.onUpdate)\n    this.on('selectionUpdate', this.options.onSelectionUpdate)\n    this.on('transaction', this.options.onTransaction)\n    this.on('focus', this.options.onFocus)\n    this.on('blur', this.options.onBlur)\n    this.on('destroy', this.options.onDestroy)\n    this.on('drop', ({ event, slice, moved }) => this.options.onDrop(event, slice, moved))\n    this.on('paste', ({ event, slice }) => this.options.onPaste(event, slice))\n    this.on('delete', this.options.onDelete)\n\n    const initialDoc = this.createDoc()\n    const selection = resolveFocusPosition(initialDoc, this.options.autofocus)\n\n    // Set editor state immediately, so that it's available independently from the view\n    this.editorState = EditorState.create({\n      doc: initialDoc,\n      schema: this.schema,\n      selection: selection || undefined,\n    })\n\n    if (this.options.element) {\n      this.mount(this.options.element)\n    }\n  }\n\n  /**\n   * Attach the editor to the DOM, creating a new editor view.\n   */\n  public mount(el: NonNullable<EditorOptions['element']> & {}) {\n    if (typeof document === 'undefined') {\n      throw new Error(\n        `[tiptap error]: The editor cannot be mounted because there is no 'document' defined in this environment.`,\n      )\n    }\n    this.createView(el)\n    this.emit('mount', { editor: this })\n\n    window.setTimeout(() => {\n      if (this.isDestroyed) {\n        return\n      }\n\n      this.commands.focus(this.options.autofocus)\n      this.emit('create', { editor: this })\n      this.isInitialized = true\n    }, 0)\n  }\n\n  /**\n   * Remove the editor from the DOM, but still allow remounting at a different point in time\n   */\n  public unmount() {\n    if (this.editorView) {\n      // Cleanup our reference to prevent circular references which caused memory leaks\n      // @ts-ignore\n      const dom = this.editorView.dom as TiptapEditorHTMLElement\n\n      if (dom?.editor) {\n        delete dom.editor\n      }\n      this.editorView.destroy()\n    }\n    this.editorView = null\n    this.isInitialized = false\n\n    // Safely remove CSS element with fallback for test environments\n    if (this.css) {\n      try {\n        if (typeof this.css.remove === 'function') {\n          this.css.remove()\n        } else if (this.css.parentNode) {\n          this.css.parentNode.removeChild(this.css)\n        }\n      } catch (error) {\n        // Silently handle any unexpected DOM removal errors in test environments\n        console.warn('Failed to remove CSS element:', error)\n      }\n    }\n    this.css = null\n    this.emit('unmount', { editor: this })\n  }\n\n  /**\n   * Returns the editor storage.\n   */\n  public get storage(): Storage {\n    return this.extensionStorage\n  }\n\n  /**\n   * An object of all registered commands.\n   */\n  public get commands(): SingleCommands {\n    return this.commandManager.commands\n  }\n\n  /**\n   * Create a command chain to call multiple commands at once.\n   */\n  public chain(): ChainedCommands {\n    return this.commandManager.chain()\n  }\n\n  /**\n   * Check if a command or a command chain can be executed. Without executing it.\n   */\n  public can(): CanCommands {\n    return this.commandManager.can()\n  }\n\n  /**\n   * Inject CSS styles.\n   */\n  private injectCSS(): void {\n    if (this.options.injectCSS && typeof document !== 'undefined') {\n      this.css = createStyleTag(style, this.options.injectNonce)\n    }\n  }\n\n  /**\n   * Update editor options.\n   *\n   * @param options A list of options\n   */\n  public setOptions(options: Partial<EditorOptions> = {}): void {\n    this.options = {\n      ...this.options,\n      ...options,\n    }\n\n    if (!this.editorView || !this.state || this.isDestroyed) {\n      return\n    }\n\n    if (this.options.editorProps) {\n      this.view.setProps(this.options.editorProps)\n    }\n\n    this.view.updateState(this.state)\n  }\n\n  /**\n   * Update editable state of the editor.\n   */\n  public setEditable(editable: boolean, emitUpdate = true): void {\n    this.setOptions({ editable })\n\n    if (emitUpdate) {\n      this.emit('update', { editor: this, transaction: this.state.tr, appendedTransactions: [] })\n    }\n  }\n\n  /**\n   * Returns whether the editor is editable.\n   */\n  public get isEditable(): boolean {\n    // since plugins are applied after creating the view\n    // `editable` is always `true` for one tick.\n    // that’s why we also have to check for `options.editable`\n    return this.options.editable && this.view && this.view.editable\n  }\n\n  /**\n   * Returns the editor state.\n   */\n  public get view(): EditorView {\n    if (this.editorView) {\n      return this.editorView\n    }\n\n    return new Proxy(\n      {\n        state: this.editorState,\n        updateState: (state: EditorState): ReturnType<EditorView['updateState']> => {\n          this.editorState = state\n        },\n        dispatch: (tr: Transaction): ReturnType<EditorView['dispatch']> => {\n          this.editorState = this.state.apply(tr)\n        },\n\n        // Stub some commonly accessed properties to prevent errors\n        composing: false,\n        dragging: null,\n        editable: true,\n        isDestroyed: false,\n      } as EditorView,\n      {\n        get: (obj, key) => {\n          // Specifically always return the most recent editorState\n          if (key === 'state') {\n            return this.editorState\n          }\n          if (key in obj) {\n            return Reflect.get(obj, key)\n          }\n\n          // We throw an error here, because we know the view is not available\n          throw new Error(\n            `[tiptap error]: The editor view is not available. Cannot access view['${key as string}']. The editor may not be mounted yet.`,\n          )\n        },\n      },\n    ) as EditorView\n  }\n\n  /**\n   * Returns the editor state.\n   */\n  public get state(): EditorState {\n    if (this.editorView) {\n      this.editorState = this.view.state\n    }\n\n    return this.editorState\n  }\n\n  /**\n   * Register a ProseMirror plugin.\n   *\n   * @param plugin A ProseMirror plugin\n   * @param handlePlugins Control how to merge the plugin into the existing plugins.\n   * @returns The new editor state\n   */\n  public registerPlugin(\n    plugin: Plugin,\n    handlePlugins?: (newPlugin: Plugin, plugins: Plugin[]) => Plugin[],\n  ): EditorState {\n    const plugins = isFunction(handlePlugins)\n      ? handlePlugins(plugin, [...this.state.plugins])\n      : [...this.state.plugins, plugin]\n\n    const state = this.state.reconfigure({ plugins })\n\n    this.view.updateState(state)\n\n    return state\n  }\n\n  /**\n   * Unregister a ProseMirror plugin.\n   *\n   * @param nameOrPluginKeyToRemove The plugins name\n   * @returns The new editor state or undefined if the editor is destroyed\n   */\n  public unregisterPlugin(\n    nameOrPluginKeyToRemove: string | PluginKey | (string | PluginKey)[],\n  ): EditorState | undefined {\n    if (this.isDestroyed) {\n      return undefined\n    }\n\n    const prevPlugins = this.state.plugins\n    let plugins = prevPlugins\n\n    ;([] as (string | PluginKey)[]).concat(nameOrPluginKeyToRemove).forEach(nameOrPluginKey => {\n      // @ts-ignore\n      const name = typeof nameOrPluginKey === 'string' ? `${nameOrPluginKey}$` : nameOrPluginKey.key\n\n      // @ts-ignore\n      plugins = plugins.filter(plugin => !plugin.key.startsWith(name))\n    })\n\n    if (prevPlugins.length === plugins.length) {\n      // No plugin was removed, so we don’t need to update the state\n      return undefined\n    }\n\n    const state = this.state.reconfigure({\n      plugins,\n    })\n\n    this.view.updateState(state)\n\n    return state\n  }\n\n  /**\n   * Creates an extension manager.\n   */\n  private createExtensionManager(): void {\n    const coreExtensions = this.options.enableCoreExtensions\n      ? [\n          Editable,\n          ClipboardTextSerializer.configure({\n            blockSeparator: this.options.coreExtensionOptions?.clipboardTextSerializer?.blockSeparator,\n          }),\n          Commands,\n          FocusEvents,\n          Keymap,\n          Tabindex,\n          Drop,\n          Paste,\n          Delete,\n        ].filter(ext => {\n          if (typeof this.options.enableCoreExtensions === 'object') {\n            return (\n              this.options.enableCoreExtensions[ext.name as keyof typeof this.options.enableCoreExtensions] !== false\n            )\n          }\n          return true\n        })\n      : []\n    const allExtensions = [...coreExtensions, ...this.options.extensions].filter(extension => {\n      return ['extension', 'node', 'mark'].includes(extension?.type)\n    })\n\n    this.extensionManager = new ExtensionManager(allExtensions, this)\n  }\n\n  /**\n   * Creates an command manager.\n   */\n  private createCommandManager(): void {\n    this.commandManager = new CommandManager({\n      editor: this,\n    })\n  }\n\n  /**\n   * Creates a ProseMirror schema.\n   */\n  private createSchema(): void {\n    this.schema = this.extensionManager.schema\n  }\n\n  /**\n   * Creates the initial document.\n   */\n  private createDoc(): ProseMirrorNode {\n    let doc: ProseMirrorNode\n\n    try {\n      doc = createDocument(this.options.content, this.schema, this.options.parseOptions, {\n        errorOnInvalidContent: this.options.enableContentCheck,\n      })\n    } catch (e) {\n      if (\n        !(e instanceof Error) ||\n        !['[tiptap error]: Invalid JSON content', '[tiptap error]: Invalid HTML content'].includes(e.message)\n      ) {\n        // Not the content error we were expecting\n        throw e\n      }\n      this.emit('contentError', {\n        editor: this,\n        error: e as Error,\n        disableCollaboration: () => {\n          if (\n            'collaboration' in this.storage &&\n            typeof this.storage.collaboration === 'object' &&\n            this.storage.collaboration\n          ) {\n            ;(this.storage.collaboration as any).isDisabled = true\n          }\n          // To avoid syncing back invalid content, reinitialize the extensions without the collaboration extension\n          this.options.extensions = this.options.extensions.filter(extension => extension.name !== 'collaboration')\n\n          // Restart the initialization process by recreating the extension manager with the new set of extensions\n          this.createExtensionManager()\n        },\n      })\n\n      // Content is invalid, but attempt to create it anyway, stripping out the invalid parts\n      doc = createDocument(this.options.content, this.schema, this.options.parseOptions, {\n        errorOnInvalidContent: false,\n      })\n    }\n    return doc\n  }\n\n  /**\n   * Creates a ProseMirror view.\n   */\n  private createView(element: NonNullable<EditorOptions['element']> & {}): void {\n    this.editorView = new EditorView(element, {\n      ...this.options.editorProps,\n      attributes: {\n        // add `role=\"textbox\"` to the editor element\n        role: 'textbox',\n        ...this.options.editorProps?.attributes,\n      },\n      dispatchTransaction: this.dispatchTransaction.bind(this),\n      state: this.editorState,\n      markViews: this.extensionManager.markViews,\n      nodeViews: this.extensionManager.nodeViews,\n    })\n\n    // `editor.view` is not yet available at this time.\n    // Therefore we will add all plugins and node views directly afterwards.\n    const newState = this.state.reconfigure({\n      plugins: this.extensionManager.plugins,\n    })\n\n    this.view.updateState(newState)\n\n    this.prependClass()\n    this.injectCSS()\n\n    // Let’s store the editor instance in the DOM element.\n    // So we’ll have access to it for tests.\n    // @ts-ignore\n    const dom = this.view.dom as TiptapEditorHTMLElement\n\n    dom.editor = this\n  }\n\n  /**\n   * Creates all node and mark views.\n   */\n  public createNodeViews(): void {\n    if (this.view.isDestroyed) {\n      return\n    }\n\n    this.view.setProps({\n      markViews: this.extensionManager.markViews,\n      nodeViews: this.extensionManager.nodeViews,\n    })\n  }\n\n  /**\n   * Prepend class name to element.\n   */\n  public prependClass(): void {\n    this.view.dom.className = `tiptap ${this.view.dom.className}`\n  }\n\n  public isCapturingTransaction = false\n\n  private capturedTransaction: Transaction | null = null\n\n  public captureTransaction(fn: () => void) {\n    this.isCapturingTransaction = true\n    fn()\n    this.isCapturingTransaction = false\n\n    const tr = this.capturedTransaction\n\n    this.capturedTransaction = null\n\n    return tr\n  }\n\n  /**\n   * The callback over which to send transactions (state updates) produced by the view.\n   *\n   * @param transaction An editor state transaction\n   */\n  private dispatchTransaction(transaction: Transaction): void {\n    // if the editor / the view of the editor was destroyed\n    // the transaction should not be dispatched as there is no view anymore.\n    if (this.view.isDestroyed) {\n      return\n    }\n\n    if (this.isCapturingTransaction) {\n      if (!this.capturedTransaction) {\n        this.capturedTransaction = transaction\n\n        return\n      }\n\n      transaction.steps.forEach(step => this.capturedTransaction?.step(step))\n\n      return\n    }\n\n    // Apply transaction and get resulting state and transactions\n    const { state, transactions } = this.state.applyTransaction(transaction)\n    const selectionHasChanged = !this.state.selection.eq(state.selection)\n    const rootTrWasApplied = transactions.includes(transaction)\n    const prevState = this.state\n\n    this.emit('beforeTransaction', {\n      editor: this,\n      transaction,\n      nextState: state,\n    })\n\n    // If transaction was filtered out, we can return early\n    if (!rootTrWasApplied) {\n      return\n    }\n\n    this.view.updateState(state)\n\n    // Emit transaction event with appended transactions info\n    this.emit('transaction', {\n      editor: this,\n      transaction,\n      appendedTransactions: transactions.slice(1),\n    })\n\n    if (selectionHasChanged) {\n      this.emit('selectionUpdate', {\n        editor: this,\n        transaction,\n      })\n    }\n\n    // Only emit the latest between focus and blur events\n    const mostRecentFocusTr = transactions.findLast(tr => tr.getMeta('focus') || tr.getMeta('blur'))\n    const focus = mostRecentFocusTr?.getMeta('focus')\n    const blur = mostRecentFocusTr?.getMeta('blur')\n\n    if (focus) {\n      this.emit('focus', {\n        editor: this,\n        event: focus.event,\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        transaction: mostRecentFocusTr!,\n      })\n    }\n\n    if (blur) {\n      this.emit('blur', {\n        editor: this,\n        event: blur.event,\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        transaction: mostRecentFocusTr!,\n      })\n    }\n\n    // Compare states for update event\n    if (\n      transaction.getMeta('preventUpdate') ||\n      !transactions.some(tr => tr.docChanged) ||\n      prevState.doc.eq(state.doc)\n    ) {\n      return\n    }\n\n    this.emit('update', {\n      editor: this,\n      transaction,\n      appendedTransactions: transactions.slice(1),\n    })\n  }\n\n  /**\n   * Get attributes of the currently selected node or mark.\n   */\n  public getAttributes(nameOrType: string | NodeType | MarkType): Record<string, any> {\n    return getAttributes(this.state, nameOrType)\n  }\n\n  /**\n   * Returns if the currently selected node or mark is active.\n   *\n   * @param name Name of the node or mark\n   * @param attributes Attributes of the node or mark\n   */\n  public isActive(name: string, attributes?: {}): boolean\n  public isActive(attributes: {}): boolean\n  public isActive(nameOrAttributes: string, attributesOrUndefined?: {}): boolean {\n    const name = typeof nameOrAttributes === 'string' ? nameOrAttributes : null\n\n    const attributes = typeof nameOrAttributes === 'string' ? attributesOrUndefined : nameOrAttributes\n\n    return isActive(this.state, name, attributes)\n  }\n\n  /**\n   * Get the document as JSON.\n   */\n  public getJSON(): DocumentType<\n    Record<string, any> | undefined,\n    TNodeType<string, undefined | Record<string, any>, any, (TNodeType | TTextType)[]>[]\n  > {\n    return this.state.doc.toJSON()\n  }\n\n  /**\n   * Get the document as HTML.\n   */\n  public getHTML(): string {\n    return getHTMLFromFragment(this.state.doc.content, this.schema)\n  }\n\n  /**\n   * Get the document as text.\n   */\n  public getText(options?: { blockSeparator?: string; textSerializers?: Record<string, TextSerializer> }): string {\n    const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n\n    return getText(this.state.doc, {\n      blockSeparator,\n      textSerializers: {\n        ...getTextSerializersFromSchema(this.schema),\n        ...textSerializers,\n      },\n    })\n  }\n\n  /**\n   * Check if there is no content.\n   */\n  public get isEmpty(): boolean {\n    return isNodeEmpty(this.state.doc)\n  }\n\n  /**\n   * Destroy the editor.\n   */\n  public destroy(): void {\n    this.emit('destroy')\n\n    this.unmount()\n\n    this.removeAllListeners()\n  }\n\n  /**\n   * Check if the editor is already destroyed.\n   */\n  public get isDestroyed(): boolean {\n    return this.editorView?.isDestroyed ?? true\n  }\n\n  public $node(selector: string, attributes?: { [key: string]: any }): NodePos | null {\n    return this.$doc?.querySelector(selector, attributes) || null\n  }\n\n  public $nodes(selector: string, attributes?: { [key: string]: any }): NodePos[] | null {\n    return this.$doc?.querySelectorAll(selector, attributes) || null\n  }\n\n  public $pos(pos: number) {\n    const $pos = this.state.doc.resolve(pos)\n\n    return new NodePos($pos, this)\n  }\n\n  get $doc() {\n    return this.$pos(0)\n  }\n}\n", "type String<PERSON>eyOf<T> = Extract<keyof T, string>\ntype CallbackType<T extends Record<string, any>, EventName extends StringKeyOf<T>> = T[EventName] extends any[]\n  ? T[EventName]\n  : [T[EventName]]\ntype CallbackFunction<T extends Record<string, any>, EventName extends StringKeyOf<T>> = (\n  ...props: CallbackType<T, EventName>\n) => any\n\nexport class EventEmitter<T extends Record<string, any>> {\n  private callbacks: { [key: string]: Array<(...args: any[]) => void> } = {}\n\n  public on<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this {\n    if (!this.callbacks[event]) {\n      this.callbacks[event] = []\n    }\n\n    this.callbacks[event].push(fn)\n\n    return this\n  }\n\n  public emit<EventName extends StringKeyOf<T>>(event: EventName, ...args: CallbackType<T, EventName>): this {\n    const callbacks = this.callbacks[event]\n\n    if (callbacks) {\n      callbacks.forEach(callback => callback.apply(this, args))\n    }\n\n    return this\n  }\n\n  public off<EventName extends StringKeyOf<T>>(event: EventName, fn?: CallbackFunction<T, EventName>): this {\n    const callbacks = this.callbacks[event]\n\n    if (callbacks) {\n      if (fn) {\n        this.callbacks[event] = callbacks.filter(callback => callback !== fn)\n      } else {\n        delete this.callbacks[event]\n      }\n    }\n\n    return this\n  }\n\n  public once<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this {\n    const onceFn = (...args: CallbackType<T, EventName>) => {\n      this.off(event, onceFn)\n      fn.apply(this, args)\n    }\n\n    return this.on(event, onceFn)\n  }\n\n  public removeAllListeners(): void {\n    this.callbacks = {}\n  }\n}\n", "import { keymap } from '@tiptap/pm/keymap'\nimport type { Schema } from '@tiptap/pm/model'\nimport type { Plugin } from '@tiptap/pm/state'\nimport type { MarkViewConstructor, NodeViewConstructor } from '@tiptap/pm/view'\n\nimport type { Editor } from './Editor.js'\nimport {\n  flattenExtensions,\n  getAttributesFromExtensions,\n  getExtensionField,\n  getNodeType,\n  getRenderedAttributes,\n  getSchemaByResolvedExtensions,\n  getSchemaTypeByName,\n  isExtensionRulesEnabled,\n  resolveExtensions,\n  sortExtensions,\n  splitExtensions,\n} from './helpers/index.js'\nimport { type MarkConfig, type NodeConfig, type Storage, getMarkType, updateMarkViewAttributes } from './index.js'\nimport { inputRulesPlugin } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { pasteRulesPlugin } from './PasteRule.js'\nimport type { AnyConfig, Extensions, RawCommands } from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\n\nexport class ExtensionManager {\n  editor: Editor\n\n  schema: Schema\n\n  extensions: Extensions\n\n  splittableMarks: string[] = []\n\n  constructor(extensions: Extensions, editor: Editor) {\n    this.editor = editor\n    this.extensions = resolveExtensions(extensions)\n    this.schema = getSchemaByResolvedExtensions(this.extensions, editor)\n    this.setupExtensions()\n  }\n\n  static resolve = resolveExtensions\n\n  static sort = sortExtensions\n\n  static flatten = flattenExtensions\n\n  /**\n   * Get all commands from the extensions.\n   * @returns An object with all commands where the key is the command name and the value is the command function\n   */\n  get commands(): RawCommands {\n    return this.extensions.reduce((commands, extension) => {\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: this.editor.extensionStorage[extension.name as keyof Storage],\n        editor: this.editor,\n        type: getSchemaTypeByName(extension.name, this.schema),\n      }\n\n      const addCommands = getExtensionField<AnyConfig['addCommands']>(extension, 'addCommands', context)\n\n      if (!addCommands) {\n        return commands\n      }\n\n      return {\n        ...commands,\n        ...addCommands(),\n      }\n    }, {} as RawCommands)\n  }\n\n  /**\n   * Get all registered Prosemirror plugins from the extensions.\n   * @returns An array of Prosemirror plugins\n   */\n  get plugins(): Plugin[] {\n    const { editor } = this\n\n    // With ProseMirror, first plugins within an array are executed first.\n    // In Tiptap, we provide the ability to override plugins,\n    // so it feels more natural to run plugins at the end of an array first.\n    // That’s why we have to reverse the `extensions` array and sort again\n    // based on the `priority` option.\n    const extensions = sortExtensions([...this.extensions].reverse())\n\n    const allPlugins = extensions\n      .map(extension => {\n        const context = {\n          name: extension.name,\n          options: extension.options,\n          storage: this.editor.extensionStorage[extension.name as keyof Storage],\n          editor,\n          type: getSchemaTypeByName(extension.name, this.schema),\n        }\n\n        const plugins: Plugin[] = []\n\n        const addKeyboardShortcuts = getExtensionField<AnyConfig['addKeyboardShortcuts']>(\n          extension,\n          'addKeyboardShortcuts',\n          context,\n        )\n\n        let defaultBindings: Record<string, () => boolean> = {}\n\n        // bind exit handling\n        if (extension.type === 'mark' && getExtensionField<MarkConfig['exitable']>(extension, 'exitable', context)) {\n          defaultBindings.ArrowRight = () => Mark.handleExit({ editor, mark: extension as Mark })\n        }\n\n        if (addKeyboardShortcuts) {\n          const bindings = Object.fromEntries(\n            Object.entries(addKeyboardShortcuts()).map(([shortcut, method]) => {\n              return [shortcut, () => method({ editor })]\n            }),\n          )\n\n          defaultBindings = { ...defaultBindings, ...bindings }\n        }\n\n        const keyMapPlugin = keymap(defaultBindings)\n\n        plugins.push(keyMapPlugin)\n\n        const addInputRules = getExtensionField<AnyConfig['addInputRules']>(extension, 'addInputRules', context)\n\n        if (isExtensionRulesEnabled(extension, editor.options.enableInputRules) && addInputRules) {\n          const rules = addInputRules()\n\n          if (rules && rules.length) {\n            const inputResult = inputRulesPlugin({\n              editor,\n              rules,\n            })\n\n            const inputPlugins = Array.isArray(inputResult) ? inputResult : [inputResult]\n\n            plugins.push(...inputPlugins)\n          }\n        }\n\n        const addPasteRules = getExtensionField<AnyConfig['addPasteRules']>(extension, 'addPasteRules', context)\n\n        if (isExtensionRulesEnabled(extension, editor.options.enablePasteRules) && addPasteRules) {\n          const rules = addPasteRules()\n\n          if (rules && rules.length) {\n            const pasteRules = pasteRulesPlugin({ editor, rules })\n\n            plugins.push(...pasteRules)\n          }\n        }\n\n        const addProseMirrorPlugins = getExtensionField<AnyConfig['addProseMirrorPlugins']>(\n          extension,\n          'addProseMirrorPlugins',\n          context,\n        )\n\n        if (addProseMirrorPlugins) {\n          const proseMirrorPlugins = addProseMirrorPlugins()\n\n          plugins.push(...proseMirrorPlugins)\n        }\n\n        return plugins\n      })\n      .flat()\n\n    return allPlugins\n  }\n\n  /**\n   * Get all attributes from the extensions.\n   * @returns An array of attributes\n   */\n  get attributes() {\n    return getAttributesFromExtensions(this.extensions)\n  }\n\n  /**\n   * Get all node views from the extensions.\n   * @returns An object with all node views where the key is the node name and the value is the node view function\n   */\n  get nodeViews(): Record<string, NodeViewConstructor> {\n    const { editor } = this\n    const { nodeExtensions } = splitExtensions(this.extensions)\n\n    return Object.fromEntries(\n      nodeExtensions\n        .filter(extension => !!getExtensionField(extension, 'addNodeView'))\n        .map(extension => {\n          const extensionAttributes = this.attributes.filter(attribute => attribute.type === extension.name)\n          const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: this.editor.extensionStorage[extension.name as keyof Storage],\n            editor,\n            type: getNodeType(extension.name, this.schema),\n          }\n          const addNodeView = getExtensionField<NodeConfig['addNodeView']>(extension, 'addNodeView', context)\n\n          if (!addNodeView) {\n            return []\n          }\n\n          const nodeview: NodeViewConstructor = (node, view, getPos, decorations, innerDecorations) => {\n            const HTMLAttributes = getRenderedAttributes(node, extensionAttributes)\n\n            return addNodeView()({\n              // pass-through\n              node,\n              view,\n              getPos: getPos as () => number,\n              decorations,\n              innerDecorations,\n              // tiptap-specific\n              editor,\n              extension,\n              HTMLAttributes,\n            })\n          }\n\n          return [extension.name, nodeview]\n        }),\n    )\n  }\n\n  get markViews(): Record<string, MarkViewConstructor> {\n    const { editor } = this\n    const { markExtensions } = splitExtensions(this.extensions)\n\n    return Object.fromEntries(\n      markExtensions\n        .filter(extension => !!getExtensionField(extension, 'addMarkView'))\n        .map(extension => {\n          const extensionAttributes = this.attributes.filter(attribute => attribute.type === extension.name)\n          const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: this.editor.extensionStorage[extension.name as keyof Storage],\n            editor,\n            type: getMarkType(extension.name, this.schema),\n          }\n          const addMarkView = getExtensionField<MarkConfig['addMarkView']>(extension, 'addMarkView', context)\n\n          if (!addMarkView) {\n            return []\n          }\n\n          const markView: MarkViewConstructor = (mark, view, inline) => {\n            const HTMLAttributes = getRenderedAttributes(mark, extensionAttributes)\n\n            return addMarkView()({\n              // pass-through\n              mark,\n              view,\n              inline,\n              // tiptap-specific\n              editor,\n              extension,\n              HTMLAttributes,\n              updateAttributes: (attrs: Record<string, any>) => {\n                updateMarkViewAttributes(mark, editor, attrs)\n              },\n            })\n          }\n\n          return [extension.name, markView]\n        }),\n    )\n  }\n\n  /**\n   * Go through all extensions, create extension storages & setup marks\n   * & bind editor event listener.\n   */\n  private setupExtensions() {\n    const extensions = this.extensions\n    // re-initialize the extension storage object instance\n    this.editor.extensionStorage = Object.fromEntries(\n      extensions.map(extension => [extension.name, extension.storage]),\n    ) as unknown as Storage\n\n    extensions.forEach(extension => {\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: this.editor.extensionStorage[extension.name as keyof Storage],\n        editor: this.editor,\n        type: getSchemaTypeByName(extension.name, this.schema),\n      }\n\n      if (extension.type === 'mark') {\n        const keepOnSplit = callOrReturn(getExtensionField(extension, 'keepOnSplit', context)) ?? true\n\n        if (keepOnSplit) {\n          this.splittableMarks.push(extension.name)\n        }\n      }\n\n      const onBeforeCreate = getExtensionField<AnyConfig['onBeforeCreate']>(extension, 'onBeforeCreate', context)\n      const onCreate = getExtensionField<AnyConfig['onCreate']>(extension, 'onCreate', context)\n      const onUpdate = getExtensionField<AnyConfig['onUpdate']>(extension, 'onUpdate', context)\n      const onSelectionUpdate = getExtensionField<AnyConfig['onSelectionUpdate']>(\n        extension,\n        'onSelectionUpdate',\n        context,\n      )\n      const onTransaction = getExtensionField<AnyConfig['onTransaction']>(extension, 'onTransaction', context)\n      const onFocus = getExtensionField<AnyConfig['onFocus']>(extension, 'onFocus', context)\n      const onBlur = getExtensionField<AnyConfig['onBlur']>(extension, 'onBlur', context)\n      const onDestroy = getExtensionField<AnyConfig['onDestroy']>(extension, 'onDestroy', context)\n\n      if (onBeforeCreate) {\n        this.editor.on('beforeCreate', onBeforeCreate)\n      }\n\n      if (onCreate) {\n        this.editor.on('create', onCreate)\n      }\n\n      if (onUpdate) {\n        this.editor.on('update', onUpdate)\n      }\n\n      if (onSelectionUpdate) {\n        this.editor.on('selectionUpdate', onSelectionUpdate)\n      }\n\n      if (onTransaction) {\n        this.editor.on('transaction', onTransaction)\n      }\n\n      if (onFocus) {\n        this.editor.on('focus', onFocus)\n      }\n\n      if (onBlur) {\n        this.editor.on('blur', onBlur)\n      }\n\n      if (onDestroy) {\n        this.editor.on('destroy', onDestroy)\n      }\n    })\n  }\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport type { Transaction } from '@tiptap/pm/state'\nimport { Transform } from '@tiptap/pm/transform'\n\n/**\n * Returns a new `Transform` based on all steps of the passed transactions.\n * @param oldDoc The Prosemirror node to start from\n * @param transactions The transactions to combine\n * @returns A new `Transform` with all steps of the passed transactions\n */\nexport function combineTransactionSteps(oldDoc: ProseMirrorNode, transactions: Transaction[]): Transform {\n  const transform = new Transform(oldDoc)\n\n  transactions.forEach(transaction => {\n    transaction.steps.forEach(step => {\n      transform.step(step)\n    })\n  })\n\n  return transform\n}\n", "import type { ParseOptions } from '@tiptap/pm/model'\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Fragment, Node as ProseMirrorNode, Schema } from '@tiptap/pm/model'\n\nimport type { Content } from '../types.js'\nimport { elementFromString } from '../utilities/elementFromString.js'\n\nexport type CreateNodeFromContentOptions = {\n  slice?: boolean\n  parseOptions?: ParseOptions\n  errorOnInvalidContent?: boolean\n}\n\n/**\n * Takes a JSON or HTML content and creates a Prosemirror node or fragment from it.\n * @param content The JSON or HTML content to create the node from\n * @param schema The Prosemirror schema to use for the node\n * @param options Options for the parser\n * @returns The created Prosemirror node or fragment\n */\nexport function createNodeFromContent(\n  content: Content | ProseMirrorNode | Fragment,\n  schema: Schema,\n  options?: CreateNodeFromContentOptions,\n): ProseMirrorNode | Fragment {\n  if (content instanceof ProseMirrorNode || content instanceof Fragment) {\n    return content\n  }\n  options = {\n    slice: true,\n    parseOptions: {},\n    ...options,\n  }\n\n  const isJSONContent = typeof content === 'object' && content !== null\n  const isTextContent = typeof content === 'string'\n\n  if (isJSONContent) {\n    try {\n      const isArrayContent = Array.isArray(content) && content.length > 0\n\n      // if the JSON Content is an array of nodes, create a fragment for each node\n      if (isArrayContent) {\n        return Fragment.fromArray(content.map(item => schema.nodeFromJSON(item)))\n      }\n\n      const node = schema.nodeFromJSON(content)\n\n      if (options.errorOnInvalidContent) {\n        node.check()\n      }\n\n      return node\n    } catch (error) {\n      if (options.errorOnInvalidContent) {\n        throw new Error('[tiptap error]: Invalid JSON content', { cause: error as Error })\n      }\n\n      console.warn('[tiptap warn]: Invalid content.', 'Passed value:', content, 'Error:', error)\n\n      return createNodeFromContent('', schema, options)\n    }\n  }\n\n  if (isTextContent) {\n    // Check for invalid content\n    if (options.errorOnInvalidContent) {\n      let hasInvalidContent = false\n      let invalidContent = ''\n\n      // A copy of the current schema with a catch-all node at the end\n      const contentCheckSchema = new Schema({\n        topNode: schema.spec.topNode,\n        marks: schema.spec.marks,\n        // Prosemirror's schemas are executed such that: the last to execute, matches last\n        // This means that we can add a catch-all node at the end of the schema to catch any content that we don't know how to handle\n        nodes: schema.spec.nodes.append({\n          __tiptap__private__unknown__catch__all__node: {\n            content: 'inline*',\n            group: 'block',\n            parseDOM: [\n              {\n                tag: '*',\n                getAttrs: e => {\n                  // If this is ever called, we know that the content has something that we don't know how to handle in the schema\n                  hasInvalidContent = true\n                  // Try to stringify the element for a more helpful error message\n                  invalidContent = typeof e === 'string' ? e : e.outerHTML\n                  return null\n                },\n              },\n            ],\n          },\n        }),\n      })\n\n      if (options.slice) {\n        DOMParser.fromSchema(contentCheckSchema).parseSlice(elementFromString(content), options.parseOptions)\n      } else {\n        DOMParser.fromSchema(contentCheckSchema).parse(elementFromString(content), options.parseOptions)\n      }\n\n      if (options.errorOnInvalidContent && hasInvalidContent) {\n        throw new Error('[tiptap error]: Invalid HTML content', {\n          cause: new Error(`Invalid element found: ${invalidContent}`),\n        })\n      }\n    }\n\n    const parser = DOMParser.fromSchema(schema)\n\n    if (options.slice) {\n      return parser.parseSlice(elementFromString(content), options.parseOptions).content\n    }\n\n    return parser.parse(elementFromString(content), options.parseOptions)\n  }\n\n  return createNodeFromContent('', schema, options)\n}\n", "const removeWhitespaces = (node: HTMLElement) => {\n  const children = node.childNodes\n\n  for (let i = children.length - 1; i >= 0; i -= 1) {\n    const child = children[i]\n\n    if (child.nodeType === 3 && child.nodeValue && /^(\\n\\s\\s|\\n)$/.test(child.nodeValue)) {\n      node.removeChild(child)\n    } else if (child.nodeType === 1) {\n      removeWhitespaces(child as HTMLElement)\n    }\n  }\n\n  return node\n}\n\nexport function elementFromString(value: string): HTMLElement {\n  if (typeof window === 'undefined') {\n    throw new Error('[tiptap error]: there is no window object available, so this function cannot be used')\n  }\n  // add a wrapper to preserve leading and trailing whitespace\n  const wrappedValue = `<body>${value}</body>`\n\n  const html = new window.DOMParser().parseFromString(wrappedValue, 'text/html').body\n\n  return removeWhitespaces(html)\n}\n", "import type { Fragment, Node as ProseMirrorNode, ParseOptions, Schema } from '@tiptap/pm/model'\n\nimport type { Content } from '../types.js'\nimport { createNodeFromContent } from './createNodeFromContent.js'\n\n/**\n * Create a new Prosemirror document node from content.\n * @param content The JSON or HTML content to create the document from\n * @param schema The Prosemirror schema to use for the document\n * @param parseOptions Options for the parser\n * @returns The created Prosemirror document node\n */\nexport function createDocument(\n  content: Content | ProseMirrorNode | Fragment,\n  schema: Schema,\n  parseOptions: ParseOptions = {},\n  options: { errorOnInvalidContent?: boolean } = {},\n): ProseMirrorNode {\n  return createNodeFromContent(content, schema, {\n    slice: false,\n    parseOptions,\n    errorOnInvalidContent: options.errorOnInvalidContent,\n  }) as ProseMirrorNode\n}\n", "import type { ContentMatch, NodeType } from '@tiptap/pm/model'\n\n/**\n * Gets the default block type at a given match\n * @param match The content match to get the default block type from\n * @returns The default block type or null\n */\nexport function defaultBlockAt(match: ContentMatch): NodeType | null {\n  for (let i = 0; i < match.edgeCount; i += 1) {\n    const { type } = match.edge(i)\n\n    if (type.isTextblock && !type.hasRequiredAttrs()) {\n      return type\n    }\n  }\n\n  return null\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport type { NodeWithPos, Predicate } from '../types.js'\n\n/**\n * Find children inside a Prosemirror node that match a predicate.\n * @param node The Prosemirror node to search in\n * @param predicate The predicate to match\n * @returns An array of nodes with their positions\n */\nexport function findChildren(node: ProseMirrorNode, predicate: Predicate): NodeWithPos[] {\n  const nodesWithPos: NodeWithPos[] = []\n\n  node.descendants((child, pos) => {\n    if (predicate(child)) {\n      nodesWithPos.push({\n        node: child,\n        pos,\n      })\n    }\n  })\n\n  return nodesWithPos\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport type { NodeWithPos, Predicate, Range } from '../types.js'\n\n/**\n * Same as `find<PERSON>hildren` but searches only within a `range`.\n * @param node The Prosemirror node to search in\n * @param range The range to search in\n * @param predicate The predicate to match\n * @returns An array of nodes with their positions\n */\nexport function findChildrenInRange(node: ProseMirrorNode, range: Range, predicate: Predicate): NodeWithPos[] {\n  const nodesWithPos: NodeWithPos[] = []\n\n  // if (range.from === range.to) {\n  //   const nodeAt = node.nodeAt(range.from)\n\n  //   if (nodeAt) {\n  //     nodesWithPos.push({\n  //       node: nodeAt,\n  //       pos: range.from,\n  //     })\n  //   }\n  // }\n\n  node.nodesBetween(range.from, range.to, (child, pos) => {\n    if (predicate(child)) {\n      nodesWithPos.push({\n        node: child,\n        pos,\n      })\n    }\n  })\n\n  return nodesWithPos\n}\n", "import type { Node as ProseMirrorNode, ResolvedPos } from '@tiptap/pm/model'\n\nimport type { Predicate } from '../types.js'\n\n/**\n * Finds the closest parent node to a resolved position that matches a predicate.\n * @param $pos The resolved position to search from\n * @param predicate The predicate to match\n * @returns The closest parent node to the resolved position that matches the predicate\n * @example ```js\n * findParentNodeClosestToPos($from, node => node.type.name === 'paragraph')\n * ```\n */\nexport function findParentNodeClosestToPos(\n  $pos: ResolvedPos,\n  predicate: Predicate,\n):\n  | {\n      pos: number\n      start: number\n      depth: number\n      node: ProseMirrorNode\n    }\n  | undefined {\n  for (let i = $pos.depth; i > 0; i -= 1) {\n    const node = $pos.node(i)\n\n    if (predicate(node)) {\n      return {\n        pos: i > 0 ? $pos.before(i) : 0,\n        start: $pos.start(i),\n        depth: i,\n        node,\n      }\n    }\n  }\n}\n", "import type { Selection } from '@tiptap/pm/state'\n\nimport type { Predicate } from '../types.js'\nimport { findParentNodeClosestToPos } from './findParentNodeClosestToPos.js'\n\n/**\n * Finds the closest parent node to the current selection that matches a predicate.\n * @param predicate The predicate to match\n * @returns A command that finds the closest parent node to the current selection that matches the predicate\n * @example ```js\n * findParentNode(node => node.type.name === 'paragraph')\n * ```\n */\nexport function findParentNode(\n  predicate: Predicate,\n): (selection: Selection) => ReturnType<typeof findParentNodeClosestToPos> {\n  return (selection: Selection) => findParentNodeClosestToPos(selection.$from, predicate)\n}\n", "import type { ExtensionConfig } from '../Extension.js'\nimport type { MarkConfig } from '../Mark.js'\nimport type { NodeConfig } from '../Node.js'\nimport type { AnyExtension, MaybeThisParameterType, RemoveThis } from '../types.js'\n\n/**\n * Returns a field from an extension\n * @param extension The Tiptap extension\n * @param field The field, for example `renderHTML` or `priority`\n * @param context The context object that should be passed as `this` into the function\n * @returns The field value\n */\nexport function getExtensionField<T = any, E extends AnyExtension = any>(\n  extension: E,\n  field: keyof ExtensionConfig | keyof MarkConfig | keyof NodeConfig,\n  context?: Omit<MaybeThisParameterType<T>, 'parent'>,\n): RemoveThis<T> {\n  if (extension.config[field as keyof typeof extension.config] === undefined && extension.parent) {\n    return getExtensionField(extension.parent, field, context)\n  }\n\n  if (typeof extension.config[field as keyof typeof extension.config] === 'function') {\n    const value = (extension.config[field as keyof typeof extension.config] as any).bind({\n      ...context,\n      parent: extension.parent ? getExtensionField(extension.parent, field, context) : null,\n    })\n\n    return value\n  }\n\n  return extension.config[field as keyof typeof extension.config] as RemoveThis<T>\n}\n", "import type { AnyConfig, Extensions } from '../types.js'\nimport { getExtensionField } from './getExtensionField.js'\n\n/**\n * Create a flattened array of extensions by traversing the `addExtensions` field.\n * @param extensions An array of Tiptap extensions\n * @returns A flattened array of Tiptap extensions\n */\nexport function flattenExtensions(extensions: Extensions): Extensions {\n  return (\n    extensions\n      .map(extension => {\n        const context = {\n          name: extension.name,\n          options: extension.options,\n          storage: extension.storage,\n        }\n\n        const addExtensions = getExtensionField<AnyConfig['addExtensions']>(extension, 'addExtensions', context)\n\n        if (addExtensions) {\n          return [extension, ...flattenExtensions(addExtensions())]\n        }\n\n        return extension\n      })\n      // `Infinity` will break TypeScript so we set a number that is probably high enough\n      .flat(10)\n  )\n}\n", "import { Node } from '@tiptap/pm/model'\n\nimport type { Extensions, JSONContent } from '../types.js'\nimport { getHTMLFromFragment } from './getHTMLFromFragment.js'\nimport { getSchema } from './getSchema.js'\n\n/**\n * Generate HTML from a JSONContent\n * @param doc The JSONContent to generate HTML from\n * @param extensions The extensions to use for the schema\n * @returns The generated HTML\n */\nexport function generateHTML(doc: JSONContent, extensions: Extensions): string {\n  const schema = getSchema(extensions)\n  const contentNode = Node.fromJSON(schema, doc)\n\n  return getHTMLFromFragment(contentNode.content, schema)\n}\n", "import type { Fragment, Schema } from '@tiptap/pm/model'\nimport { DOMSerializer } from '@tiptap/pm/model'\n\nexport function getHTMLFromFragment(fragment: Fragment, schema: Schema): string {\n  const documentFragment = DOMSerializer.fromSchema(schema).serializeFragment(fragment)\n\n  const temporaryDocument = document.implementation.createHTMLDocument()\n  const container = temporaryDocument.createElement('div')\n\n  container.appendChild(documentFragment)\n\n  return container.innerHTML\n}\n", "import type { MarkS<PERSON>, NodeSpec, TagParseRule } from '@tiptap/pm/model'\nimport { Schema } from '@tiptap/pm/model'\n\nimport type { Editor, MarkConfig, NodeConfig } from '../index.js'\nimport type { AnyConfig, Extensions } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\nimport { isEmptyObject } from '../utilities/isEmptyObject.js'\nimport { getAttributesFromExtensions } from './getAttributesFromExtensions.js'\nimport { getExtensionField } from './getExtensionField.js'\nimport { getRenderedAttributes } from './getRenderedAttributes.js'\nimport { injectExtensionAttributesToParseRule } from './injectExtensionAttributesToParseRule.js'\nimport { splitExtensions } from './splitExtensions.js'\n\nfunction cleanUpSchemaItem<T>(data: T) {\n  return Object.fromEntries(\n    // @ts-ignore\n    Object.entries(data).filter(([key, value]) => {\n      if (key === 'attrs' && isEmptyObject(value as object | undefined)) {\n        return false\n      }\n\n      return value !== null && value !== undefined\n    }),\n  ) as T\n}\n\n/**\n * Creates a new Prosemirror schema based on the given extensions.\n * @param extensions An array of Tiptap extensions\n * @param editor The editor instance\n * @returns A Prosemirror schema\n */\nexport function getSchemaByResolvedExtensions(extensions: Extensions, editor?: Editor): Schema {\n  const allAttributes = getAttributesFromExtensions(extensions)\n  const { nodeExtensions, markExtensions } = splitExtensions(extensions)\n  const topNode = nodeExtensions.find(extension => getExtensionField(extension, 'topNode'))?.name\n\n  const nodes = Object.fromEntries(\n    nodeExtensions.map(extension => {\n      const extensionAttributes = allAttributes.filter(attribute => attribute.type === extension.name)\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor,\n      }\n\n      const extraNodeFields = extensions.reduce((fields, e) => {\n        const extendNodeSchema = getExtensionField<AnyConfig['extendNodeSchema']>(e, 'extendNodeSchema', context)\n\n        return {\n          ...fields,\n          ...(extendNodeSchema ? extendNodeSchema(extension) : {}),\n        }\n      }, {})\n\n      const schema: NodeSpec = cleanUpSchemaItem({\n        ...extraNodeFields,\n        content: callOrReturn(getExtensionField<NodeConfig['content']>(extension, 'content', context)),\n        marks: callOrReturn(getExtensionField<NodeConfig['marks']>(extension, 'marks', context)),\n        group: callOrReturn(getExtensionField<NodeConfig['group']>(extension, 'group', context)),\n        inline: callOrReturn(getExtensionField<NodeConfig['inline']>(extension, 'inline', context)),\n        atom: callOrReturn(getExtensionField<NodeConfig['atom']>(extension, 'atom', context)),\n        selectable: callOrReturn(getExtensionField<NodeConfig['selectable']>(extension, 'selectable', context)),\n        draggable: callOrReturn(getExtensionField<NodeConfig['draggable']>(extension, 'draggable', context)),\n        code: callOrReturn(getExtensionField<NodeConfig['code']>(extension, 'code', context)),\n        whitespace: callOrReturn(getExtensionField<NodeConfig['whitespace']>(extension, 'whitespace', context)),\n        linebreakReplacement: callOrReturn(\n          getExtensionField<NodeConfig['linebreakReplacement']>(extension, 'linebreakReplacement', context),\n        ),\n        defining: callOrReturn(getExtensionField<NodeConfig['defining']>(extension, 'defining', context)),\n        isolating: callOrReturn(getExtensionField<NodeConfig['isolating']>(extension, 'isolating', context)),\n        attrs: Object.fromEntries(\n          extensionAttributes.map(extensionAttribute => {\n            return [\n              extensionAttribute.name,\n              { default: extensionAttribute?.attribute?.default, validate: extensionAttribute?.attribute?.validate },\n            ]\n          }),\n        ),\n      })\n\n      const parseHTML = callOrReturn(getExtensionField<NodeConfig['parseHTML']>(extension, 'parseHTML', context))\n\n      if (parseHTML) {\n        schema.parseDOM = parseHTML.map(parseRule =>\n          injectExtensionAttributesToParseRule(parseRule, extensionAttributes),\n        ) as TagParseRule[]\n      }\n\n      const renderHTML = getExtensionField<NodeConfig['renderHTML']>(extension, 'renderHTML', context)\n\n      if (renderHTML) {\n        schema.toDOM = node =>\n          renderHTML({\n            node,\n            HTMLAttributes: getRenderedAttributes(node, extensionAttributes),\n          })\n      }\n\n      const renderText = getExtensionField<NodeConfig['renderText']>(extension, 'renderText', context)\n\n      if (renderText) {\n        schema.toText = renderText\n      }\n\n      return [extension.name, schema]\n    }),\n  )\n\n  const marks = Object.fromEntries(\n    markExtensions.map(extension => {\n      const extensionAttributes = allAttributes.filter(attribute => attribute.type === extension.name)\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor,\n      }\n\n      const extraMarkFields = extensions.reduce((fields, e) => {\n        const extendMarkSchema = getExtensionField<AnyConfig['extendMarkSchema']>(e, 'extendMarkSchema', context)\n\n        return {\n          ...fields,\n          ...(extendMarkSchema ? extendMarkSchema(extension as any) : {}),\n        }\n      }, {})\n\n      const schema: MarkSpec = cleanUpSchemaItem({\n        ...extraMarkFields,\n        inclusive: callOrReturn(getExtensionField<MarkConfig['inclusive']>(extension, 'inclusive', context)),\n        excludes: callOrReturn(getExtensionField<MarkConfig['excludes']>(extension, 'excludes', context)),\n        group: callOrReturn(getExtensionField<MarkConfig['group']>(extension, 'group', context)),\n        spanning: callOrReturn(getExtensionField<MarkConfig['spanning']>(extension, 'spanning', context)),\n        code: callOrReturn(getExtensionField<MarkConfig['code']>(extension, 'code', context)),\n        attrs: Object.fromEntries(\n          extensionAttributes.map(extensionAttribute => {\n            return [\n              extensionAttribute.name,\n              { default: extensionAttribute?.attribute?.default, validate: extensionAttribute?.attribute?.validate },\n            ]\n          }),\n        ),\n      })\n\n      const parseHTML = callOrReturn(getExtensionField<MarkConfig['parseHTML']>(extension, 'parseHTML', context))\n\n      if (parseHTML) {\n        schema.parseDOM = parseHTML.map(parseRule =>\n          injectExtensionAttributesToParseRule(parseRule, extensionAttributes),\n        )\n      }\n\n      const renderHTML = getExtensionField<MarkConfig['renderHTML']>(extension, 'renderHTML', context)\n\n      if (renderHTML) {\n        schema.toDOM = mark =>\n          renderHTML({\n            mark,\n            HTMLAttributes: getRenderedAttributes(mark, extensionAttributes),\n          })\n      }\n\n      return [extension.name, schema]\n    }),\n  )\n\n  return new Schema({\n    topNode,\n    nodes,\n    marks,\n  })\n}\n", "// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport function isFunction(value: any): value is Function {\n  return typeof value === 'function'\n}\n", "import type { MaybeReturnType } from '../types.js'\nimport { isFunction } from './isFunction.js'\n\n/**\n * Optionally calls `value` as a function.\n * Otherwise it is returned directly.\n * @param value Function or any value.\n * @param context Optional context to bind to function.\n * @param props Optional props to pass to function.\n */\nexport function callOrReturn<T>(value: T, context: any = undefined, ...props: any[]): MaybeReturnType<T> {\n  if (isFunction(value)) {\n    if (context) {\n      return value.bind(context)(...props)\n    }\n\n    return value(...props)\n  }\n\n  return value as MaybeReturnType<T>\n}\n", "export function isEmptyObject(value = {}): boolean {\n  return Object.keys(value).length === 0 && value.constructor === Object\n}\n", "import type { Extension } from '../Extension.js'\nimport type { <PERSON> } from '../Mark.js'\nimport type { Node } from '../Node.js'\nimport type { Extensions } from '../types.js'\n\nexport function splitExtensions(extensions: Extensions) {\n  const baseExtensions = extensions.filter(extension => extension.type === 'extension') as Extension[]\n  const nodeExtensions = extensions.filter(extension => extension.type === 'node') as Node[]\n  const markExtensions = extensions.filter(extension => extension.type === 'mark') as Mark[]\n\n  return {\n    baseExtensions,\n    nodeExtensions,\n    markExtensions,\n  }\n}\n", "import type { MarkConfig, NodeConfig } from '../index.js'\nimport type { AnyConfig, Attribute, Attributes, ExtensionAttribute, Extensions } from '../types.js'\nimport { getExtensionField } from './getExtensionField.js'\nimport { splitExtensions } from './splitExtensions.js'\n\n/**\n * Get a list of all extension attributes defined in `addAttribute` and `addGlobalAttribute`.\n * @param extensions List of extensions\n */\nexport function getAttributesFromExtensions(extensions: Extensions): ExtensionAttribute[] {\n  const extensionAttributes: ExtensionAttribute[] = []\n  const { nodeExtensions, markExtensions } = splitExtensions(extensions)\n  const nodeAndMarkExtensions = [...nodeExtensions, ...markExtensions]\n  const defaultAttribute: Required<Omit<Attribute, 'validate'>> & Pick<Attribute, 'validate'> = {\n    default: null,\n    validate: undefined,\n    rendered: true,\n    renderHTML: null,\n    parseHTML: null,\n    keepOnSplit: true,\n    isRequired: false,\n  }\n\n  extensions.forEach(extension => {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n      extensions: nodeAndMarkExtensions,\n    }\n\n    const addGlobalAttributes = getExtensionField<AnyConfig['addGlobalAttributes']>(\n      extension,\n      'addGlobalAttributes',\n      context,\n    )\n\n    if (!addGlobalAttributes) {\n      return\n    }\n\n    const globalAttributes = addGlobalAttributes()\n\n    globalAttributes.forEach(globalAttribute => {\n      globalAttribute.types.forEach(type => {\n        Object.entries(globalAttribute.attributes).forEach(([name, attribute]) => {\n          extensionAttributes.push({\n            type,\n            name,\n            attribute: {\n              ...defaultAttribute,\n              ...attribute,\n            },\n          })\n        })\n      })\n    })\n  })\n\n  nodeAndMarkExtensions.forEach(extension => {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    const addAttributes = getExtensionField<NodeConfig['addAttributes'] | MarkConfig['addAttributes']>(\n      extension,\n      'addAttributes',\n      context,\n    )\n\n    if (!addAttributes) {\n      return\n    }\n\n    // TODO: remove `as Attributes`\n    const attributes = addAttributes() as Attributes\n\n    Object.entries(attributes).forEach(([name, attribute]) => {\n      const mergedAttr = {\n        ...defaultAttribute,\n        ...attribute,\n      }\n\n      if (typeof mergedAttr?.default === 'function') {\n        mergedAttr.default = mergedAttr.default()\n      }\n\n      if (mergedAttr?.isRequired && mergedAttr?.default === undefined) {\n        delete mergedAttr.default\n      }\n\n      extensionAttributes.push({\n        type: extension.name,\n        name,\n        attribute: mergedAttr,\n      })\n    })\n  })\n\n  return extensionAttributes\n}\n", "export function mergeAttributes(...objects: Record<string, any>[]): Record<string, any> {\n  return objects\n    .filter(item => !!item)\n    .reduce((items, item) => {\n      const mergedAttributes = { ...items }\n\n      Object.entries(item).forEach(([key, value]) => {\n        const exists = mergedAttributes[key]\n\n        if (!exists) {\n          mergedAttributes[key] = value\n\n          return\n        }\n\n        if (key === 'class') {\n          const valueClasses: string[] = value ? String(value).split(' ') : []\n          const existingClasses: string[] = mergedAttributes[key] ? mergedAttributes[key].split(' ') : []\n\n          const insertClasses = valueClasses.filter(valueClass => !existingClasses.includes(valueClass))\n\n          mergedAttributes[key] = [...existingClasses, ...insertClasses].join(' ')\n        } else if (key === 'style') {\n          const newStyles: string[] = value\n            ? value\n                .split(';')\n                .map((style: string) => style.trim())\n                .filter(Boolean)\n            : []\n          const existingStyles: string[] = mergedAttributes[key]\n            ? mergedAttributes[key]\n                .split(';')\n                .map((style: string) => style.trim())\n                .filter(Boolean)\n            : []\n\n          const styleMap = new Map<string, string>()\n\n          existingStyles.forEach(style => {\n            const [property, val] = style.split(':').map(part => part.trim())\n\n            styleMap.set(property, val)\n          })\n\n          newStyles.forEach(style => {\n            const [property, val] = style.split(':').map(part => part.trim())\n\n            styleMap.set(property, val)\n          })\n\n          mergedAttributes[key] = Array.from(styleMap.entries())\n            .map(([property, val]) => `${property}: ${val}`)\n            .join('; ')\n        } else {\n          mergedAttributes[key] = value\n        }\n      })\n\n      return mergedAttributes\n    }, {})\n}\n", "import type { Mark, Node } from '@tiptap/pm/model'\n\nimport type { ExtensionAttribute } from '../types.js'\nimport { mergeAttributes } from '../utilities/mergeAttributes.js'\n\nexport function getRenderedAttributes(\n  nodeOrMark: Node | Mark,\n  extensionAttributes: ExtensionAttribute[],\n): Record<string, any> {\n  return extensionAttributes\n    .filter(attribute => attribute.type === nodeOrMark.type.name)\n    .filter(item => item.attribute.rendered)\n    .map(item => {\n      if (!item.attribute.renderHTML) {\n        return {\n          [item.name]: nodeOrMark.attrs[item.name],\n        }\n      }\n\n      return item.attribute.renderHTML(nodeOrMark.attrs) || {}\n    })\n    .reduce((attributes, attribute) => mergeAttributes(attributes, attribute), {})\n}\n", "export function fromString(value: any): any {\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  if (value.match(/^[+-]?(?:\\d*\\.)?\\d+$/)) {\n    return Number(value)\n  }\n\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  return value\n}\n", "import type { ParseRule } from '@tiptap/pm/model'\n\nimport type { ExtensionAttribute } from '../types.js'\nimport { fromString } from '../utilities/fromString.js'\n\n/**\n * This function merges extension attributes into parserule attributes (`attrs` or `getAttrs`).\n * Cancels when `getAttrs` returned `false`.\n * @param parseRule ProseMirror ParseRule\n * @param extensionAttributes List of attributes to inject\n */\nexport function injectExtensionAttributesToParseRule(\n  parseRule: ParseRule,\n  extensionAttributes: ExtensionAttribute[],\n): ParseRule {\n  if ('style' in parseRule) {\n    return parseRule\n  }\n\n  return {\n    ...parseRule,\n    getAttrs: (node: HTMLElement) => {\n      const oldAttributes = parseRule.getAttrs ? parseRule.getAttrs(node) : parseRule.attrs\n\n      if (oldAttributes === false) {\n        return false\n      }\n\n      const newAttributes = extensionAttributes.reduce((items, item) => {\n        const value = item.attribute.parseHTML\n          ? item.attribute.parseHTML(node)\n          : fromString(node.getAttribute(item.name))\n\n        if (value === null || value === undefined) {\n          return items\n        }\n\n        return {\n          ...items,\n          [item.name]: value,\n        }\n      }, {})\n\n      return { ...oldAttributes, ...newAttributes }\n    },\n  }\n}\n", "/**\n * Find duplicates in an array.\n */\nexport function findDuplicates<T>(items: T[]): T[] {\n  const filtered = items.filter((el, index) => items.indexOf(el) !== index)\n\n  return Array.from(new Set(filtered))\n}\n", "import type { AnyConfig, Extensions } from '../types.js'\nimport { getExtensionField } from './getExtensionField.js'\n\n/**\n * Sort extensions by priority.\n * @param extensions An array of Tiptap extensions\n * @returns A sorted array of Tiptap extensions by priority\n */\nexport function sortExtensions(extensions: Extensions): Extensions {\n  const defaultPriority = 100\n\n  return extensions.sort((a, b) => {\n    const priorityA = getExtensionField<AnyConfig['priority']>(a, 'priority') || defaultPriority\n    const priorityB = getExtensionField<AnyConfig['priority']>(b, 'priority') || defaultPriority\n\n    if (priorityA > priorityB) {\n      return -1\n    }\n\n    if (priorityA < priorityB) {\n      return 1\n    }\n\n    return 0\n  })\n}\n", "import type { Extensions } from '../types.js'\nimport { findDuplicates } from '../utilities/findDuplicates.js'\nimport { flattenExtensions } from './flattenExtensions.js'\nimport { sortExtensions } from './sortExtensions.js'\n\n/**\n * Returns a flattened and sorted extension list while\n * also checking for duplicated extensions and warns the user.\n * @param extensions An array of Tiptap extensions\n * @returns An flattened and sorted array of Tiptap extensions\n */\nexport function resolveExtensions(extensions: Extensions): Extensions {\n  const resolvedExtensions = sortExtensions(flattenExtensions(extensions))\n  const duplicatedNames = findDuplicates(resolvedExtensions.map(extension => extension.name))\n\n  if (duplicatedNames.length) {\n    console.warn(\n      `[tiptap warn]: Duplicate extension names found: [${duplicatedNames\n        .map(item => `'${item}'`)\n        .join(', ')}]. This can lead to issues.`,\n    )\n  }\n\n  return resolvedExtensions\n}\n", "import type { Schema } from '@tiptap/pm/model'\n\nimport type { Editor } from '../Editor.js'\nimport type { Extensions } from '../types.js'\nimport { getSchemaByResolvedExtensions } from './getSchemaByResolvedExtensions.js'\nimport { resolveExtensions } from './resolveExtensions.js'\n\nexport function getSchema(extensions: Extensions, editor?: Editor): Schema {\n  const resolvedExtensions = resolveExtensions(extensions)\n\n  return getSchemaByResolvedExtensions(resolvedExtensions, editor)\n}\n", "import { DOM<PERSON>arser } from '@tiptap/pm/model'\n\nimport type { Extensions } from '../types.js'\nimport { elementFromString } from '../utilities/elementFromString.js'\nimport { getSchema } from './getSchema.js'\n\n/**\n * Generate JSONContent from HTML\n * @param html The HTML to generate J<PERSON><PERSON>ontent from\n * @param extensions The extensions to use for the schema\n * @returns The generated JSONContent\n */\nexport function generateJSON(html: string, extensions: Extensions): Record<string, any> {\n  const schema = getSchema(extensions)\n  const dom = elementFromString(html)\n\n  return DOMParser.fromSchema(schema).parse(dom).toJSON()\n}\n", "import { Node } from '@tiptap/pm/model'\n\nimport type { Extensions, JSONContent, TextSerializer } from '../types.js'\nimport { getSchema } from './getSchema.js'\nimport { getText } from './getText.js'\nimport { getTextSerializersFromSchema } from './getTextSerializersFromSchema.js'\n\n/**\n * Generate raw text from a JSONContent\n * @param doc The JSONContent to generate text from\n * @param extensions The extensions to use for the schema\n * @param options Options for the text generation f.e. blockSeparator or textSerializers\n * @returns The generated text\n */\nexport function generateText(\n  doc: JSONContent,\n  extensions: Extensions,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n): string {\n  const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n  const schema = getSchema(extensions)\n  const contentNode = Node.fromJSON(schema, doc)\n\n  return getText(contentNode, {\n    blockSeparator,\n    textSerializers: {\n      ...getTextSerializersFromSchema(schema),\n      ...textSerializers,\n    },\n  })\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport type { Range, TextSerializer } from '../types.js'\n\n/**\n * Gets the text between two positions in a Prosemirror node\n * and serializes it using the given text serializers and block separator (see getText)\n * @param startNode The Prosemirror node to start from\n * @param range The range of the text to get\n * @param options Options for the text serializer & block separator\n * @returns The text between the two positions\n */\nexport function getTextBetween(\n  startNode: ProseMirrorNode,\n  range: Range,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n): string {\n  const { from, to } = range\n  const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n  let text = ''\n\n  startNode.nodesBetween(from, to, (node, pos, parent, index) => {\n    if (node.isBlock && pos > from) {\n      text += blockSeparator\n    }\n\n    const textSerializer = textSerializers?.[node.type.name]\n\n    if (textSerializer) {\n      if (parent) {\n        text += textSerializer({\n          node,\n          pos,\n          parent,\n          index,\n          range,\n        })\n      }\n      // do not descend into child nodes when there exists a serializer\n      return false\n    }\n\n    if (node.isText) {\n      text += node?.text?.slice(Math.max(from, pos) - pos, to - pos) // eslint-disable-line\n    }\n  })\n\n  return text\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport type { TextSerializer } from '../types.js'\nimport { getTextBetween } from './getTextBetween.js'\n\n/**\n * Gets the text of a Prosemirror node\n * @param node The Prosemirror node\n * @param options Options for the text serializer & block separator\n * @returns The text of the node\n * @example ```js\n * const text = getText(node, { blockSeparator: '\\n' })\n * ```\n */\nexport function getText(\n  node: ProseMirrorNode,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n) {\n  const range = {\n    from: 0,\n    to: node.content.size,\n  }\n\n  return getTextBetween(node, range, options)\n}\n", "import type { Schema } from '@tiptap/pm/model'\n\nimport type { TextSerializer } from '../types.js'\n\n/**\n * Find text serializers `toText` in a Prosemirror schema\n * @param schema The Prosemirror schema to search in\n * @returns A record of text serializers by node name\n */\nexport function getTextSerializersFromSchema(schema: Schema): Record<string, TextSerializer> {\n  return Object.fromEntries(\n    Object.entries(schema.nodes)\n      .filter(([, node]) => node.spec.toText)\n      .map(([name, node]) => [name, node.spec.toText]),\n  )\n}\n", "import type { MarkType, Schema } from '@tiptap/pm/model'\n\nexport function getMarkType(nameOrType: string | MarkType, schema: Schema): MarkType {\n  if (typeof nameOrType === 'string') {\n    if (!schema.marks[nameOrType]) {\n      throw Error(`There is no mark type named '${nameOrType}'. Maybe you forgot to add the extension?`)\n    }\n\n    return schema.marks[nameOrType]\n  }\n\n  return nameOrType\n}\n", "import type { Mark, MarkType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nimport { getMarkType } from './getMarkType.js'\n\nexport function getMarkAttributes(state: EditorState, typeOrName: string | MarkType): Record<string, any> {\n  const type = getMarkType(typeOrName, state.schema)\n  const { from, to, empty } = state.selection\n  const marks: Mark[] = []\n\n  if (empty) {\n    if (state.storedMarks) {\n      marks.push(...state.storedMarks)\n    }\n\n    marks.push(...state.selection.$head.marks())\n  } else {\n    state.doc.nodesBetween(from, to, node => {\n      marks.push(...node.marks)\n    })\n  }\n\n  const mark = marks.find(markItem => markItem.type.name === type.name)\n\n  if (!mark) {\n    return {}\n  }\n\n  return { ...mark.attrs }\n}\n", "import type { NodeType, Schema } from '@tiptap/pm/model'\n\nexport function getNodeType(nameOrType: string | NodeType, schema: Schema): NodeType {\n  if (typeof nameOrType === 'string') {\n    if (!schema.nodes[nameOrType]) {\n      throw Error(`There is no node type named '${nameOrType}'. Maybe you forgot to add the extension?`)\n    }\n\n    return schema.nodes[nameOrType]\n  }\n\n  return nameOrType\n}\n", "import type { Node, NodeType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nimport { getNodeType } from './getNodeType.js'\n\nexport function getNodeAttributes(state: EditorState, typeOrName: string | NodeType): Record<string, any> {\n  const type = getNodeType(typeOrName, state.schema)\n  const { from, to } = state.selection\n  const nodes: Node[] = []\n\n  state.doc.nodesBetween(from, to, node => {\n    nodes.push(node)\n  })\n\n  const node = nodes.reverse().find(nodeItem => nodeItem.type.name === type.name)\n\n  if (!node) {\n    return {}\n  }\n\n  return { ...node.attrs }\n}\n", "import type { Schema } from '@tiptap/pm/model'\n\n/**\n * Get the type of a schema item by its name.\n * @param name The name of the schema item\n * @param schema The Prosemiror schema to search in\n * @returns The type of the schema item (`node` or `mark`), or null if it doesn't exist\n */\nexport function getSchemaTypeNameByName(name: string, schema: Schema): 'node' | 'mark' | null {\n  if (schema.nodes[name]) {\n    return 'node'\n  }\n\n  if (schema.marks[name]) {\n    return 'mark'\n  }\n\n  return null\n}\n", "import type { MarkType, NodeType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nimport { getMarkAttributes } from './getMarkAttributes.js'\nimport { getNodeAttributes } from './getNodeAttributes.js'\nimport { getSchemaTypeNameByName } from './getSchemaTypeNameByName.js'\n\n/**\n * Get node or mark attributes by type or name on the current editor state\n * @param state The current editor state\n * @param typeOrName The node or mark type or name\n * @returns The attributes of the node or mark or an empty object\n */\nexport function getAttributes(state: EditorState, typeOrName: string | NodeType | MarkType): Record<string, any> {\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (schemaType === 'node') {\n    return getNodeAttributes(state, typeOrName as NodeType)\n  }\n\n  if (schemaType === 'mark') {\n    return getMarkAttributes(state, typeOrName as MarkType)\n  }\n\n  return {}\n}\n", "/**\n * Removes duplicated values within an array.\n * Supports numbers, strings and objects.\n */\nexport function removeDuplicates<T>(array: T[], by = JSON.stringify): T[] {\n  const seen: Record<any, any> = {}\n\n  return array.filter(item => {\n    const key = by(item)\n\n    return Object.prototype.hasOwnProperty.call(seen, key) ? false : (seen[key] = true)\n  })\n}\n", "import type { Step, Transform } from '@tiptap/pm/transform'\n\nimport type { Range } from '../types.js'\nimport { removeDuplicates } from '../utilities/removeDuplicates.js'\n\nexport type ChangedRange = {\n  oldRange: Range\n  newRange: Range\n}\n\n/**\n * Removes duplicated ranges and ranges that are\n * fully captured by other ranges.\n */\nfunction simplifyChangedRanges(changes: ChangedRange[]): ChangedRange[] {\n  const uniqueChanges = removeDuplicates(changes)\n\n  return uniqueChanges.length === 1\n    ? uniqueChanges\n    : uniqueChanges.filter((change, index) => {\n        const rest = uniqueChanges.filter((_, i) => i !== index)\n\n        return !rest.some(otherChange => {\n          return (\n            change.oldRange.from >= otherChange.oldRange.from &&\n            change.oldRange.to <= otherChange.oldRange.to &&\n            change.newRange.from >= otherChange.newRange.from &&\n            change.newRange.to <= otherChange.newRange.to\n          )\n        })\n      })\n}\n\n/**\n * Returns a list of changed ranges\n * based on the first and last state of all steps.\n */\nexport function getChangedRanges(transform: Transform): ChangedRange[] {\n  const { mapping, steps } = transform\n  const changes: ChangedRange[] = []\n\n  mapping.maps.forEach((stepMap, index) => {\n    const ranges: Range[] = []\n\n    // This accounts for step changes where no range was actually altered\n    // e.g. when setting a mark, node attribute, etc.\n    // @ts-ignore\n    if (!stepMap.ranges.length) {\n      const { from, to } = steps[index] as Step & {\n        from?: number\n        to?: number\n      }\n\n      if (from === undefined || to === undefined) {\n        return\n      }\n\n      ranges.push({ from, to })\n    } else {\n      stepMap.forEach((from, to) => {\n        ranges.push({ from, to })\n      })\n    }\n\n    ranges.forEach(({ from, to }) => {\n      const newStart = mapping.slice(index).map(from, -1)\n      const newEnd = mapping.slice(index).map(to)\n      const oldStart = mapping.invert().map(newStart, -1)\n      const oldEnd = mapping.invert().map(newEnd)\n\n      changes.push({\n        oldRange: {\n          from: oldStart,\n          to: oldEnd,\n        },\n        newRange: {\n          from: newStart,\n          to: newEnd,\n        },\n      })\n    })\n  })\n\n  return simplifyChangedRanges(changes)\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport type { JSONContent } from '../types.js'\n\ninterface DebugJSONContent extends J<PERSON><PERSON>ontent {\n  from: number\n  to: number\n}\n\nexport function getDebugJSON(node: ProseMirrorNode, startOffset = 0): DebugJSONContent {\n  const isTopNode = node.type === node.type.schema.topNodeType\n  const increment = isTopNode ? 0 : 1\n  const from = startOffset\n  const to = from + node.nodeSize\n  const marks = node.marks.map(mark => {\n    const output: { type: string; attrs?: Record<string, any> } = {\n      type: mark.type.name,\n    }\n\n    if (Object.keys(mark.attrs).length) {\n      output.attrs = { ...mark.attrs }\n    }\n\n    return output\n  })\n  const attrs = { ...node.attrs }\n  const output: DebugJSONContent = {\n    type: node.type.name,\n    from,\n    to,\n  }\n\n  if (Object.keys(attrs).length) {\n    output.attrs = attrs\n  }\n\n  if (marks.length) {\n    output.marks = marks\n  }\n\n  if (node.content.childCount) {\n    output.content = []\n\n    node.forEach((child, offset) => {\n      output.content?.push(getDebugJSON(child, startOffset + offset + increment))\n    })\n  }\n\n  if (node.text) {\n    output.text = node.text\n  }\n\n  return output\n}\n", "export function isRegExp(value: any): value is RegExp {\n  return Object.prototype.toString.call(value) === '[object RegExp]'\n}\n", "import { isRegExp } from './isRegExp.js'\n\n/**\n * Check if object1 includes object2\n * @param object1 Object\n * @param object2 Object\n */\nexport function objectIncludes(\n  object1: Record<string, any>,\n  object2: Record<string, any>,\n  options: { strict: boolean } = { strict: true },\n): boolean {\n  const keys = Object.keys(object2)\n\n  if (!keys.length) {\n    return true\n  }\n\n  return keys.every(key => {\n    if (options.strict) {\n      return object2[key] === object1[key]\n    }\n\n    if (isRegExp(object2[key])) {\n      return object2[key].test(object1[key])\n    }\n\n    return object2[key] === object1[key]\n  })\n}\n", "import type { Mark as ProseMirrorMark, MarkType, ResolvedPos } from '@tiptap/pm/model'\n\nimport type { Range } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\n\nfunction findMarkInSet(\n  marks: ProseMirrorMark[],\n  type: MarkType,\n  attributes: Record<string, any> = {},\n): ProseMirrorMark | undefined {\n  return marks.find(item => {\n    return (\n      item.type === type &&\n      objectIncludes(\n        // Only check equality for the attributes that are provided\n        Object.fromEntries(Object.keys(attributes).map(k => [k, item.attrs[k]])),\n        attributes,\n      )\n    )\n  })\n}\n\nfunction isMarkInSet(marks: ProseMirrorMark[], type: MarkType, attributes: Record<string, any> = {}): boolean {\n  return !!findMarkInSet(marks, type, attributes)\n}\n\n/**\n * Get the range of a mark at a resolved position.\n */\nexport function getMarkRange(\n  /**\n   * The position to get the mark range for.\n   */\n  $pos: ResolvedPos,\n  /**\n   * The mark type to get the range for.\n   */\n  type: MarkType,\n  /**\n   * The attributes to match against.\n   * If not provided, only the first mark at the position will be matched.\n   */\n  attributes?: Record<string, any>,\n): Range | void {\n  if (!$pos || !type) {\n    return\n  }\n  let start = $pos.parent.childAfter($pos.parentOffset)\n\n  // If the cursor is at the start of a text node that does not have the mark, look backward\n  if (!start.node || !start.node.marks.some(mark => mark.type === type)) {\n    start = $pos.parent.childBefore($pos.parentOffset)\n  }\n\n  // If there is no text node with the mark even backward, return undefined\n  if (!start.node || !start.node.marks.some(mark => mark.type === type)) {\n    return\n  }\n\n  // Default to only matching against the first mark's attributes\n  attributes = attributes || start.node.marks[0]?.attrs\n\n  // We now know that the cursor is either at the start, middle or end of a text node with the specified mark\n  // so we can look it up on the targeted mark\n  const mark = findMarkInSet([...start.node.marks], type, attributes)\n\n  if (!mark) {\n    return\n  }\n\n  let startIndex = start.index\n  let startPos = $pos.start() + start.offset\n  let endIndex = startIndex + 1\n  let endPos = startPos + start.node.nodeSize\n\n  while (startIndex > 0 && isMarkInSet([...$pos.parent.child(startIndex - 1).marks], type, attributes)) {\n    startIndex -= 1\n    startPos -= $pos.parent.child(startIndex).nodeSize\n  }\n\n  while (endIndex < $pos.parent.childCount && isMarkInSet([...$pos.parent.child(endIndex).marks], type, attributes)) {\n    endPos += $pos.parent.child(endIndex).nodeSize\n    endIndex += 1\n  }\n\n  return {\n    from: startPos,\n    to: endPos,\n  }\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport type { MarkRange } from '../types.js'\nimport { getMarkRange } from './getMarkRange.js'\n\nexport function getMarksBetween(from: number, to: number, doc: ProseMirrorNode): MarkRange[] {\n  const marks: MarkRange[] = []\n\n  // get all inclusive marks on empty selection\n  if (from === to) {\n    doc\n      .resolve(from)\n      .marks()\n      .forEach(mark => {\n        const $pos = doc.resolve(from)\n        const range = getMarkRange($pos, mark.type)\n\n        if (!range) {\n          return\n        }\n\n        marks.push({\n          mark,\n          ...range,\n        })\n      })\n  } else {\n    doc.nodesBetween(from, to, (node, pos) => {\n      if (!node || node?.nodeSize === undefined) {\n        return\n      }\n\n      marks.push(\n        ...node.marks.map(mark => ({\n          from: pos,\n          to: pos + node.nodeSize,\n          mark,\n        })),\n      )\n    })\n  }\n\n  return marks\n}\n", "import type { Node, NodeType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\n/**\n * Finds the first node of a given type or name in the current selection.\n * @param state The editor state.\n * @param typeOrName The node type or name.\n * @param pos The position to start searching from.\n * @param maxDepth The maximum depth to search.\n * @returns The node and the depth as an array.\n */\nexport const getNodeAtPosition = (state: EditorState, typeOrName: string | NodeType, pos: number, maxDepth = 20) => {\n  const $pos = state.doc.resolve(pos)\n\n  let currentDepth = maxDepth\n  let node: Node | null = null\n\n  while (currentDepth > 0 && node === null) {\n    const currentNode = $pos.node(currentDepth)\n\n    if (currentNode?.type.name === typeOrName) {\n      node = currentNode\n    } else {\n      currentDepth -= 1\n    }\n  }\n\n  return [node, currentDepth] as [Node | null, number]\n}\n", "import type { MarkType, NodeType, Schema } from '@tiptap/pm/model'\n\n/**\n * Tries to get a node or mark type by its name.\n * @param name The name of the node or mark type\n * @param schema The Prosemiror schema to search in\n * @returns The node or mark type, or null if it doesn't exist\n */\nexport function getSchemaTypeByName(name: string, schema: Schema): NodeType | MarkType | null {\n  return schema.nodes[name] || schema.marks[name] || null\n}\n", "import type { ExtensionAttribute } from '../types.js'\n\n/**\n * Return attributes of an extension that should be splitted by keepOnSplit flag\n * @param extensionAttributes Array of extension attributes\n * @param typeName The type of the extension\n * @param attributes The attributes of the extension\n * @returns The splitted attributes\n */\nexport function getSplittedAttributes(\n  extensionAttributes: ExtensionAttribute[],\n  typeName: string,\n  attributes: Record<string, any>,\n): Record<string, any> {\n  return Object.fromEntries(\n    Object.entries(attributes).filter(([name]) => {\n      const extensionAttribute = extensionAttributes.find(item => {\n        return item.type === typeName && item.name === name\n      })\n\n      if (!extensionAttribute) {\n        return false\n      }\n\n      return extensionAttribute.attribute.keepOnSplit\n    }),\n  )\n}\n", "import type { ResolvedPos } from '@tiptap/pm/model'\n\n/**\n * Returns the text content of a resolved prosemirror position\n * @param $from The resolved position to get the text content from\n * @param maxMatch The maximum number of characters to match\n * @returns The text content\n */\nexport const getTextContentFromNodes = ($from: ResolvedPos, maxMatch = 500) => {\n  let textBefore = ''\n\n  const sliceEndPos = $from.parentOffset\n\n  $from.parent.nodesBetween(Math.max(0, sliceEndPos - maxMatch), sliceEndPos, (node, pos, parent, index) => {\n    const chunk =\n      node.type.spec.toText?.({\n        node,\n        pos,\n        parent,\n        index,\n      }) ||\n      node.textContent ||\n      '%leaf%'\n\n    textBefore += node.isAtom && !node.isText ? chunk : chunk.slice(0, Math.max(0, sliceEndPos - pos))\n  })\n\n  return textBefore\n}\n", "import type { MarkType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nimport type { MarkRange } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\nimport { getMarkType } from './getMarkType.js'\n\nexport function isMarkActive(\n  state: EditorState,\n  typeOrName: MarkType | string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  const { empty, ranges } = state.selection\n  const type = typeOrName ? getMarkType(typeOrName, state.schema) : null\n\n  if (empty) {\n    return !!(state.storedMarks || state.selection.$from.marks())\n      .filter(mark => {\n        if (!type) {\n          return true\n        }\n\n        return type.name === mark.type.name\n      })\n      .find(mark => objectIncludes(mark.attrs, attributes, { strict: false }))\n  }\n\n  let selectionRange = 0\n  const markRanges: MarkRange[] = []\n\n  ranges.forEach(({ $from, $to }) => {\n    const from = $from.pos\n    const to = $to.pos\n\n    state.doc.nodesBetween(from, to, (node, pos) => {\n      if (!node.isText && !node.marks.length) {\n        return\n      }\n\n      const relativeFrom = Math.max(from, pos)\n      const relativeTo = Math.min(to, pos + node.nodeSize)\n      const range = relativeTo - relativeFrom\n\n      selectionRange += range\n\n      markRanges.push(\n        ...node.marks.map(mark => ({\n          mark,\n          from: relativeFrom,\n          to: relativeTo,\n        })),\n      )\n    })\n  })\n\n  if (selectionRange === 0) {\n    return false\n  }\n\n  // calculate range of matched mark\n  const matchedRange = markRanges\n    .filter(markRange => {\n      if (!type) {\n        return true\n      }\n\n      return type.name === markRange.mark.type.name\n    })\n    .filter(markRange => objectIncludes(markRange.mark.attrs, attributes, { strict: false }))\n    .reduce((sum, markRange) => sum + markRange.to - markRange.from, 0)\n\n  // calculate range of marks that excludes the searched mark\n  // for example `code` doesn’t allow any other marks\n  const excludedRange = markRanges\n    .filter(markRange => {\n      if (!type) {\n        return true\n      }\n\n      return markRange.mark.type !== type && markRange.mark.type.excludes(type)\n    })\n    .reduce((sum, markRange) => sum + markRange.to - markRange.from, 0)\n\n  // we only include the result of `excludedRange`\n  // if there is a match at all\n  const range = matchedRange > 0 ? matchedRange + excludedRange : matchedRange\n\n  return range >= selectionRange\n}\n", "import type { NodeType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nimport type { NodeRange } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\nimport { getNodeType } from './getNodeType.js'\n\nexport function isNodeActive(\n  state: EditorState,\n  typeOrName: NodeType | string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  const { from, to, empty } = state.selection\n  const type = typeOrName ? getNodeType(typeOrName, state.schema) : null\n\n  const nodeRanges: NodeRange[] = []\n\n  state.doc.nodesBetween(from, to, (node, pos) => {\n    if (node.isText) {\n      return\n    }\n\n    const relativeFrom = Math.max(from, pos)\n    const relativeTo = Math.min(to, pos + node.nodeSize)\n\n    nodeRanges.push({\n      node,\n      from: relativeFrom,\n      to: relativeTo,\n    })\n  })\n\n  const selectionRange = to - from\n  const matchedNodeRanges = nodeRanges\n    .filter(nodeRange => {\n      if (!type) {\n        return true\n      }\n\n      return type.name === nodeRange.node.type.name\n    })\n    .filter(nodeRange => objectIncludes(nodeRange.node.attrs, attributes, { strict: false }))\n\n  if (empty) {\n    return !!matchedNodeRanges.length\n  }\n\n  const range = matchedNodeRanges.reduce((sum, nodeRange) => sum + nodeRange.to - nodeRange.from, 0)\n\n  return range >= selectionRange\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nimport { getSchemaTypeNameByName } from './getSchemaTypeNameByName.js'\nimport { isMarkActive } from './isMarkActive.js'\nimport { isNodeActive } from './isNodeActive.js'\n\nexport function isActive(state: EditorState, name: string | null, attributes: Record<string, any> = {}): boolean {\n  if (!name) {\n    return isNodeActive(state, null, attributes) || isMarkActive(state, null, attributes)\n  }\n\n  const schemaType = getSchemaTypeNameByName(name, state.schema)\n\n  if (schemaType === 'node') {\n    return isNodeActive(state, name, attributes)\n  }\n\n  if (schemaType === 'mark') {\n    return isMarkActive(state, name, attributes)\n  }\n\n  return false\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nimport { findParentNode } from './findParentNode.js'\n\nexport const isAtEndOfNode = (state: EditorState, nodeType?: string) => {\n  const { $from, $to, $anchor } = state.selection\n\n  if (nodeType) {\n    const parentNode = findParentNode(node => node.type.name === nodeType)(state.selection)\n\n    if (!parentNode) {\n      return false\n    }\n\n    const $parentPos = state.doc.resolve(parentNode.pos + 1)\n\n    if ($anchor.pos + 1 === $parentPos.end()) {\n      return true\n    }\n\n    return false\n  }\n\n  if ($to.parentOffset < $to.parent.nodeSize - 2 || $from.pos !== $to.pos) {\n    return false\n  }\n\n  return true\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const isAtStartOfNode = (state: EditorState) => {\n  const { $from, $to } = state.selection\n\n  if ($from.parentOffset > 0 || $from.pos !== $to.pos) {\n    return false\n  }\n\n  return true\n}\n", "import type { AnyExtension, EnableRules } from '../types.js'\n\nexport function isExtensionRulesEnabled(extension: AnyExtension, enabled: EnableRules): boolean {\n  if (Array.isArray(enabled)) {\n    return enabled.some(enabledExtension => {\n      const name = typeof enabledExtension === 'string' ? enabledExtension : enabledExtension.name\n\n      return name === extension.name\n    })\n  }\n\n  return enabled\n}\n", "import { getExtensionField } from '../helpers/getExtensionField.js'\nimport type { NodeConfig } from '../index.js'\nimport type { Extensions } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\nimport { splitExtensions } from './splitExtensions.js'\n\nexport function isList(name: string, extensions: Extensions): boolean {\n  const { nodeExtensions } = splitExtensions(extensions)\n  const extension = nodeExtensions.find(item => item.name === name)\n\n  if (!extension) {\n    return false\n  }\n\n  const context = {\n    name: extension.name,\n    options: extension.options,\n    storage: extension.storage,\n  }\n  const group = callOrReturn(getExtensionField<NodeConfig['group']>(extension, 'group', context))\n\n  if (typeof group !== 'string') {\n    return false\n  }\n\n  return group.split(' ').includes('list')\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\n/**\n * Returns true if the given prosemirror node is empty.\n */\nexport function isNodeEmpty(\n  node: ProseMirrorNode,\n  {\n    checkChildren = true,\n    ignoreWhitespace = false,\n  }: {\n    /**\n     * When true (default), it will also check if all children are empty.\n     */\n    checkChildren?: boolean\n    /**\n     * When true, it will ignore whitespace when checking for emptiness.\n     */\n    ignoreWhitespace?: boolean\n  } = {},\n): boolean {\n  if (ignoreWhitespace) {\n    if (node.type.name === 'hardBreak') {\n      // Hard breaks are considered empty\n      return true\n    }\n    if (node.isText) {\n      return /^\\s*$/m.test(node.text ?? '')\n    }\n  }\n\n  if (node.isText) {\n    return !node.text\n  }\n\n  if (node.isAtom || node.isLeaf) {\n    return false\n  }\n\n  if (node.content.childCount === 0) {\n    return true\n  }\n\n  if (checkChildren) {\n    let isContentEmpty = true\n\n    node.content.forEach(childNode => {\n      if (isContentEmpty === false) {\n        // Exit early for perf\n        return\n      }\n\n      if (!isNodeEmpty(childNode, { ignoreWhitespace, checkChildren })) {\n        isContentEmpty = false\n      }\n    })\n\n    return isContentEmpty\n  }\n\n  return false\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\n\nexport function isNodeSelection(value: unknown): value is NodeSelection {\n  return value instanceof NodeSelection\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nexport function isTextSelection(value: unknown): value is TextSelection {\n  return value instanceof TextSelection\n}\n", "export function minMax(value = 0, min = 0, max = 0): number {\n  return Math.min(Math.max(value, min), max)\n}\n", "import type { EditorView } from '@tiptap/pm/view'\n\nimport { minMax } from '../utilities/minMax.js'\n\nexport function posToDOMRect(view: EditorView, from: number, to: number): DOMRect {\n  const minPos = 0\n  const maxPos = view.state.doc.content.size\n  const resolvedFrom = minMax(from, minPos, maxPos)\n  const resolvedEnd = minMax(to, minPos, maxPos)\n  const start = view.coordsAtPos(resolvedFrom)\n  const end = view.coordsAtPos(resolvedEnd, -1)\n  const top = Math.min(start.top, end.top)\n  const bottom = Math.max(start.bottom, end.bottom)\n  const left = Math.min(start.left, end.left)\n  const right = Math.max(start.right, end.right)\n  const width = right - left\n  const height = bottom - top\n  const x = left\n  const y = top\n  const data = {\n    top,\n    bottom,\n    left,\n    right,\n    width,\n    height,\n    x,\n    y,\n  }\n\n  return {\n    ...data,\n    toJSON: () => data,\n  }\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Selection, TextSelection } from '@tiptap/pm/state'\n\nimport type { FocusPosition } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\nexport function resolveFocusPosition(doc: ProseMirrorNode, position: FocusPosition = null): Selection | null {\n  if (!position) {\n    return null\n  }\n\n  const selectionAtStart = Selection.atStart(doc)\n  const selectionAtEnd = Selection.atEnd(doc)\n\n  if (position === 'start' || position === true) {\n    return selectionAtStart\n  }\n\n  if (position === 'end') {\n    return selectionAtEnd\n  }\n\n  const minPos = selectionAtStart.from\n  const maxPos = selectionAtEnd.to\n\n  if (position === 'all') {\n    return TextSelection.create(doc, minMax(0, minPos, maxPos), minMax(doc.content.size, minPos, maxPos))\n  }\n\n  return TextSelection.create(doc, minMax(position, minPos, maxPos), minMax(position, minPos, maxPos))\n}\n", "import type { Schema } from '@tiptap/pm/model'\n\nimport type { J<PERSON>NContent } from '../types.js'\n\ntype RewriteUnknownContentOptions = {\n  /**\n   * If true, unknown nodes will be treated as paragraphs\n   * @default true\n   */\n  fallbackToParagraph?: boolean\n}\n\ntype RewrittenContent = {\n  /**\n   * The original JSON content that was rewritten\n   */\n  original: JSONContent\n  /**\n   * The name of the node or mark that was unsupported\n   */\n  unsupported: string\n}[]\n\n/**\n * The actual implementation of the rewriteUnknownContent function\n */\nfunction rewriteUnknownContentInner({\n  json,\n  validMarks,\n  validNodes,\n  options,\n  rewrittenContent = [],\n}: {\n  json: JSONContent\n  validMarks: Set<string>\n  validNodes: Set<string>\n  options?: RewriteUnknownContentOptions\n  rewrittenContent?: RewrittenContent\n}): {\n  /**\n   * The cleaned JSON content\n   */\n  json: JSONContent | null\n  /**\n   * The array of nodes and marks that were rewritten\n   */\n  rewrittenContent: RewrittenContent\n} {\n  if (json.marks && Array.isArray(json.marks)) {\n    json.marks = json.marks.filter(mark => {\n      const name = typeof mark === 'string' ? mark : mark.type\n\n      if (validMarks.has(name)) {\n        return true\n      }\n\n      rewrittenContent.push({\n        original: JSON.parse(JSON.stringify(mark)),\n        unsupported: name,\n      })\n      // Just ignore any unknown marks\n      return false\n    })\n  }\n\n  if (json.content && Array.isArray(json.content)) {\n    json.content = json.content\n      .map(\n        value =>\n          rewriteUnknownContentInner({\n            json: value,\n            validMarks,\n            validNodes,\n            options,\n            rewrittenContent,\n          }).json,\n      )\n      .filter(a => a !== null && a !== undefined)\n  }\n\n  if (json.type && !validNodes.has(json.type)) {\n    rewrittenContent.push({\n      original: JSON.parse(JSON.stringify(json)),\n      unsupported: json.type,\n    })\n\n    if (json.content && Array.isArray(json.content) && options?.fallbackToParagraph !== false) {\n      // Just treat it like a paragraph and hope for the best\n      json.type = 'paragraph'\n\n      return {\n        json,\n        rewrittenContent,\n      }\n    }\n\n    // or just omit it entirely\n    return {\n      json: null,\n      rewrittenContent,\n    }\n  }\n\n  return { json, rewrittenContent }\n}\n\n/**\n * Rewrite unknown nodes and marks within JSON content\n * Allowing for user within the editor\n */\nexport function rewriteUnknownContent(\n  /**\n   * The JSON content to clean of unknown nodes and marks\n   */\n  json: JSONContent,\n  /**\n   * The schema to use for validation\n   */\n  schema: Schema,\n  /**\n   * Options for the cleaning process\n   */\n  options?: RewriteUnknownContentOptions,\n): {\n  /**\n   * The cleaned JSON content\n   */\n  json: JSONContent | null\n  /**\n   * The array of nodes and marks that were rewritten\n   */\n  rewrittenContent: {\n    /**\n     * The original JSON content that was rewritten\n     */\n    original: JSONContent\n    /**\n     * The name of the node or mark that was unsupported\n     */\n    unsupported: string\n  }[]\n} {\n  return rewriteUnknownContentInner({\n    json,\n    validNodes: new Set(Object.keys(schema.nodes)),\n    validMarks: new Set(Object.keys(schema.marks)),\n    options,\n  })\n}\n", "import type { Transaction } from '@tiptap/pm/state'\nimport { Selection } from '@tiptap/pm/state'\nimport { ReplaceAroundStep, ReplaceStep } from '@tiptap/pm/transform'\n\n// source: https://github.com/ProseMirror/prosemirror-state/blob/master/src/selection.js#L466\nexport function selectionToInsertionEnd(tr: Transaction, startLen: number, bias: number) {\n  const last = tr.steps.length - 1\n\n  if (last < startLen) {\n    return\n  }\n\n  const step = tr.steps[last]\n\n  if (!(step instanceof ReplaceStep || step instanceof ReplaceAroundStep)) {\n    return\n  }\n\n  const map = tr.mapping.maps[last]\n  let end = 0\n\n  map.forEach((_from, _to, _newFrom, newTo) => {\n    if (end === 0) {\n      end = newTo\n    }\n  })\n\n  tr.setSelection(Selection.near(tr.doc.resolve(end), bias))\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Fragment } from '@tiptap/pm/model'\nimport type { EditorState, TextSelection } from '@tiptap/pm/state'\nimport { Plugin } from '@tiptap/pm/state'\n\nimport { CommandManager } from './CommandManager.js'\nimport type { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport { getTextContentFromNodes } from './helpers/getTextContentFromNodes.js'\nimport type { CanCommands, ChainedCommands, ExtendedRegExpMatchArray, Range, SingleCommands } from './types.js'\nimport { isRegExp } from './utilities/isRegExp.js'\n\nexport type InputRuleMatch = {\n  index: number\n  text: string\n  replaceWith?: string\n  match?: RegExpMatchArray\n  data?: Record<string, any>\n}\n\nexport type InputRuleFinder = RegExp | ((text: string) => InputRuleMatch | null)\n\nexport class InputRule {\n  find: InputRuleFinder\n\n  handler: (props: {\n    state: EditorState\n    range: Range\n    match: ExtendedRegExpMatchArray\n    commands: SingleCommands\n    chain: () => ChainedCommands\n    can: () => CanCommands\n  }) => void | null\n\n  constructor(config: {\n    find: InputRuleFinder\n    handler: (props: {\n      state: EditorState\n      range: Range\n      match: ExtendedRegExpMatchArray\n      commands: SingleCommands\n      chain: () => ChainedCommands\n      can: () => CanCommands\n    }) => void | null\n  }) {\n    this.find = config.find\n    this.handler = config.handler\n  }\n}\n\nconst inputRuleMatcherHandler = (text: string, find: InputRuleFinder): ExtendedRegExpMatchArray | null => {\n  if (isRegExp(find)) {\n    return find.exec(text)\n  }\n\n  const inputRuleMatch = find(text)\n\n  if (!inputRuleMatch) {\n    return null\n  }\n\n  const result: ExtendedRegExpMatchArray = [inputRuleMatch.text]\n\n  result.index = inputRuleMatch.index\n  result.input = text\n  result.data = inputRuleMatch.data\n\n  if (inputRuleMatch.replaceWith) {\n    if (!inputRuleMatch.text.includes(inputRuleMatch.replaceWith)) {\n      console.warn('[tiptap warn]: \"inputRuleMatch.replaceWith\" must be part of \"inputRuleMatch.text\".')\n    }\n\n    result.push(inputRuleMatch.replaceWith)\n  }\n\n  return result\n}\n\nfunction run(config: {\n  editor: Editor\n  from: number\n  to: number\n  text: string\n  rules: InputRule[]\n  plugin: Plugin\n}): boolean {\n  const { editor, from, to, text, rules, plugin } = config\n  const { view } = editor\n\n  if (view.composing) {\n    return false\n  }\n\n  const $from = view.state.doc.resolve(from)\n\n  if (\n    // check for code node\n    $from.parent.type.spec.code ||\n    // check for code mark\n    !!($from.nodeBefore || $from.nodeAfter)?.marks.find(mark => mark.type.spec.code)\n  ) {\n    return false\n  }\n\n  let matched = false\n\n  const textBefore = getTextContentFromNodes($from) + text\n\n  rules.forEach(rule => {\n    if (matched) {\n      return\n    }\n\n    const match = inputRuleMatcherHandler(textBefore, rule.find)\n\n    if (!match) {\n      return\n    }\n\n    const tr = view.state.tr\n    const state = createChainableState({\n      state: view.state,\n      transaction: tr,\n    })\n    const range = {\n      from: from - (match[0].length - text.length),\n      to,\n    }\n\n    const { commands, chain, can } = new CommandManager({\n      editor,\n      state,\n    })\n\n    const handler = rule.handler({\n      state,\n      range,\n      match,\n      commands,\n      chain,\n      can,\n    })\n\n    // stop if there are no changes\n    if (handler === null || !tr.steps.length) {\n      return\n    }\n\n    // store transform as meta data\n    // so we can undo input rules within the `undoInputRules` command\n    tr.setMeta(plugin, {\n      transform: tr,\n      from,\n      to,\n      text,\n    })\n\n    view.dispatch(tr)\n    matched = true\n  })\n\n  return matched\n}\n\n/**\n * Create an input rules plugin. When enabled, it will cause text\n * input that matches any of the given rules to trigger the rule’s\n * action.\n */\nexport function inputRulesPlugin(props: { editor: Editor; rules: InputRule[] }): Plugin {\n  const { editor, rules } = props\n  const plugin = new Plugin({\n    state: {\n      init() {\n        return null\n      },\n      apply(tr, prev, state) {\n        const stored = tr.getMeta(plugin)\n\n        if (stored) {\n          return stored\n        }\n\n        // if InputRule is triggered by insertContent()\n        const simulatedInputMeta = tr.getMeta('applyInputRules') as\n          | undefined\n          | {\n              from: number\n              text: string | ProseMirrorNode | Fragment\n            }\n        const isSimulatedInput = !!simulatedInputMeta\n\n        if (isSimulatedInput) {\n          setTimeout(() => {\n            let { text } = simulatedInputMeta\n\n            if (typeof text === 'string') {\n              text = text as string\n            } else {\n              text = getHTMLFromFragment(Fragment.from(text), state.schema)\n            }\n\n            const { from } = simulatedInputMeta\n            const to = from + text.length\n\n            run({\n              editor,\n              from,\n              to,\n              text,\n              rules,\n              plugin,\n            })\n          })\n        }\n\n        return tr.selectionSet || tr.docChanged ? null : prev\n      },\n    },\n\n    props: {\n      handleTextInput(view, from, to, text) {\n        return run({\n          editor,\n          from,\n          to,\n          text,\n          rules,\n          plugin,\n        })\n      },\n\n      handleDOMEvents: {\n        compositionend: view => {\n          setTimeout(() => {\n            const { $cursor } = view.state.selection as TextSelection\n\n            if ($cursor) {\n              run({\n                editor,\n                from: $cursor.pos,\n                to: $cursor.pos,\n                text: '',\n                rules,\n                plugin,\n              })\n            }\n          })\n\n          return false\n        },\n      },\n\n      // add support for input rules to trigger on enter\n      // this is useful for example for code blocks\n      handleKeyDown(view, event) {\n        if (event.key !== 'Enter') {\n          return false\n        }\n\n        const { $cursor } = view.state.selection as TextSelection\n\n        if ($cursor) {\n          return run({\n            editor,\n            from: $cursor.pos,\n            to: $cursor.pos,\n            text: '\\n',\n            rules,\n            plugin,\n          })\n        }\n\n        return false\n      },\n    },\n\n    // @ts-ignore\n    isInputRules: true,\n  }) as Plugin\n\n  return plugin\n}\n", "// see: https://github.com/mesqueeb/is-what/blob/88d6e4ca92fb2baab6003c54e02eedf4e729e5ab/src/index.ts\n\nfunction getType(value: any): string {\n  return Object.prototype.toString.call(value).slice(8, -1)\n}\n\nexport function isPlainObject(value: any): value is Record<string, any> {\n  if (getType(value) !== 'Object') {\n    return false\n  }\n\n  return value.constructor === Object && Object.getPrototypeOf(value) === Object.prototype\n}\n", "import { isPlainObject } from './isPlainObject.js'\n\nexport function mergeDeep(target: Record<string, any>, source: Record<string, any>): Record<string, any> {\n  const output = { ...target }\n\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isPlainObject(source[key]) && isPlainObject(target[key])) {\n        output[key] = mergeDeep(target[key], source[key])\n      } else {\n        output[key] = source[key]\n      }\n    })\n  }\n\n  return output\n}\n", "import type { Plugin } from '@tiptap/pm/state'\n\nimport type { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport type { ExtensionConfig, MarkConfig, NodeConfig } from './index.js'\nimport type { InputRule } from './InputRule.js'\nimport type { Mark } from './Mark.js'\nimport type { Node } from './Node.js'\nimport type { PasteRule } from './PasteRule.js'\nimport type {\n  AnyConfig,\n  EditorEvents,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\nexport interface ExtendableConfig<\n  Options = any,\n  Storage = any,\n  Config extends\n    | ExtensionConfig<Options, Storage>\n    | NodeConfig<Options, Storage>\n    | MarkConfig<Options, Storage>\n    | ExtendableConfig<Options, Storage> = ExtendableConfig<Options, Storage, any, any>,\n  PMType = any,\n> {\n  /**\n   * The extension name - this must be unique.\n   * It will be used to identify the extension.\n   *\n   * @example 'myExtension'\n   */\n  name: string\n\n  /**\n   * The priority of your extension. The higher, the earlier it will be called\n   * and will take precedence over other extensions with a lower priority.\n   * @default 100\n   * @example 101\n   */\n  priority?: number\n\n  /**\n   * This method will add options to this extension\n   * @see https://tiptap.dev/docs/editor/guide/custom-extensions#settings\n   * @example\n   * addOptions() {\n   *  return {\n   *    myOption: 'foo',\n   *    myOtherOption: 10,\n   * }\n   */\n  addOptions?: (this: { name: string; parent: ParentConfig<Config>['addOptions'] }) => Options\n\n  /**\n   * The default storage this extension can save data to.\n   * @see https://tiptap.dev/docs/editor/guide/custom-extensions#storage\n   * @example\n   * defaultStorage: {\n   *   prefetchedUsers: [],\n   *   loading: false,\n   * }\n   */\n  addStorage?: (this: { name: string; options: Options; parent: ParentConfig<Config>['addStorage'] }) => Storage\n\n  /**\n   * This function adds globalAttributes to specific nodes.\n   * @see https://tiptap.dev/docs/editor/guide/custom-extensions#global-attributes\n   * @example\n   * addGlobalAttributes() {\n   *   return [\n   *     {\n           // Extend the following extensions\n   *       types: [\n   *         'heading',\n   *         'paragraph',\n   *       ],\n   *       // … with those attributes\n   *       attributes: {\n   *         textAlign: {\n   *           default: 'left',\n   *           renderHTML: attributes => ({\n   *             style: `text-align: ${attributes.textAlign}`,\n   *           }),\n   *           parseHTML: element => element.style.textAlign || 'left',\n   *         },\n   *       },\n   *     },\n   *   ]\n   * }\n   */\n  addGlobalAttributes?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    extensions: (Node | Mark)[]\n    parent: ParentConfig<Config>['addGlobalAttributes']\n  }) => GlobalAttributes\n\n  /**\n   * This function adds commands to the editor\n   * @see https://tiptap.dev/docs/editor/guide/custom-extensions#commands\n   * @example\n   * addCommands() {\n   *   return {\n   *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n   *   }\n   * }\n   */\n  addCommands?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    editor: Editor\n    type: PMType\n    parent: ParentConfig<Config>['addCommands']\n  }) => Partial<RawCommands>\n\n  /**\n   * This function registers keyboard shortcuts.\n   * @see https://tiptap.dev/docs/editor/guide/custom-extensions#keyboard-shortcuts\n   * @example\n   * addKeyboardShortcuts() {\n   *   return {\n   *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n   *   }\n   * },\n   */\n  addKeyboardShortcuts?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    editor: Editor\n    type: PMType\n    parent: ParentConfig<Config>['addKeyboardShortcuts']\n  }) => {\n    [key: string]: KeyboardShortcutCommand\n  }\n\n  /**\n   * This function adds input rules to the editor.\n   * @see https://tiptap.dev/docs/editor/guide/custom-extensions#input-rules\n   * @example\n   * addInputRules() {\n   *   return [\n   *     markInputRule({\n   *       find: inputRegex,\n   *       type: this.type,\n   *     }),\n   *   ]\n   * },\n   */\n  addInputRules?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    editor: Editor\n    type: PMType\n    parent: ParentConfig<Config>['addInputRules']\n  }) => InputRule[]\n\n  /**\n   * This function adds paste rules to the editor.\n   * @see https://tiptap.dev/docs/editor/guide/custom-extensions#paste-rules\n   * @example\n   * addPasteRules() {\n   *   return [\n   *     markPasteRule({\n   *       find: pasteRegex,\n   *       type: this.type,\n   *     }),\n   *   ]\n   * },\n   */\n  addPasteRules?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    editor: Editor\n    type: PMType\n    parent: ParentConfig<Config>['addPasteRules']\n  }) => PasteRule[]\n\n  /**\n   * This function adds Prosemirror plugins to the editor\n   * @see https://tiptap.dev/docs/editor/guide/custom-extensions#prosemirror-plugins\n   * @example\n   * addProseMirrorPlugins() {\n   *   return [\n   *     customPlugin(),\n   *   ]\n   * }\n   */\n  addProseMirrorPlugins?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    editor: Editor\n    type: PMType\n    parent: ParentConfig<Config>['addProseMirrorPlugins']\n  }) => Plugin[]\n\n  /**\n   * This function adds additional extensions to the editor. This is useful for\n   * building extension kits.\n   * @example\n   * addExtensions() {\n   *   return [\n   *     BulletList,\n   *     OrderedList,\n   *     ListItem\n   *   ]\n   * }\n   */\n  addExtensions?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    parent: ParentConfig<Config>['addExtensions']\n  }) => Extensions\n\n  /**\n   * This function extends the schema of the node.\n   * @example\n   * extendNodeSchema() {\n   *   return {\n   *     group: 'inline',\n   *     selectable: false,\n   *   }\n   * }\n   */\n  extendNodeSchema?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<Config>['extendNodeSchema']\n        },\n        extension: Node,\n      ) => Record<string, any>)\n    | null\n\n  /**\n   * This function extends the schema of the mark.\n   * @example\n   * extendMarkSchema() {\n   *   return {\n   *     group: 'inline',\n   *     selectable: false,\n   *   }\n   * }\n   */\n  extendMarkSchema?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<Config>['extendMarkSchema']\n        },\n        extension: Mark,\n      ) => Record<string, any>)\n    | null\n\n  /**\n   * The editor is not ready yet.\n   */\n  onBeforeCreate?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: PMType\n          parent: ParentConfig<Config>['onBeforeCreate']\n        },\n        event: EditorEvents['beforeCreate'],\n      ) => void)\n    | null\n\n  /**\n   * The editor is ready.\n   */\n  onCreate?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: PMType\n          parent: ParentConfig<Config>['onCreate']\n        },\n        event: EditorEvents['create'],\n      ) => void)\n    | null\n\n  /**\n   * The content has changed.\n   */\n  onUpdate?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: PMType\n          parent: ParentConfig<Config>['onUpdate']\n        },\n        event: EditorEvents['update'],\n      ) => void)\n    | null\n\n  /**\n   * The selection has changed.\n   */\n  onSelectionUpdate?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: PMType\n          parent: ParentConfig<Config>['onSelectionUpdate']\n        },\n        event: EditorEvents['selectionUpdate'],\n      ) => void)\n    | null\n\n  /**\n   * The editor state has changed.\n   */\n  onTransaction?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: PMType\n          parent: ParentConfig<Config>['onTransaction']\n        },\n        event: EditorEvents['transaction'],\n      ) => void)\n    | null\n\n  /**\n   * The editor is focused.\n   */\n  onFocus?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: PMType\n          parent: ParentConfig<Config>['onFocus']\n        },\n        event: EditorEvents['focus'],\n      ) => void)\n    | null\n\n  /**\n   * The editor isn’t focused anymore.\n   */\n  onBlur?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: PMType\n          parent: ParentConfig<Config>['onBlur']\n        },\n        event: EditorEvents['blur'],\n      ) => void)\n    | null\n\n  /**\n   * The editor is destroyed.\n   */\n  onDestroy?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: PMType\n          parent: ParentConfig<Config>['onDestroy']\n        },\n        event: EditorEvents['destroy'],\n      ) => void)\n    | null\n}\n\nexport class Extendable<\n  Options = any,\n  Storage = any,\n  Config = ExtensionConfig<Options, Storage> | NodeConfig<Options, Storage> | MarkConfig<Options, Storage>,\n> {\n  type = 'extendable'\n  parent: Extendable | null = null\n\n  child: Extendable | null = null\n\n  name = ''\n\n  config: Config = {\n    name: this.name,\n  } as Config\n\n  constructor(config: Partial<Config> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = (this.config as any).name\n  }\n\n  get options(): Options {\n    return {\n      ...(callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this as any, 'addOptions', {\n          name: this.name,\n        }),\n      ) || {}),\n    }\n  }\n\n  get storage(): Readonly<Storage> {\n    return {\n      ...(callOrReturn(\n        getExtensionField<AnyConfig['addStorage']>(this as any, 'addStorage', {\n          name: this.name,\n          options: this.options,\n        }),\n      ) || {}),\n    }\n  }\n\n  configure(options: Partial<Options> = {}) {\n    const extension = this.extend<Options, Storage, Config>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    extension.name = this.name\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<\n    ExtendedOptions = Options,\n    ExtendedStorage = Storage,\n    ExtendedConfig =\n      | ExtensionConfig<ExtendedOptions, ExtendedStorage>\n      | NodeConfig<ExtendedOptions, ExtendedStorage>\n      | MarkConfig<ExtendedOptions, ExtendedStorage>,\n  >(extendedConfig: Partial<ExtendedConfig> = {}): Extendable<ExtendedOptions, ExtendedStorage> {\n    const extension = new (this.constructor as any)({ ...this.config, ...extendedConfig })\n\n    extension.parent = this\n    this.child = extension\n    extension.name = 'name' in extendedConfig ? extendedConfig.name : extension.parent.name\n\n    return extension\n  }\n}\n", "import type { DOMOutputSpec, Mark as ProseMirrorMark, MarkSpec, MarkType } from '@tiptap/pm/model'\n\nimport type { Editor } from './Editor.js'\nimport type { ExtendableConfig } from './Extendable.js'\nimport { Extendable } from './Extendable.js'\nimport type { Attributes, MarkViewRenderer, ParentConfig } from './types.js'\n\nexport interface MarkConfig<Options = any, Storage = any>\n  extends ExtendableConfig<Options, Storage, MarkConfig<Options, Storage>, MarkType> {\n  /**\n   * Mark View\n   */\n  addMarkView?:\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        editor: Editor\n        type: MarkType\n        parent: ParentConfig<MarkConfig<Options, Storage>>['addMarkView']\n      }) => MarkViewRenderer)\n    | null\n\n  /**\n   * Keep mark after split node\n   */\n  keepOnSplit?: boolean | (() => boolean)\n\n  /**\n   * Inclusive\n   */\n  inclusive?:\n    | MarkSpec['inclusive']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<MarkConfig<Options, Storage>>['inclusive']\n        editor?: Editor\n      }) => MarkSpec['inclusive'])\n\n  /**\n   * Excludes\n   */\n  excludes?:\n    | MarkSpec['excludes']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<MarkConfig<Options, Storage>>['excludes']\n        editor?: Editor\n      }) => MarkSpec['excludes'])\n\n  /**\n   * Marks this Mark as exitable\n   */\n  exitable?: boolean | (() => boolean)\n\n  /**\n   * Group\n   */\n  group?:\n    | MarkSpec['group']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<MarkConfig<Options, Storage>>['group']\n        editor?: Editor\n      }) => MarkSpec['group'])\n\n  /**\n   * Spanning\n   */\n  spanning?:\n    | MarkSpec['spanning']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<MarkConfig<Options, Storage>>['spanning']\n        editor?: Editor\n      }) => MarkSpec['spanning'])\n\n  /**\n   * Code\n   */\n  code?:\n    | boolean\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<MarkConfig<Options, Storage>>['code']\n        editor?: Editor\n      }) => boolean)\n\n  /**\n   * Parse HTML\n   */\n  parseHTML?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    parent: ParentConfig<MarkConfig<Options, Storage>>['parseHTML']\n    editor?: Editor\n  }) => MarkSpec['parseDOM']\n\n  /**\n   * Render HTML\n   */\n  renderHTML?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['renderHTML']\n          editor?: Editor\n        },\n        props: {\n          mark: ProseMirrorMark\n          HTMLAttributes: Record<string, any>\n        },\n      ) => DOMOutputSpec)\n    | null\n\n  /**\n   * Attributes\n   */\n  addAttributes?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    parent: ParentConfig<MarkConfig<Options, Storage>>['addAttributes']\n    editor?: Editor\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n  }) => Attributes | {}\n}\n\n/**\n * The Mark class is used to create custom mark extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Mark<Options = any, Storage = any> extends Extendable<Options, Storage, MarkConfig<Options, Storage>> {\n  type = 'mark'\n\n  /**\n   * Create a new Mark instance\n   * @param config - Mark configuration object or a function that returns a configuration object\n   */\n  static create<O = any, S = any>(config: Partial<MarkConfig<O, S>> | (() => Partial<MarkConfig<O, S>>) = {}) {\n    // If the config is a function, execute it to get the configuration object\n    const resolvedConfig = typeof config === 'function' ? config() : config\n    return new Mark<O, S>(resolvedConfig)\n  }\n\n  static handleExit({ editor, mark }: { editor: Editor; mark: Mark }) {\n    const { tr } = editor.state\n    const currentPos = editor.state.selection.$from\n    const isAtEnd = currentPos.pos === currentPos.end()\n\n    if (isAtEnd) {\n      const currentMarks = currentPos.marks()\n      const isInMark = !!currentMarks.find(m => m?.type.name === mark.name)\n\n      if (!isInMark) {\n        return false\n      }\n\n      const removeMark = currentMarks.find(m => m?.type.name === mark.name)\n\n      if (removeMark) {\n        tr.removeStoredMark(removeMark)\n      }\n      tr.insertText(' ', currentPos.pos)\n\n      editor.view.dispatch(tr)\n\n      return true\n    }\n\n    return false\n  }\n\n  configure(options?: Partial<Options>) {\n    return super.configure(options) as Mark<Options, Storage>\n  }\n\n  extend<\n    ExtendedOptions = Options,\n    ExtendedStorage = Storage,\n    ExtendedConfig = MarkConfig<ExtendedOptions, ExtendedStorage>,\n  >(\n    extendedConfig?:\n      | (() => Partial<ExtendedConfig>)\n      | (Partial<ExtendedConfig> &\n          ThisType<{\n            name: string\n            options: ExtendedOptions\n            storage: ExtendedStorage\n            editor: Editor\n            type: MarkType\n          }>),\n  ): Mark<ExtendedOptions, ExtendedStorage> {\n    // If the extended config is a function, execute it to get the configuration object\n    const resolvedConfig = typeof extendedConfig === 'function' ? extendedConfig() : extendedConfig\n    return super.extend(resolvedConfig) as Mark<ExtendedOptions, ExtendedStorage>\n  }\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Fragment } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\nimport { Plugin } from '@tiptap/pm/state'\n\nimport { CommandManager } from './CommandManager.js'\nimport type { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport type { CanCommands, ChainedCommands, ExtendedRegExpMatchArray, Range, SingleCommands } from './types.js'\nimport { isNumber } from './utilities/isNumber.js'\nimport { isRegExp } from './utilities/isRegExp.js'\n\nexport type PasteRuleMatch = {\n  index: number\n  text: string\n  replaceWith?: string\n  match?: RegExpMatchArray\n  data?: Record<string, any>\n}\n\nexport type PasteRuleFinder =\n  | RegExp\n  | ((text: string, event?: ClipboardEvent | null) => PasteRuleMatch[] | null | undefined)\n\n/**\n * Paste rules are used to react to pasted content.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport class PasteRule {\n  find: PasteRuleFinder\n\n  handler: (props: {\n    state: EditorState\n    range: Range\n    match: ExtendedRegExpMatchArray\n    commands: SingleCommands\n    chain: () => ChainedCommands\n    can: () => CanCommands\n    pasteEvent: ClipboardEvent | null\n    dropEvent: DragEvent | null\n  }) => void | null\n\n  constructor(config: {\n    find: PasteRuleFinder\n    handler: (props: {\n      can: () => CanCommands\n      chain: () => ChainedCommands\n      commands: SingleCommands\n      dropEvent: DragEvent | null\n      match: ExtendedRegExpMatchArray\n      pasteEvent: ClipboardEvent | null\n      range: Range\n      state: EditorState\n    }) => void | null\n  }) {\n    this.find = config.find\n    this.handler = config.handler\n  }\n}\n\nconst pasteRuleMatcherHandler = (\n  text: string,\n  find: PasteRuleFinder,\n  event?: ClipboardEvent | null,\n): ExtendedRegExpMatchArray[] => {\n  if (isRegExp(find)) {\n    return [...text.matchAll(find)]\n  }\n\n  const matches = find(text, event)\n\n  if (!matches) {\n    return []\n  }\n\n  return matches.map(pasteRuleMatch => {\n    const result: ExtendedRegExpMatchArray = [pasteRuleMatch.text]\n\n    result.index = pasteRuleMatch.index\n    result.input = text\n    result.data = pasteRuleMatch.data\n\n    if (pasteRuleMatch.replaceWith) {\n      if (!pasteRuleMatch.text.includes(pasteRuleMatch.replaceWith)) {\n        console.warn('[tiptap warn]: \"pasteRuleMatch.replaceWith\" must be part of \"pasteRuleMatch.text\".')\n      }\n\n      result.push(pasteRuleMatch.replaceWith)\n    }\n\n    return result\n  })\n}\n\nfunction run(config: {\n  editor: Editor\n  state: EditorState\n  from: number\n  to: number\n  rule: PasteRule\n  pasteEvent: ClipboardEvent | null\n  dropEvent: DragEvent | null\n}): boolean {\n  const { editor, state, from, to, rule, pasteEvent, dropEvent } = config\n\n  const { commands, chain, can } = new CommandManager({\n    editor,\n    state,\n  })\n\n  const handlers: (void | null)[] = []\n\n  state.doc.nodesBetween(from, to, (node, pos) => {\n    // Skip code blocks and non-textual nodes.\n    // Be defensive: `node` may be a Fragment without a `type`. Only text,\n    // inline, or textblock nodes are processed by paste rules.\n    if (node.type?.spec?.code || !(node.isText || node.isTextblock || node.isInline)) {\n      return\n    }\n\n    // For textblock and inline/text nodes, compute the range relative to the node.\n    // Prefer `node.nodeSize` when available (some Node shapes expose this),\n    // otherwise fall back to `node.content?.size`. Default to 0 if neither exists.\n    const contentSize = node.content?.size ?? node.nodeSize ?? 0\n    const resolvedFrom = Math.max(from, pos)\n    const resolvedTo = Math.min(to, pos + contentSize)\n\n    // If the resolved range is empty or invalid for this node, skip it. This\n    // avoids calling `textBetween` with start > end which can cause internal\n    // Fragment/Node traversal to access undefined `nodeSize` values.\n    if (resolvedFrom >= resolvedTo) {\n      return\n    }\n\n    const textToMatch = node.isText\n      ? node.text || ''\n      : node.textBetween(resolvedFrom - pos, resolvedTo - pos, undefined, '\\ufffc')\n\n    const matches = pasteRuleMatcherHandler(textToMatch, rule.find, pasteEvent)\n\n    matches.forEach(match => {\n      if (match.index === undefined) {\n        return\n      }\n\n      const start = resolvedFrom + match.index + 1\n      const end = start + match[0].length\n      const range = {\n        from: state.tr.mapping.map(start),\n        to: state.tr.mapping.map(end),\n      }\n\n      const handler = rule.handler({\n        state,\n        range,\n        match,\n        commands,\n        chain,\n        can,\n        pasteEvent,\n        dropEvent,\n      })\n\n      handlers.push(handler)\n    })\n  })\n\n  const success = handlers.every(handler => handler !== null)\n\n  return success\n}\n\n// When dragging across editors, must get another editor instance to delete selection content.\nlet tiptapDragFromOtherEditor: Editor | null = null\n\nconst createClipboardPasteEvent = (text: string) => {\n  const event = new ClipboardEvent('paste', {\n    clipboardData: new DataTransfer(),\n  })\n\n  event.clipboardData?.setData('text/html', text)\n\n  return event\n}\n\n/**\n * Create an paste rules plugin. When enabled, it will cause pasted\n * text that matches any of the given rules to trigger the rule’s\n * action.\n */\nexport function pasteRulesPlugin(props: { editor: Editor; rules: PasteRule[] }): Plugin[] {\n  const { editor, rules } = props\n  let dragSourceElement: Element | null = null\n  let isPastedFromProseMirror = false\n  let isDroppedFromProseMirror = false\n  let pasteEvent = typeof ClipboardEvent !== 'undefined' ? new ClipboardEvent('paste') : null\n  let dropEvent: DragEvent | null\n\n  try {\n    dropEvent = typeof DragEvent !== 'undefined' ? new DragEvent('drop') : null\n  } catch {\n    dropEvent = null\n  }\n\n  const processEvent = ({\n    state,\n    from,\n    to,\n    rule,\n    pasteEvt,\n  }: {\n    state: EditorState\n    from: number\n    to: { b: number }\n    rule: PasteRule\n    pasteEvt: ClipboardEvent | null\n  }) => {\n    const tr = state.tr\n    const chainableState = createChainableState({\n      state,\n      transaction: tr,\n    })\n\n    const handler = run({\n      editor,\n      state: chainableState,\n      from: Math.max(from - 1, 0),\n      to: to.b - 1,\n      rule,\n      pasteEvent: pasteEvt,\n      dropEvent,\n    })\n\n    if (!handler || !tr.steps.length) {\n      return\n    }\n\n    try {\n      dropEvent = typeof DragEvent !== 'undefined' ? new DragEvent('drop') : null\n    } catch {\n      dropEvent = null\n    }\n    pasteEvent = typeof ClipboardEvent !== 'undefined' ? new ClipboardEvent('paste') : null\n\n    return tr\n  }\n\n  const plugins = rules.map(rule => {\n    return new Plugin({\n      // we register a global drag handler to track the current drag source element\n      view(view) {\n        const handleDragstart = (event: DragEvent) => {\n          dragSourceElement = view.dom.parentElement?.contains(event.target as Element) ? view.dom.parentElement : null\n\n          if (dragSourceElement) {\n            tiptapDragFromOtherEditor = editor\n          }\n        }\n\n        const handleDragend = () => {\n          if (tiptapDragFromOtherEditor) {\n            tiptapDragFromOtherEditor = null\n          }\n        }\n\n        window.addEventListener('dragstart', handleDragstart)\n        window.addEventListener('dragend', handleDragend)\n\n        return {\n          destroy() {\n            window.removeEventListener('dragstart', handleDragstart)\n            window.removeEventListener('dragend', handleDragend)\n          },\n        }\n      },\n\n      props: {\n        handleDOMEvents: {\n          drop: (view, event: Event) => {\n            isDroppedFromProseMirror = dragSourceElement === view.dom.parentElement\n            dropEvent = event as DragEvent\n\n            if (!isDroppedFromProseMirror) {\n              const dragFromOtherEditor = tiptapDragFromOtherEditor\n\n              if (dragFromOtherEditor?.isEditable) {\n                // setTimeout to avoid the wrong content after drop, timeout arg can't be empty or 0\n                setTimeout(() => {\n                  const selection = dragFromOtherEditor.state.selection\n\n                  if (selection) {\n                    dragFromOtherEditor.commands.deleteRange({ from: selection.from, to: selection.to })\n                  }\n                }, 10)\n              }\n            }\n            return false\n          },\n\n          paste: (_view, event: Event) => {\n            const html = (event as ClipboardEvent).clipboardData?.getData('text/html')\n\n            pasteEvent = event as ClipboardEvent\n\n            isPastedFromProseMirror = !!html?.includes('data-pm-slice')\n\n            return false\n          },\n        },\n      },\n\n      appendTransaction: (transactions, oldState, state) => {\n        const transaction = transactions[0]\n        const isPaste = transaction.getMeta('uiEvent') === 'paste' && !isPastedFromProseMirror\n        const isDrop = transaction.getMeta('uiEvent') === 'drop' && !isDroppedFromProseMirror\n\n        // if PasteRule is triggered by insertContent()\n        const simulatedPasteMeta = transaction.getMeta('applyPasteRules') as\n          | undefined\n          | { from: number; text: string | ProseMirrorNode | Fragment }\n        const isSimulatedPaste = !!simulatedPasteMeta\n\n        if (!isPaste && !isDrop && !isSimulatedPaste) {\n          return\n        }\n\n        // Handle simulated paste\n        if (isSimulatedPaste) {\n          let { text } = simulatedPasteMeta\n\n          if (typeof text === 'string') {\n            text = text as string\n          } else {\n            text = getHTMLFromFragment(Fragment.from(text), state.schema)\n          }\n\n          const { from } = simulatedPasteMeta\n          const to = from + text.length\n\n          const pasteEvt = createClipboardPasteEvent(text)\n\n          return processEvent({\n            rule,\n            state,\n            from,\n            to: { b: to },\n            pasteEvt,\n          })\n        }\n\n        // handle actual paste/drop\n        const from = oldState.doc.content.findDiffStart(state.doc.content)\n        const to = oldState.doc.content.findDiffEnd(state.doc.content)\n\n        // stop if there is no changed range\n        if (!isNumber(from) || !to || from === to.b) {\n          return\n        }\n\n        return processEvent({\n          rule,\n          state,\n          from,\n          to,\n          pasteEvt: pasteEvent,\n        })\n      },\n    })\n  })\n\n  return plugins\n}\n", "export function isNumber(value: any): value is number {\n  return typeof value === 'number'\n}\n", "export { ClipboardTextSerializer } from './clipboardTextSerializer.js'\nexport { Commands } from './commands.js'\nexport { Delete } from './delete.js'\nexport { Drop } from './drop.js'\nexport { Editable } from './editable.js'\nexport { FocusEvents, focusEventsPluginKey } from './focusEvents.js'\nexport { Keymap } from './keymap.js'\nexport { Paste } from './paste.js'\nexport { Tabindex } from './tabindex.js'\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\nimport { getTextBetween } from '../helpers/getTextBetween.js'\nimport { getTextSerializersFromSchema } from '../helpers/getTextSerializersFromSchema.js'\n\nexport type ClipboardTextSerializerOptions = {\n  blockSeparator?: string\n}\n\nexport const ClipboardTextSerializer = Extension.create<ClipboardTextSerializerOptions>({\n  name: 'clipboardTextSerializer',\n\n  addOptions() {\n    return {\n      blockSeparator: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('clipboardTextSerializer'),\n        props: {\n          clipboardTextSerializer: () => {\n            const { editor } = this\n            const { state, schema } = editor\n            const { doc, selection } = state\n            const { ranges } = selection\n            const from = Math.min(...ranges.map(range => range.$from.pos))\n            const to = Math.max(...ranges.map(range => range.$to.pos))\n            const textSerializers = getTextSerializersFromSchema(schema)\n            const range = { from, to }\n\n            return getTextBetween(doc, range, {\n              ...(this.options.blockSeparator !== undefined ? { blockSeparator: this.options.blockSeparator } : {}),\n              textSerializers,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import type { Editor } from './Editor.js'\nimport { type ExtendableConfig, Extendable } from './Extendable.js'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\nexport interface ExtensionConfig<Options = any, Storage = any>\n  extends ExtendableConfig<Options, Storage, ExtensionConfig<Options, Storage>, null> {}\n\n/**\n * The Extension class is the base class for all extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Extension<Options = any, Storage = any> extends Extendable<\n  Options,\n  Storage,\n  ExtensionConfig<Options, Storage>\n> {\n  type = 'extension'\n\n  /**\n   * Create a new Extension instance\n   * @param config - Extension configuration object or a function that returns a configuration object\n   */\n  static create<O = any, S = any>(\n    config: Partial<ExtensionConfig<O, S>> | (() => Partial<ExtensionConfig<O, S>>) = {},\n  ) {\n    // If the config is a function, execute it to get the configuration object\n    const resolvedConfig = typeof config === 'function' ? config() : config\n    return new Extension<O, S>(resolvedConfig)\n  }\n\n  configure(options?: Partial<Options>) {\n    return super.configure(options) as Extension<Options, Storage>\n  }\n\n  extend<\n    ExtendedOptions = Options,\n    ExtendedStorage = Storage,\n    ExtendedConfig = ExtensionConfig<ExtendedOptions, ExtendedStorage>,\n  >(\n    extendedConfig?:\n      | (() => Partial<ExtendedConfig>)\n      | (Partial<ExtendedConfig> &\n          ThisType<{\n            name: string\n            options: ExtendedOptions\n            storage: ExtendedStorage\n            editor: Editor\n            type: null\n          }>),\n  ): Extension<ExtendedOptions, ExtendedStorage> {\n    // If the extended config is a function, execute it to get the configuration object\n    const resolvedConfig = typeof extendedConfig === 'function' ? extendedConfig() : extendedConfig\n    return super.extend(resolvedConfig) as Extension<ExtendedOptions, ExtendedStorage>\n  }\n}\n", "export * from './blur.js'\nexport * from './clearContent.js'\nexport * from './clearNodes.js'\nexport * from './command.js'\nexport * from './createParagraphNear.js'\nexport * from './cut.js'\nexport * from './deleteCurrentNode.js'\nexport * from './deleteNode.js'\nexport * from './deleteRange.js'\nexport * from './deleteSelection.js'\nexport * from './enter.js'\nexport * from './exitCode.js'\nexport * from './extendMarkRange.js'\nexport * from './first.js'\nexport * from './focus.js'\nexport * from './forEach.js'\nexport * from './insertContent.js'\nexport * from './insertContentAt.js'\nexport * from './join.js'\nexport * from './joinItemBackward.js'\nexport * from './joinItemForward.js'\nexport * from './joinTextblockBackward.js'\nexport * from './joinTextblockForward.js'\nexport * from './keyboardShortcut.js'\nexport * from './lift.js'\nexport * from './liftEmptyBlock.js'\nexport * from './liftListItem.js'\nexport * from './newlineInCode.js'\nexport * from './resetAttributes.js'\nexport * from './scrollIntoView.js'\nexport * from './selectAll.js'\nexport * from './selectNodeBackward.js'\nexport * from './selectNodeForward.js'\nexport * from './selectParentNode.js'\nexport * from './selectTextblockEnd.js'\nexport * from './selectTextblockStart.js'\nexport * from './setContent.js'\nexport * from './setMark.js'\nexport * from './setMeta.js'\nexport * from './setNode.js'\nexport * from './setNodeSelection.js'\nexport * from './setTextSelection.js'\nexport * from './sinkListItem.js'\nexport * from './splitBlock.js'\nexport * from './splitListItem.js'\nexport * from './toggleList.js'\nexport * from './toggleMark.js'\nexport * from './toggleNode.js'\nexport * from './toggleWrap.js'\nexport * from './undoInputRule.js'\nexport * from './unsetAllMarks.js'\nexport * from './unsetMark.js'\nexport * from './updateAttributes.js'\nexport * from './wrapIn.js'\nexport * from './wrapInList.js'\n", "import type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blur: {\n      /**\n       * Removes focus from the editor.\n       * @example editor.commands.blur()\n       */\n      blur: () => ReturnType\n    }\n  }\n}\n\nexport const blur: RawCommands['blur'] =\n  () =>\n  ({ editor, view }) => {\n    requestAnimationFrame(() => {\n      if (!editor.isDestroyed) {\n        ;(view.dom as HTMLElement).blur()\n\n        // Browsers should remove the caret on blur but safari does not.\n        // See: https://github.com/ueberdosis/tiptap/issues/2405\n        window?.getSelection()?.removeAllRanges()\n      }\n    })\n\n    return true\n  }\n", "import type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    clearContent: {\n      /**\n       * Clear the whole document.\n       * @example editor.commands.clearContent()\n       */\n      clearContent: (\n        /**\n         * Whether to emit an update event.\n         * @default true\n         */\n        emitUpdate?: boolean,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const clearContent: RawCommands['clearContent'] =\n  (emitUpdate = true) =>\n  ({ commands }) => {\n    return commands.setContent('', { emitUpdate })\n  }\n", "import { liftTarget } from '@tiptap/pm/transform'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    clearNodes: {\n      /**\n       * Normalize nodes to a simple paragraph.\n       * @example editor.commands.clearNodes()\n       */\n      clearNodes: () => ReturnType\n    }\n  }\n}\n\nexport const clearNodes: RawCommands['clearNodes'] =\n  () =>\n  ({ state, tr, dispatch }) => {\n    const { selection } = tr\n    const { ranges } = selection\n\n    if (!dispatch) {\n      return true\n    }\n\n    ranges.forEach(({ $from, $to }) => {\n      state.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n        if (node.type.isText) {\n          return\n        }\n\n        const { doc, mapping } = tr\n        const $mappedFrom = doc.resolve(mapping.map(pos))\n        const $mappedTo = doc.resolve(mapping.map(pos + node.nodeSize))\n        const nodeRange = $mappedFrom.blockRange($mappedTo)\n\n        if (!nodeRange) {\n          return\n        }\n\n        const targetLiftDepth = liftTarget(nodeRange)\n\n        if (node.type.isTextblock) {\n          const { defaultType } = $mappedFrom.parent.contentMatchAt($mappedFrom.index())\n\n          tr.setNodeMarkup(nodeRange.start, defaultType)\n        }\n\n        if (targetLiftDepth || targetLiftDepth === 0) {\n          tr.lift(nodeRange, targetLiftDepth)\n        }\n      })\n    })\n\n    return true\n  }\n", "import type { Command, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    command: {\n      /**\n       * Define a command inline.\n       * @param fn The command function.\n       * @example\n       * editor.commands.command(({ tr, state }) => {\n       *   ...\n       *   return true\n       * })\n       */\n      command: (fn: (props: Parameters<Command>[0]) => boolean) => ReturnType\n    }\n  }\n}\n\nexport const command: RawCommands['command'] = fn => props => {\n  return fn(props)\n}\n", "import { createParagraphNear as originalCreateParagraph<PERSON>ear } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    createParagraphNear: {\n      /**\n       * Create a paragraph nearby.\n       * @example editor.commands.createParagraphNear()\n       */\n      createParagraphNear: () => ReturnType\n    }\n  }\n}\n\nexport const createParagraphNear: RawCommands['createParagraphNear'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalCreateParagraphNear(state, dispatch)\n  }\n", "import { TextSelection } from '@tiptap/pm/state'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    cut: {\n      /**\n       * Cuts content from a range and inserts it at a given position.\n       * @param range The range to cut.\n       * @param range.from The start position of the range.\n       * @param range.to The end position of the range.\n       * @param targetPos The position to insert the content at.\n       * @example editor.commands.cut({ from: 1, to: 3 }, 5)\n       */\n      cut: ({ from, to }: { from: number; to: number }, targetPos: number) => ReturnType\n    }\n  }\n}\n\nexport const cut: RawCommands['cut'] =\n  (originRange, targetPos) =>\n  ({ editor, tr }) => {\n    const { state } = editor\n\n    const contentSlice = state.doc.slice(originRange.from, originRange.to)\n\n    tr.deleteRange(originRange.from, originRange.to)\n    const newPos = tr.mapping.map(targetPos)\n\n    tr.insert(newPos, contentSlice.content)\n\n    tr.setSelection(new TextSelection(tr.doc.resolve(Math.max(newPos - 1, 0))))\n\n    return true\n  }\n", "import type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteCurrentNode: {\n      /**\n       * Delete the node that currently has the selection anchor.\n       * @example editor.commands.deleteCurrentNode()\n       */\n      deleteCurrentNode: () => ReturnType\n    }\n  }\n}\n\nexport const deleteCurrentNode: RawCommands['deleteCurrentNode'] =\n  () =>\n  ({ tr, dispatch }) => {\n    const { selection } = tr\n    const currentNode = selection.$anchor.node()\n\n    // if there is content inside the current node, break out of this command\n    if (currentNode.content.size > 0) {\n      return false\n    }\n\n    const $pos = tr.selection.$anchor\n\n    for (let depth = $pos.depth; depth > 0; depth -= 1) {\n      const node = $pos.node(depth)\n\n      if (node.type === currentNode.type) {\n        if (dispatch) {\n          const from = $pos.before(depth)\n          const to = $pos.after(depth)\n\n          tr.delete(from, to).scrollIntoView()\n        }\n\n        return true\n      }\n    }\n\n    return false\n  }\n", "import type { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteNode: {\n      /**\n       * Delete a node with a given type or name.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.deleteNode('paragraph')\n       */\n      deleteNode: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const deleteNode: RawCommands['deleteNode'] =\n  typeOrName =>\n  ({ tr, state, dispatch }) => {\n    const type = getNodeType(typeOrName, state.schema)\n    const $pos = tr.selection.$anchor\n\n    for (let depth = $pos.depth; depth > 0; depth -= 1) {\n      const node = $pos.node(depth)\n\n      if (node.type === type) {\n        if (dispatch) {\n          const from = $pos.before(depth)\n          const to = $pos.after(depth)\n\n          tr.delete(from, to).scrollIntoView()\n        }\n\n        return true\n      }\n    }\n\n    return false\n  }\n", "import type { Range, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteRange: {\n      /**\n       * Delete a given range.\n       * @param range The range to delete.\n       * @example editor.commands.deleteRange({ from: 1, to: 3 })\n       */\n      deleteRange: (range: Range) => ReturnType\n    }\n  }\n}\n\nexport const deleteRange: RawCommands['deleteRange'] =\n  range =>\n  ({ tr, dispatch }) => {\n    const { from, to } = range\n\n    if (dispatch) {\n      tr.delete(from, to)\n    }\n\n    return true\n  }\n", "import { deleteSelection as originalDeleteSelection } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteSelection: {\n      /**\n       * Delete the selection, if there is one.\n       * @example editor.commands.deleteSelection()\n       */\n      deleteSelection: () => ReturnType\n    }\n  }\n}\n\nexport const deleteSelection: RawCommands['deleteSelection'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalDeleteSelection(state, dispatch)\n  }\n", "import type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    enter: {\n      /**\n       * Trigger enter.\n       * @example editor.commands.enter()\n       */\n      enter: () => ReturnType\n    }\n  }\n}\n\nexport const enter: RawCommands['enter'] =\n  () =>\n  ({ commands }) => {\n    return commands.keyboardShortcut('Enter')\n  }\n", "import { exitCode as originalExitCode } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    exitCode: {\n      /**\n       * Exit from a code block.\n       * @example editor.commands.exitCode()\n       */\n      exitCode: () => ReturnType\n    }\n  }\n}\n\nexport const exitCode: RawCommands['exitCode'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalExitCode(state, dispatch)\n  }\n", "import type { MarkType } from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\n\nimport { getMarkRange } from '../helpers/getMarkRange.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    extendMarkRange: {\n      /**\n       * Extends the text selection to the current mark by type or name.\n       * @param typeOrName The type or name of the mark.\n       * @param attributes The attributes of the mark.\n       * @example editor.commands.extendMarkRange('bold')\n       * @example editor.commands.extendMarkRange('mention', { userId: \"1\" })\n       */\n      extendMarkRange: (\n        /**\n         * The type or name of the mark.\n         */\n        typeOrName: string | MarkType,\n\n        /**\n         * The attributes of the mark.\n         */\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const extendMarkRange: RawCommands['extendMarkRange'] =\n  (typeOrName, attributes = {}) =>\n  ({ tr, state, dispatch }) => {\n    const type = getMarkType(typeOrName, state.schema)\n    const { doc, selection } = tr\n    const { $from, from, to } = selection\n\n    if (dispatch) {\n      const range = getMarkRange($from, type, attributes)\n\n      if (range && range.from <= from && range.to >= to) {\n        const newSelection = TextSelection.create(doc, range.from, range.to)\n\n        tr.setSelection(newSelection)\n      }\n    }\n\n    return true\n  }\n", "import type { Command, CommandProps, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    first: {\n      /**\n       * Runs one command after the other and stops at the first which returns true.\n       * @param commands The commands to run.\n       * @example editor.commands.first([command1, command2])\n       */\n      first: (commands: Command[] | ((props: CommandProps) => Command[])) => ReturnType\n    }\n  }\n}\n\nexport const first: RawCommands['first'] = commands => props => {\n  const items = typeof commands === 'function' ? commands(props) : commands\n\n  for (let i = 0; i < items.length; i += 1) {\n    if (items[i](props)) {\n      return true\n    }\n  }\n\n  return false\n}\n", "export function isAndroid(): boolean {\n  return navigator.platform === 'Android' || /android/i.test(navigator.userAgent)\n}\n", "export function isiOS(): boolean {\n  return (\n    ['iPad Simulator', 'iPhone Simulator', 'iPod Simulator', 'iPad', 'iPhone', 'iPod'].includes(navigator.platform) ||\n    // iPad on iOS 13 detection\n    (navigator.userAgent.includes('Mac') && 'ontouchend' in document)\n  )\n}\n", "import { isTextSelection } from '../helpers/isTextSelection.js'\nimport { resolveFocusPosition } from '../helpers/resolveFocusPosition.js'\nimport type { FocusPosition, RawCommands } from '../types.js'\nimport { isAndroid } from '../utilities/isAndroid.js'\nimport { isiOS } from '../utilities/isiOS.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    focus: {\n      /**\n       * Focus the editor at the given position.\n       * @param position The position to focus at.\n       * @param options.scrollIntoView Scroll the focused position into view after focusing\n       * @example editor.commands.focus()\n       * @example editor.commands.focus(32, { scrollIntoView: false })\n       */\n      focus: (\n        /**\n         * The position to focus at.\n         */\n        position?: FocusPosition,\n\n        /**\n         * Optional options\n         * @default { scrollIntoView: true }\n         */\n        options?: {\n          scrollIntoView?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const focus: RawCommands['focus'] =\n  (position = null, options = {}) =>\n  ({ editor, view, tr, dispatch }) => {\n    options = {\n      scrollIntoView: true,\n      ...options,\n    }\n\n    const delayedFocus = () => {\n      // focus within `requestAnimationFrame` breaks focus on iOS and Android\n      // so we have to call this\n      if (isiOS() || isAndroid()) {\n        ;(view.dom as HTMLElement).focus()\n      }\n\n      // For React we have to focus asynchronously. Otherwise wild things happen.\n      // see: https://github.com/ueberdosis/tiptap/issues/1520\n      requestAnimationFrame(() => {\n        if (!editor.isDestroyed) {\n          view.focus()\n\n          if (options?.scrollIntoView) {\n            editor.commands.scrollIntoView()\n          }\n        }\n      })\n    }\n\n    if ((view.hasFocus() && position === null) || position === false) {\n      return true\n    }\n\n    // we don’t try to resolve a NodeSelection or CellSelection\n    if (dispatch && position === null && !isTextSelection(editor.state.selection)) {\n      delayedFocus()\n      return true\n    }\n\n    // pass through tr.doc instead of editor.state.doc\n    // since transactions could change the editors state before this command has been run\n    const selection = resolveFocusPosition(tr.doc, position) || editor.state.selection\n    const isSameSelection = editor.state.selection.eq(selection)\n\n    if (dispatch) {\n      if (!isSameSelection) {\n        tr.setSelection(selection)\n      }\n\n      // `tr.setSelection` resets the stored marks\n      // so we’ll restore them if the selection is the same as before\n      if (isSameSelection && tr.storedMarks) {\n        tr.setStoredMarks(tr.storedMarks)\n      }\n\n      delayedFocus()\n    }\n\n    return true\n  }\n", "import type { CommandProps, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    forEach: {\n      /**\n       * Loop through an array of items.\n       */\n      forEach: <T>(\n        items: T[],\n        fn: (\n          item: T,\n          props: CommandProps & {\n            index: number\n          },\n        ) => boolean,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const forEach: RawCommands['forEach'] = (items, fn) => props => {\n  return items.every((item, index) => fn(item, { ...props, index }))\n}\n", "import type { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport type { Content, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    insertContent: {\n      /**\n       * Insert a node or string of HTML at the current position.\n       * @example editor.commands.insertContent('<h1>Example</h1>')\n       * @example editor.commands.insertContent('<h1>Example</h1>', { updateSelection: false })\n       */\n      insertContent: (\n        /**\n         * The ProseMirror content to insert.\n         */\n        value: Content | ProseMirrorNode | Fragment,\n\n        /**\n         * Optional options\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           */\n          parseOptions?: ParseOptions\n\n          /**\n           * Whether to update the selection after inserting the content.\n           */\n          updateSelection?: boolean\n          applyInputRules?: boolean\n          applyPasteRules?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const insertContent: RawCommands['insertContent'] =\n  (value, options) =>\n  ({ tr, commands }) => {\n    return commands.insertContentAt({ from: tr.selection.from, to: tr.selection.to }, value, options)\n  }\n", "import type { Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\nimport { Fragment } from '@tiptap/pm/model'\n\nimport { createNodeFromContent } from '../helpers/createNodeFromContent.js'\nimport { selectionToInsertionEnd } from '../helpers/selectionToInsertionEnd.js'\nimport type { Content, Range, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    insertContentAt: {\n      /**\n       * Insert a node or string of HTML at a specific position.\n       * @example editor.commands.insertContentAt(0, '<h1>Example</h1>')\n       */\n      insertContentAt: (\n        /**\n         * The position to insert the content at.\n         */\n        position: number | Range,\n\n        /**\n         * The ProseMirror content to insert.\n         */\n        value: Content | ProseMirrorNode | Fragment,\n\n        /**\n         * Optional options\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           */\n          parseOptions?: ParseOptions\n\n          /**\n           * Whether to update the selection after inserting the content.\n           */\n          updateSelection?: boolean\n\n          /**\n           * Whether to apply input rules after inserting the content.\n           */\n          applyInputRules?: boolean\n\n          /**\n           * Whether to apply paste rules after inserting the content.\n           */\n          applyPasteRules?: boolean\n\n          /**\n           * Whether to throw an error if the content is invalid.\n           */\n          errorOnInvalidContent?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nconst isFragment = (nodeOrFragment: ProseMirrorNode | Fragment): nodeOrFragment is Fragment => {\n  return !('type' in nodeOrFragment)\n}\n\nexport const insertContentAt: RawCommands['insertContentAt'] =\n  (position, value, options) =>\n  ({ tr, dispatch, editor }) => {\n    if (dispatch) {\n      options = {\n        parseOptions: editor.options.parseOptions,\n        updateSelection: true,\n        applyInputRules: false,\n        applyPasteRules: false,\n        ...options,\n      }\n\n      let content: Fragment | ProseMirrorNode\n      const { selection } = editor.state\n\n      const emitContentError = (error: Error) => {\n        editor.emit('contentError', {\n          editor,\n          error,\n          disableCollaboration: () => {\n            if (\n              'collaboration' in editor.storage &&\n              typeof editor.storage.collaboration === 'object' &&\n              editor.storage.collaboration\n            ) {\n              ;(editor.storage.collaboration as any).isDisabled = true\n            }\n          },\n        })\n      }\n\n      const parseOptions: ParseOptions = {\n        preserveWhitespace: 'full',\n        ...options.parseOptions,\n      }\n\n      // If `emitContentError` is enabled, we want to check the content for errors\n      // but ignore them (do not remove the invalid content from the document)\n      if (!options.errorOnInvalidContent && !editor.options.enableContentCheck && editor.options.emitContentError) {\n        try {\n          createNodeFromContent(value, editor.schema, {\n            parseOptions,\n            errorOnInvalidContent: true,\n          })\n        } catch (e) {\n          emitContentError(e as Error)\n        }\n      }\n\n      try {\n        content = createNodeFromContent(value, editor.schema, {\n          parseOptions,\n          errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n        })\n      } catch (e) {\n        emitContentError(e as Error)\n        return false\n      }\n\n      let { from, to } =\n        typeof position === 'number' ? { from: position, to: position } : { from: position.from, to: position.to }\n\n      let isOnlyTextContent = true\n      let isOnlyBlockContent = true\n      const nodes = isFragment(content) ? content : [content]\n\n      nodes.forEach(node => {\n        // check if added node is valid\n        node.check()\n\n        isOnlyTextContent = isOnlyTextContent ? node.isText && node.marks.length === 0 : false\n\n        isOnlyBlockContent = isOnlyBlockContent ? node.isBlock : false\n      })\n\n      // check if we can replace the wrapping node by\n      // the newly inserted content\n      // example:\n      // replace an empty paragraph by an inserted image\n      // instead of inserting the image below the paragraph\n      if (from === to && isOnlyBlockContent) {\n        const { parent } = tr.doc.resolve(from)\n        const isEmptyTextBlock = parent.isTextblock && !parent.type.spec.code && !parent.childCount\n\n        if (isEmptyTextBlock) {\n          from -= 1\n          to += 1\n        }\n      }\n\n      let newContent\n\n      // if there is only plain text we have to use `insertText`\n      // because this will keep the current marks\n      if (isOnlyTextContent) {\n        // if value is string, we can use it directly\n        // otherwise if it is an array, we have to join it\n        if (Array.isArray(value)) {\n          newContent = value.map(v => v.text || '').join('')\n        } else if (value instanceof Fragment) {\n          let text = ''\n\n          value.forEach(node => {\n            if (node.text) {\n              text += node.text\n            }\n          })\n\n          newContent = text\n        } else if (typeof value === 'object' && !!value && !!value.text) {\n          newContent = value.text\n        } else {\n          newContent = value as string\n        }\n\n        tr.insertText(newContent, from, to)\n      } else {\n        newContent = content\n\n        const fromSelectionAtStart = selection.$from.parentOffset === 0\n        const isTextSelection = selection.$from.node().isText || selection.$from.node().isTextblock\n        const hasContent = selection.$from.node().content.size > 0\n\n        if (fromSelectionAtStart && isTextSelection && hasContent) {\n          from = Math.max(0, from - 1)\n        }\n\n        tr.replaceWith(from, to, newContent)\n      }\n\n      // set cursor at end of inserted content\n      if (options.updateSelection) {\n        selectionToInsertionEnd(tr, tr.steps.length - 1, -1)\n      }\n\n      if (options.applyInputRules) {\n        tr.setMeta('applyInputRules', { from, text: newContent })\n      }\n\n      if (options.applyPasteRules) {\n        tr.setMeta('applyPasteRules', { from, text: newContent })\n      }\n    }\n\n    return true\n  }\n", "import {\n  joinBack<PERSON> as original<PERSON>oi<PERSON><PERSON><PERSON><PERSON>,\n  joinDown as original<PERSON>oinDown,\n  joinForward as original<PERSON>oin<PERSON><PERSON><PERSON>,\n  joinUp as original<PERSON>oinUp,\n} from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinUp: {\n      /**\n       * Join the selected block or, if there is a text selection, the closest ancestor block of the selection that can be joined, with the sibling above it.\n       * @example editor.commands.joinUp()\n       */\n      joinUp: () => ReturnType\n    }\n    joinDown: {\n      /**\n       * Join the selected block, or the closest ancestor of the selection that can be joined, with the sibling after it.\n       * @example editor.commands.joinDown()\n       */\n      joinDown: () => ReturnType\n    }\n    joinBackward: {\n      /**\n       * If the selection is empty and at the start of a textblock, try to reduce the distance between that block and the one before it—if there's a block directly before it that can be joined, join them.\n       * If not, try to move the selected block closer to the next one in the document structure by lifting it out of its\n       * parent or moving it into a parent of the previous block. Will use the view for accurate (bidi-aware) start-of-textblock detection if given.\n       * @example editor.commands.joinBackward()\n       */\n      joinBackward: () => ReturnType\n    }\n    joinForward: {\n      /**\n       * If the selection is empty and the cursor is at the end of a textblock, try to reduce or remove the boundary between that block and the one after it,\n       * either by joining them or by moving the other block closer to this one in the tree structure.\n       * Will use the view for accurate start-of-textblock detection if given.\n       * @example editor.commands.joinForward()\n       */\n      joinForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinUp: RawCommands['joinUp'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalJoinUp(state, dispatch)\n  }\n\nexport const joinDown: RawCommands['joinDown'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalJoinDown(state, dispatch)\n  }\n\nexport const joinBackward: RawCommands['joinBackward'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalJoinBackward(state, dispatch)\n  }\n\nexport const joinForward: RawCommands['joinForward'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalJoinForward(state, dispatch)\n  }\n", "import { joinPoint } from '@tiptap/pm/transform'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinItemBackward: {\n      /**\n       * Join two items backward.\n       * @example editor.commands.joinItemBackward()\n       */\n      joinItemBackward: () => ReturnType\n    }\n  }\n}\n\nexport const joinItemBackward: RawCommands['joinItemBackward'] =\n  () =>\n  ({ state, dispatch, tr }) => {\n    try {\n      const point = joinPoint(state.doc, state.selection.$from.pos, -1)\n\n      if (point === null || point === undefined) {\n        return false\n      }\n\n      tr.join(point, 2)\n\n      if (dispatch) {\n        dispatch(tr)\n      }\n\n      return true\n    } catch {\n      return false\n    }\n  }\n", "import { joinPoint } from '@tiptap/pm/transform'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinItemForward: {\n      /**\n       * Join two items Forwards.\n       * @example editor.commands.joinItemForward()\n       */\n      joinItemForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinItemForward: RawCommands['joinItemForward'] =\n  () =>\n  ({ state, dispatch, tr }) => {\n    try {\n      const point = joinPoint(state.doc, state.selection.$from.pos, +1)\n\n      if (point === null || point === undefined) {\n        return false\n      }\n\n      tr.join(point, 2)\n\n      if (dispatch) {\n        dispatch(tr)\n      }\n\n      return true\n    } catch {\n      return false\n    }\n  }\n", "import { joinTextblockBackward as originalCommand } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinTextblockBackward: {\n      /**\n       * A more limited form of joinBackward that only tries to join the current textblock to the one before it, if the cursor is at the start of a textblock.\n       */\n      joinTextblockBackward: () => ReturnType\n    }\n  }\n}\n\nexport const joinTextblockBackward: RawCommands['joinTextblockBackward'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalCommand(state, dispatch)\n  }\n", "import { joinTextblockForward as originalCommand } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinTextblockForward: {\n      /**\n       * A more limited form of joinForward that only tries to join the current textblock to the one after it, if the cursor is at the end of a textblock.\n       */\n      joinTextblockForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinTextblockForward: RawCommands['joinTextblockForward'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalCommand(state, dispatch)\n  }\n", "export function isMacOS(): boolean {\n  return typeof navigator !== 'undefined' ? /Mac/.test(navigator.platform) : false\n}\n", "import type { RawCommands } from '../types.js'\nimport { isiOS } from '../utilities/isiOS.js'\nimport { isMacOS } from '../utilities/isMacOS.js'\n\nfunction normalizeKeyName(name: string) {\n  const parts = name.split(/-(?!$)/)\n  let result = parts[parts.length - 1]\n\n  if (result === 'Space') {\n    result = ' '\n  }\n\n  let alt\n  let ctrl\n  let shift\n  let meta\n\n  for (let i = 0; i < parts.length - 1; i += 1) {\n    const mod = parts[i]\n\n    if (/^(cmd|meta|m)$/i.test(mod)) {\n      meta = true\n    } else if (/^a(lt)?$/i.test(mod)) {\n      alt = true\n    } else if (/^(c|ctrl|control)$/i.test(mod)) {\n      ctrl = true\n    } else if (/^s(hift)?$/i.test(mod)) {\n      shift = true\n    } else if (/^mod$/i.test(mod)) {\n      if (isiOS() || isMacOS()) {\n        meta = true\n      } else {\n        ctrl = true\n      }\n    } else {\n      throw new Error(`Unrecognized modifier name: ${mod}`)\n    }\n  }\n\n  if (alt) {\n    result = `Alt-${result}`\n  }\n\n  if (ctrl) {\n    result = `Ctrl-${result}`\n  }\n\n  if (meta) {\n    result = `Meta-${result}`\n  }\n\n  if (shift) {\n    result = `Shift-${result}`\n  }\n\n  return result\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    keyboardShortcut: {\n      /**\n       * Trigger a keyboard shortcut.\n       * @param name The name of the keyboard shortcut.\n       * @example editor.commands.keyboardShortcut('Mod-b')\n       */\n      keyboardShortcut: (name: string) => ReturnType\n    }\n  }\n}\n\nexport const keyboardShortcut: RawCommands['keyboardShortcut'] =\n  name =>\n  ({ editor, view, tr, dispatch }) => {\n    const keys = normalizeKeyName(name).split(/-(?!$)/)\n    const key = keys.find(item => !['Alt', 'Ctrl', 'Meta', 'Shift'].includes(item))\n    const event = new KeyboardEvent('keydown', {\n      key: key === 'Space' ? ' ' : key,\n      altKey: keys.includes('Alt'),\n      ctrlKey: keys.includes('Ctrl'),\n      metaKey: keys.includes('Meta'),\n      shiftKey: keys.includes('Shift'),\n      bubbles: true,\n      cancelable: true,\n    })\n\n    const capturedTransaction = editor.captureTransaction(() => {\n      view.someProp('handleKeyDown', f => f(view, event))\n    })\n\n    capturedTransaction?.steps.forEach(step => {\n      const newStep = step.map(tr.mapping)\n\n      if (newStep && dispatch) {\n        tr.maybeStep(newStep)\n      }\n    })\n\n    return true\n  }\n", "import { lift as originalLift } from '@tiptap/pm/commands'\nimport type { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    lift: {\n      /**\n       * Removes an existing wrap if possible lifting the node out of it\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.lift('paragraph')\n       * @example editor.commands.lift('heading', { level: 1 })\n       */\n      lift: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const lift: RawCommands['lift'] =\n  (typeOrName, attributes = {}) =>\n  ({ state, dispatch }) => {\n    const type = getNodeType(typeOrName, state.schema)\n    const isActive = isNodeActive(state, type, attributes)\n\n    if (!isActive) {\n      return false\n    }\n\n    return originalLift(state, dispatch)\n  }\n", "import { liftEmptyBlock as originalLiftEmptyBlock } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    liftEmptyBlock: {\n      /**\n       * If the cursor is in an empty textblock that can be lifted, lift the block.\n       * @example editor.commands.liftEmptyBlock()\n       */\n      liftEmptyBlock: () => ReturnType\n    }\n  }\n}\n\nexport const liftEmptyBlock: RawCommands['liftEmptyBlock'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalLiftEmptyBlock(state, dispatch)\n  }\n", "import type { NodeType } from '@tiptap/pm/model'\nimport { liftListItem as originalLiftListItem } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    liftListItem: {\n      /**\n       * Create a command to lift the list item around the selection up into a wrapping list.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.liftListItem('listItem')\n       */\n      liftListItem: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const liftListItem: RawCommands['liftListItem'] =\n  typeOrName =>\n  ({ state, dispatch }) => {\n    const type = getNodeType(typeOrName, state.schema)\n\n    return originalLiftListItem(type)(state, dispatch)\n  }\n", "import { newlineInCode as originalNewlineInCode } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    newlineInCode: {\n      /**\n       * Add a newline character in code.\n       * @example editor.commands.newlineInCode()\n       */\n      newlineInCode: () => ReturnType\n    }\n  }\n}\n\nexport const newlineInCode: RawCommands['newlineInCode'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalNewlineInCode(state, dispatch)\n  }\n", "/**\n * Remove a property or an array of properties from an object\n * @param obj Object\n * @param key Key to remove\n */\nexport function deleteProps(obj: Record<string, any>, propOrProps: string | string[]): Record<string, any> {\n  const props = typeof propOrProps === 'string' ? [propOrProps] : propOrProps\n\n  return Object.keys(obj).reduce((newObj: Record<string, any>, prop) => {\n    if (!props.includes(prop)) {\n      newObj[prop] = obj[prop]\n    }\n\n    return newObj\n  }, {})\n}\n", "import type { MarkType, NodeType } from '@tiptap/pm/model'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSchemaTypeNameByName } from '../helpers/getSchemaTypeNameByName.js'\nimport type { RawCommands } from '../types.js'\nimport { deleteProps } from '../utilities/deleteProps.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    resetAttributes: {\n      /**\n       * Resets some node attributes to the default value.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node to reset.\n       * @example editor.commands.resetAttributes('heading', 'level')\n       */\n      resetAttributes: (typeOrName: string | NodeType | MarkType, attributes: string | string[]) => ReturnType\n    }\n  }\n}\n\nexport const resetAttributes: RawCommands['resetAttributes'] =\n  (typeOrName, attributes) =>\n  ({ tr, state, dispatch }) => {\n    let nodeType: NodeType | null = null\n    let markType: MarkType | null = null\n\n    const schemaType = getSchemaTypeNameByName(\n      typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n      state.schema,\n    )\n\n    if (!schemaType) {\n      return false\n    }\n\n    if (schemaType === 'node') {\n      nodeType = getNodeType(typeOrName as NodeType, state.schema)\n    }\n\n    if (schemaType === 'mark') {\n      markType = getMarkType(typeOrName as MarkType, state.schema)\n    }\n\n    if (dispatch) {\n      tr.selection.ranges.forEach(range => {\n        state.doc.nodesBetween(range.$from.pos, range.$to.pos, (node, pos) => {\n          if (nodeType && nodeType === node.type) {\n            tr.setNodeMarkup(pos, undefined, deleteProps(node.attrs, attributes))\n          }\n\n          if (markType && node.marks.length) {\n            node.marks.forEach(mark => {\n              if (markType === mark.type) {\n                tr.addMark(pos, pos + node.nodeSize, markType.create(deleteProps(mark.attrs, attributes)))\n              }\n            })\n          }\n        })\n      })\n    }\n\n    return true\n  }\n", "import type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    scrollIntoView: {\n      /**\n       * Scroll the selection into view.\n       * @example editor.commands.scrollIntoView()\n       */\n      scrollIntoView: () => ReturnType\n    }\n  }\n}\n\nexport const scrollIntoView: RawCommands['scrollIntoView'] =\n  () =>\n  ({ tr, dispatch }) => {\n    if (dispatch) {\n      tr.scrollIntoView()\n    }\n\n    return true\n  }\n", "import { AllSelection } from '@tiptap/pm/state'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectAll: {\n      /**\n       * Select the whole document.\n       * @example editor.commands.selectAll()\n       */\n      selectAll: () => ReturnType\n    }\n  }\n}\n\nexport const selectAll: RawCommands['selectAll'] =\n  () =>\n  ({ tr, dispatch }) => {\n    if (dispatch) {\n      const selection = new AllSelection(tr.doc)\n\n      tr.setSelection(selection)\n    }\n\n    return true\n  }\n", "import { selectNodeBackward as originalSelectNodeBackward } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectNodeBackward: {\n      /**\n       * Select a node backward.\n       * @example editor.commands.selectNodeBackward()\n       */\n      selectNodeBackward: () => ReturnType\n    }\n  }\n}\n\nexport const selectNodeBackward: RawCommands['selectNodeBackward'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalSelectNodeBackward(state, dispatch)\n  }\n", "import { selectNodeForward as originalSelectNodeForward } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectNodeForward: {\n      /**\n       * Select a node forward.\n       * @example editor.commands.selectNodeForward()\n       */\n      selectNodeForward: () => ReturnType\n    }\n  }\n}\n\nexport const selectNodeForward: RawCommands['selectNodeForward'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalSelectNodeForward(state, dispatch)\n  }\n", "import { selectParentNode as originalSelectParentNode } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectParentNode: {\n      /**\n       * Select the parent node.\n       * @example editor.commands.selectParentNode()\n       */\n      selectParentNode: () => ReturnType\n    }\n  }\n}\n\nexport const selectParentNode: RawCommands['selectParentNode'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalSelectParentNode(state, dispatch)\n  }\n", "// @ts-ignore\n// TODO: add types to @types/prosemirror-commands\nimport { selectTextblockEnd as originalSelectTextblockEnd } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectTextblockEnd: {\n      /**\n       * Moves the cursor to the end of current text block.\n       * @example editor.commands.selectTextblockEnd()\n       */\n      selectTextblockEnd: () => ReturnType\n    }\n  }\n}\n\nexport const selectTextblockEnd: RawCommands['selectTextblockEnd'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalSelectTextblockEnd(state, dispatch)\n  }\n", "// @ts-ignore\n// TODO: add types to @types/prosemirror-commands\nimport { selectTextblockStart as originalSelectTextblockStart } from '@tiptap/pm/commands'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectTextblockStart: {\n      /**\n       * Moves the cursor to the start of current text block.\n       * @example editor.commands.selectTextblockStart()\n       */\n      selectTextblockStart: () => ReturnType\n    }\n  }\n}\n\nexport const selectTextblockStart: RawCommands['selectTextblockStart'] =\n  () =>\n  ({ state, dispatch }) => {\n    return originalSelectTextblockStart(state, dispatch)\n  }\n", "import type { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { createDocument } from '../helpers/createDocument.js'\nimport type { Content, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setContent: {\n      /**\n       * Replace the whole document with new content.\n       * @param content The new content.\n       * @param emitUpdate Whether to emit an update event.\n       * @param parseOptions Options for parsing the content.\n       * @example editor.commands.setContent('<p>Example text</p>')\n       */\n      setContent: (\n        /**\n         * The new content.\n         */\n        content: Content | Fragment | ProseMirrorNode,\n\n        /**\n         * Options for `setContent`.\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           * @default {}\n           */\n          parseOptions?: ParseOptions\n\n          /**\n           * Whether to throw an error if the content is invalid.\n           */\n          errorOnInvalidContent?: boolean\n\n          /**\n           * Whether to emit an update event.\n           * @default true\n           */\n          emitUpdate?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const setContent: RawCommands['setContent'] =\n  (content, { errorOnInvalidContent, emitUpdate = true, parseOptions = {} } = {}) =>\n  ({ editor, tr, dispatch, commands }) => {\n    const { doc } = tr\n\n    // This is to keep backward compatibility with the previous behavior\n    // TODO remove this in the next major version\n    if (parseOptions.preserveWhitespace !== 'full') {\n      const document = createDocument(content, editor.schema, parseOptions, {\n        errorOnInvalidContent: errorOnInvalidContent ?? editor.options.enableContentCheck,\n      })\n\n      if (dispatch) {\n        tr.replaceWith(0, doc.content.size, document).setMeta('preventUpdate', !emitUpdate)\n      }\n      return true\n    }\n\n    if (dispatch) {\n      tr.setMeta('preventUpdate', !emitUpdate)\n    }\n\n    return commands.insertContentAt({ from: 0, to: doc.content.size }, content, {\n      parseOptions,\n      errorOnInvalidContent: errorOnInvalidContent ?? editor.options.enableContentCheck,\n    })\n  }\n", "import type { MarkType, ResolvedPos } from '@tiptap/pm/model'\nimport type { EditorState, Transaction } from '@tiptap/pm/state'\n\nimport { getMarkAttributes } from '../helpers/getMarkAttributes.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { isTextSelection } from '../helpers/index.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setMark: {\n      /**\n       * Add a mark with new attributes.\n       * @param typeOrName The mark type or name.\n       * @example editor.commands.setMark('bold', { level: 1 })\n       */\n      setMark: (typeOrName: string | MarkType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nfunction canSetMark(state: EditorState, tr: Transaction, newMarkType: MarkType) {\n  const { selection } = tr\n  let cursor: ResolvedPos | null = null\n\n  if (isTextSelection(selection)) {\n    cursor = selection.$cursor\n  }\n\n  if (cursor) {\n    const currentMarks = state.storedMarks ?? cursor.marks()\n    const parentAllowsMarkType = cursor.parent.type.allowsMarkType(newMarkType)\n\n    // There can be no current marks that exclude the new mark, and the parent must allow this mark type\n    return (\n      parentAllowsMarkType &&\n      (!!newMarkType.isInSet(currentMarks) || !currentMarks.some(mark => mark.type.excludes(newMarkType)))\n    )\n  }\n\n  const { ranges } = selection\n\n  return ranges.some(({ $from, $to }) => {\n    let someNodeSupportsMark =\n      $from.depth === 0 ? state.doc.inlineContent && state.doc.type.allowsMarkType(newMarkType) : false\n\n    state.doc.nodesBetween($from.pos, $to.pos, (node, _pos, parent) => {\n      // If we already found a mark that we can enable, return false to bypass the remaining search\n      if (someNodeSupportsMark) {\n        return false\n      }\n\n      if (node.isInline) {\n        const parentAllowsMarkType = !parent || parent.type.allowsMarkType(newMarkType)\n        const currentMarksAllowMarkType =\n          !!newMarkType.isInSet(node.marks) || !node.marks.some(otherMark => otherMark.type.excludes(newMarkType))\n\n        someNodeSupportsMark = parentAllowsMarkType && currentMarksAllowMarkType\n      }\n      return !someNodeSupportsMark\n    })\n\n    return someNodeSupportsMark\n  })\n}\nexport const setMark: RawCommands['setMark'] =\n  (typeOrName, attributes = {}) =>\n  ({ tr, state, dispatch }) => {\n    const { selection } = tr\n    const { empty, ranges } = selection\n    const type = getMarkType(typeOrName, state.schema)\n\n    if (dispatch) {\n      if (empty) {\n        const oldAttributes = getMarkAttributes(state, type)\n\n        tr.addStoredMark(\n          type.create({\n            ...oldAttributes,\n            ...attributes,\n          }),\n        )\n      } else {\n        ranges.forEach(range => {\n          const from = range.$from.pos\n          const to = range.$to.pos\n\n          state.doc.nodesBetween(from, to, (node, pos) => {\n            const trimmedFrom = Math.max(pos, from)\n            const trimmedTo = Math.min(pos + node.nodeSize, to)\n            const someHasMark = node.marks.find(mark => mark.type === type)\n\n            // if there is already a mark of this type\n            // we know that we have to merge its attributes\n            // otherwise we add a fresh new mark\n            if (someHasMark) {\n              node.marks.forEach(mark => {\n                if (type === mark.type) {\n                  tr.addMark(\n                    trimmedFrom,\n                    trimmedTo,\n                    type.create({\n                      ...mark.attrs,\n                      ...attributes,\n                    }),\n                  )\n                }\n              })\n            } else {\n              tr.addMark(trimmedFrom, trimmedTo, type.create(attributes))\n            }\n          })\n        })\n      }\n    }\n\n    return canSetMark(state, tr, type)\n  }\n", "import type { Plug<PERSON>, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setMeta: {\n      /**\n       * Store a metadata property in the current transaction.\n       * @param key The key of the metadata property.\n       * @param value The value to store.\n       * @example editor.commands.setMeta('foo', 'bar')\n       */\n      setMeta: (key: string | Plugin | PluginKey, value: any) => ReturnType\n    }\n  }\n}\n\nexport const setMeta: RawCommands['setMeta'] =\n  (key, value) =>\n  ({ tr }) => {\n    tr.setMeta(key, value)\n\n    return true\n  }\n", "import { setBlockType } from '@tiptap/pm/commands'\nimport type { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setNode: {\n      /**\n       * Replace a given range with a node.\n       * @param typeOrName The type or name of the node\n       * @param attributes The attributes of the node\n       * @example editor.commands.setNode('paragraph')\n       */\n      setNode: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const setNode: RawCommands['setNode'] =\n  (typeOrName, attributes = {}) =>\n  ({ state, dispatch, chain }) => {\n    const type = getNodeType(typeOrName, state.schema)\n\n    let attributesToCopy: Record<string, any> | undefined\n\n    if (state.selection.$anchor.sameParent(state.selection.$head)) {\n      // only copy attributes if the selection is pointing to a node of the same type\n      attributesToCopy = state.selection.$anchor.parent.attrs\n    }\n\n    // TODO: use a fallback like insertContent?\n    if (!type.isTextblock) {\n      console.warn('[tiptap warn]: Currently \"setNode()\" only supports text block nodes.')\n\n      return false\n    }\n\n    return (\n      chain()\n        // try to convert node to default node if needed\n        .command(({ commands }) => {\n          const canSetBlock = setBlockType(type, { ...attributesToCopy, ...attributes })(state)\n\n          if (canSetBlock) {\n            return true\n          }\n\n          return commands.clearNodes()\n        })\n        .command(({ state: updatedState }) => {\n          return setBlockType(type, { ...attributesToCopy, ...attributes })(updatedState, dispatch)\n        })\n        .run()\n    )\n  }\n", "import { NodeSelection } from '@tiptap/pm/state'\n\nimport type { RawCommands } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setNodeSelection: {\n      /**\n       * Creates a NodeSelection.\n       * @param position - Position of the node.\n       * @example editor.commands.setNodeSelection(10)\n       */\n      setNodeSelection: (position: number) => ReturnType\n    }\n  }\n}\n\nexport const setNodeSelection: RawCommands['setNodeSelection'] =\n  position =>\n  ({ tr, dispatch }) => {\n    if (dispatch) {\n      const { doc } = tr\n      const from = minMax(position, 0, doc.content.size)\n      const selection = NodeSelection.create(doc, from)\n\n      tr.setSelection(selection)\n    }\n\n    return true\n  }\n", "import { TextSelection } from '@tiptap/pm/state'\n\nimport type { Range, RawCommands } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setTextSelection: {\n      /**\n       * Creates a TextSelection.\n       * @param position The position of the selection.\n       * @example editor.commands.setTextSelection(10)\n       */\n      setTextSelection: (position: number | Range) => ReturnType\n    }\n  }\n}\n\nexport const setTextSelection: RawCommands['setTextSelection'] =\n  position =>\n  ({ tr, dispatch }) => {\n    if (dispatch) {\n      const { doc } = tr\n      const { from, to } = typeof position === 'number' ? { from: position, to: position } : position\n      const minPos = TextSelection.atStart(doc).from\n      const maxPos = TextSelection.atEnd(doc).to\n      const resolvedFrom = minMax(from, minPos, maxPos)\n      const resolvedEnd = minMax(to, minPos, maxPos)\n      const selection = TextSelection.create(doc, resolvedFrom, resolvedEnd)\n\n      tr.setSelection(selection)\n    }\n\n    return true\n  }\n", "import type { NodeType } from '@tiptap/pm/model'\nimport { sinkListItem as originalSinkListItem } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    sinkListItem: {\n      /**\n       * Sink the list item down into an inner list.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.sinkListItem('listItem')\n       */\n      sinkListItem: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const sinkListItem: RawCommands['sinkListItem'] =\n  typeOrName =>\n  ({ state, dispatch }) => {\n    const type = getNodeType(typeOrName, state.schema)\n\n    return originalSinkListItem(type)(state, dispatch)\n  }\n", "import type { EditorState } from '@tiptap/pm/state'\nimport { NodeSelection, TextSelection } from '@tiptap/pm/state'\nimport { canSplit } from '@tiptap/pm/transform'\n\nimport { defaultBlockAt } from '../helpers/defaultBlockAt.js'\nimport { getSplittedAttributes } from '../helpers/getSplittedAttributes.js'\nimport type { RawCommands } from '../types.js'\n\nfunction ensureMarks(state: EditorState, splittableMarks?: string[]) {\n  const marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks())\n\n  if (marks) {\n    const filteredMarks = marks.filter(mark => splittableMarks?.includes(mark.type.name))\n\n    state.tr.ensureMarks(filteredMarks)\n  }\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    splitBlock: {\n      /**\n       * Forks a new node from an existing node.\n       * @param options.keepMarks Keep marks from the previous node.\n       * @example editor.commands.splitBlock()\n       * @example editor.commands.splitBlock({ keepMarks: true })\n       */\n      splitBlock: (options?: { keepMarks?: boolean }) => ReturnType\n    }\n  }\n}\n\nexport const splitBlock: RawCommands['splitBlock'] =\n  ({ keepMarks = true } = {}) =>\n  ({ tr, state, dispatch, editor }) => {\n    const { selection, doc } = tr\n    const { $from, $to } = selection\n    const extensionAttributes = editor.extensionManager.attributes\n    const newAttributes = getSplittedAttributes(extensionAttributes, $from.node().type.name, $from.node().attrs)\n\n    if (selection instanceof NodeSelection && selection.node.isBlock) {\n      if (!$from.parentOffset || !canSplit(doc, $from.pos)) {\n        return false\n      }\n\n      if (dispatch) {\n        if (keepMarks) {\n          ensureMarks(state, editor.extensionManager.splittableMarks)\n        }\n\n        tr.split($from.pos).scrollIntoView()\n      }\n\n      return true\n    }\n\n    if (!$from.parent.isBlock) {\n      return false\n    }\n\n    const atEnd = $to.parentOffset === $to.parent.content.size\n\n    const deflt = $from.depth === 0 ? undefined : defaultBlockAt($from.node(-1).contentMatchAt($from.indexAfter(-1)))\n\n    let types =\n      atEnd && deflt\n        ? [\n            {\n              type: deflt,\n              attrs: newAttributes,\n            },\n          ]\n        : undefined\n\n    let can = canSplit(tr.doc, tr.mapping.map($from.pos), 1, types)\n\n    if (!types && !can && canSplit(tr.doc, tr.mapping.map($from.pos), 1, deflt ? [{ type: deflt }] : undefined)) {\n      can = true\n      types = deflt\n        ? [\n            {\n              type: deflt,\n              attrs: newAttributes,\n            },\n          ]\n        : undefined\n    }\n\n    if (dispatch) {\n      if (can) {\n        if (selection instanceof TextSelection) {\n          tr.deleteSelection()\n        }\n\n        tr.split(tr.mapping.map($from.pos), 1, types)\n\n        if (deflt && !atEnd && !$from.parentOffset && $from.parent.type !== deflt) {\n          const first = tr.mapping.map($from.before())\n          const $first = tr.doc.resolve(first)\n\n          if ($from.node(-1).canReplaceWith($first.index(), $first.index() + 1, deflt)) {\n            tr.setNodeMarkup(tr.mapping.map($from.before()), deflt)\n          }\n        }\n      }\n\n      if (keepMarks) {\n        ensureMarks(state, editor.extensionManager.splittableMarks)\n      }\n\n      tr.scrollIntoView()\n    }\n\n    return can\n  }\n", "import type { Node as ProseMirrorNode, NodeType } from '@tiptap/pm/model'\nimport { Fragment, Slice } from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\nimport { canSplit } from '@tiptap/pm/transform'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSplittedAttributes } from '../helpers/getSplittedAttributes.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    splitListItem: {\n      /**\n       * Splits one list item into two list items.\n       * @param typeOrName The type or name of the node.\n       * @param overrideAttrs The attributes to ensure on the new node.\n       * @example editor.commands.splitListItem('listItem')\n       */\n      splitListItem: (typeOrName: string | NodeType, overrideAttrs?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const splitListItem: RawCommands['splitListItem'] =\n  (typeOrName, overrideAttrs = {}) =>\n  ({ tr, state, dispatch, editor }) => {\n    const type = getNodeType(typeOrName, state.schema)\n    const { $from, $to } = state.selection\n\n    // @ts-ignore\n    // eslint-disable-next-line\n    const node: ProseMirrorNode = state.selection.node\n\n    if ((node && node.isBlock) || $from.depth < 2 || !$from.sameParent($to)) {\n      return false\n    }\n\n    const grandParent = $from.node(-1)\n\n    if (grandParent.type !== type) {\n      return false\n    }\n\n    const extensionAttributes = editor.extensionManager.attributes\n\n    if ($from.parent.content.size === 0 && $from.node(-1).childCount === $from.indexAfter(-1)) {\n      // In an empty block. If this is a nested list, the wrapping\n      // list item should be split. Otherwise, bail out and let next\n      // command handle lifting.\n      if ($from.depth === 2 || $from.node(-3).type !== type || $from.index(-2) !== $from.node(-2).childCount - 1) {\n        return false\n      }\n\n      if (dispatch) {\n        let wrap = Fragment.empty\n        // eslint-disable-next-line\n        const depthBefore = $from.index(-1) ? 1 : $from.index(-2) ? 2 : 3\n\n        // Build a fragment containing empty versions of the structure\n        // from the outer list item to the parent node of the cursor\n        for (let d = $from.depth - depthBefore; d >= $from.depth - 3; d -= 1) {\n          wrap = Fragment.from($from.node(d).copy(wrap))\n        }\n\n        const depthAfter =\n          // eslint-disable-next-line no-nested-ternary\n          $from.indexAfter(-1) < $from.node(-2).childCount\n            ? 1\n            : $from.indexAfter(-2) < $from.node(-3).childCount\n              ? 2\n              : 3\n\n        // Add a second list item with an empty default start node\n        const newNextTypeAttributes = {\n          ...getSplittedAttributes(extensionAttributes, $from.node().type.name, $from.node().attrs),\n          ...overrideAttrs,\n        }\n        const nextType = type.contentMatch.defaultType?.createAndFill(newNextTypeAttributes) || undefined\n\n        wrap = wrap.append(Fragment.from(type.createAndFill(null, nextType) || undefined))\n\n        const start = $from.before($from.depth - (depthBefore - 1))\n\n        tr.replace(start, $from.after(-depthAfter), new Slice(wrap, 4 - depthBefore, 0))\n\n        let sel = -1\n\n        tr.doc.nodesBetween(start, tr.doc.content.size, (n, pos) => {\n          if (sel > -1) {\n            return false\n          }\n\n          if (n.isTextblock && n.content.size === 0) {\n            sel = pos + 1\n          }\n        })\n\n        if (sel > -1) {\n          tr.setSelection(TextSelection.near(tr.doc.resolve(sel)))\n        }\n\n        tr.scrollIntoView()\n      }\n\n      return true\n    }\n\n    const nextType = $to.pos === $from.end() ? grandParent.contentMatchAt(0).defaultType : null\n\n    const newTypeAttributes = {\n      ...getSplittedAttributes(extensionAttributes, grandParent.type.name, grandParent.attrs),\n      ...overrideAttrs,\n    }\n    const newNextTypeAttributes = {\n      ...getSplittedAttributes(extensionAttributes, $from.node().type.name, $from.node().attrs),\n      ...overrideAttrs,\n    }\n\n    tr.delete($from.pos, $to.pos)\n\n    const types = nextType\n      ? [\n          { type, attrs: newTypeAttributes },\n          { type: nextType, attrs: newNextTypeAttributes },\n        ]\n      : [{ type, attrs: newTypeAttributes }]\n\n    if (!canSplit(tr.doc, $from.pos, 2)) {\n      return false\n    }\n\n    if (dispatch) {\n      const { selection, storedMarks } = state\n      const { splittableMarks } = editor.extensionManager\n      const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n      tr.split($from.pos, 2, types).scrollIntoView()\n\n      if (!marks || !dispatch) {\n        return true\n      }\n\n      const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n      tr.ensureMarks(filteredMarks)\n    }\n\n    return true\n  }\n", "import type { NodeType } from '@tiptap/pm/model'\nimport type { Transaction } from '@tiptap/pm/state'\nimport { canJoin } from '@tiptap/pm/transform'\n\nimport { findParentNode } from '../helpers/findParentNode.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isList } from '../helpers/isList.js'\nimport type { RawCommands } from '../types.js'\n\nconst joinListBackwards = (tr: Transaction, listType: NodeType): boolean => {\n  const list = findParentNode(node => node.type === listType)(tr.selection)\n\n  if (!list) {\n    return true\n  }\n\n  const before = tr.doc.resolve(Math.max(0, list.pos - 1)).before(list.depth)\n\n  if (before === undefined) {\n    return true\n  }\n\n  const nodeBefore = tr.doc.nodeAt(before)\n  const canJoinBackwards = list.node.type === nodeBefore?.type && canJoin(tr.doc, list.pos)\n\n  if (!canJoinBackwards) {\n    return true\n  }\n\n  tr.join(list.pos)\n\n  return true\n}\n\nconst joinListForwards = (tr: Transaction, listType: NodeType): boolean => {\n  const list = findParentNode(node => node.type === listType)(tr.selection)\n\n  if (!list) {\n    return true\n  }\n\n  const after = tr.doc.resolve(list.start).after(list.depth)\n\n  if (after === undefined) {\n    return true\n  }\n\n  const nodeAfter = tr.doc.nodeAt(after)\n  const canJoinForwards = list.node.type === nodeAfter?.type && canJoin(tr.doc, after)\n\n  if (!canJoinForwards) {\n    return true\n  }\n\n  tr.join(after)\n\n  return true\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleList: {\n      /**\n       * Toggle between different list types.\n       * @param listTypeOrName The type or name of the list.\n       * @param itemTypeOrName The type or name of the list item.\n       * @param keepMarks Keep marks when toggling.\n       * @param attributes Attributes for the new list.\n       * @example editor.commands.toggleList('bulletList', 'listItem')\n       */\n      toggleList: (\n        listTypeOrName: string | NodeType,\n        itemTypeOrName: string | NodeType,\n        keepMarks?: boolean,\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleList: RawCommands['toggleList'] =\n  (listTypeOrName, itemTypeOrName, keepMarks, attributes = {}) =>\n  ({ editor, tr, state, dispatch, chain, commands, can }) => {\n    const { extensions, splittableMarks } = editor.extensionManager\n    const listType = getNodeType(listTypeOrName, state.schema)\n    const itemType = getNodeType(itemTypeOrName, state.schema)\n    const { selection, storedMarks } = state\n    const { $from, $to } = selection\n    const range = $from.blockRange($to)\n\n    const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n    if (!range) {\n      return false\n    }\n\n    const parentList = findParentNode(node => isList(node.type.name, extensions))(selection)\n\n    if (range.depth >= 1 && parentList && range.depth - parentList.depth <= 1) {\n      // remove list\n      if (parentList.node.type === listType) {\n        return commands.liftListItem(itemType)\n      }\n\n      // change list type\n      if (isList(parentList.node.type.name, extensions) && listType.validContent(parentList.node.content) && dispatch) {\n        return chain()\n          .command(() => {\n            tr.setNodeMarkup(parentList.pos, listType)\n\n            return true\n          })\n          .command(() => joinListBackwards(tr, listType))\n          .command(() => joinListForwards(tr, listType))\n          .run()\n      }\n    }\n    if (!keepMarks || !marks || !dispatch) {\n      return (\n        chain()\n          // try to convert node to default node if needed\n          .command(() => {\n            const canWrapInList = can().wrapInList(listType, attributes)\n\n            if (canWrapInList) {\n              return true\n            }\n\n            return commands.clearNodes()\n          })\n          .wrapInList(listType, attributes)\n          .command(() => joinListBackwards(tr, listType))\n          .command(() => joinListForwards(tr, listType))\n          .run()\n      )\n    }\n\n    return (\n      chain()\n        // try to convert node to default node if needed\n        .command(() => {\n          const canWrapInList = can().wrapInList(listType, attributes)\n\n          const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n          tr.ensureMarks(filteredMarks)\n\n          if (canWrapInList) {\n            return true\n          }\n\n          return commands.clearNodes()\n        })\n        .wrapInList(listType, attributes)\n        .command(() => joinListBackwards(tr, listType))\n        .command(() => joinListForwards(tr, listType))\n        .run()\n    )\n  }\n", "import type { MarkType } from '@tiptap/pm/model'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { isMarkActive } from '../helpers/isMarkActive.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleMark: {\n      /**\n       * Toggle a mark on and off.\n       * @param typeOrName The mark type or name.\n       * @param attributes The attributes of the mark.\n       * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.\n       * @example editor.commands.toggleMark('bold')\n       */\n      toggleMark: (\n        /**\n         * The mark type or name.\n         */\n        typeOrName: string | MarkType,\n\n        /**\n         * The attributes of the mark.\n         */\n        attributes?: Record<string, any>,\n\n        options?: {\n          /**\n           * Removes the mark even across the current selection. Defaults to `false`.\n           */\n          extendEmptyMarkRange?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleMark: RawCommands['toggleMark'] =\n  (typeOrName, attributes = {}, options = {}) =>\n  ({ state, commands }) => {\n    const { extendEmptyMarkRange = false } = options\n    const type = getMarkType(typeOrName, state.schema)\n    const isActive = isMarkActive(state, type, attributes)\n\n    if (isActive) {\n      return commands.unsetMark(type, { extendEmptyMarkRange })\n    }\n\n    return commands.setMark(type, attributes)\n  }\n", "import type { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleNode: {\n      /**\n       * Toggle a node with another node.\n       * @param typeOrName The type or name of the node.\n       * @param toggleTypeOrName The type or name of the node to toggle.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.toggleNode('heading', 'paragraph')\n       */\n      toggleNode: (\n        typeOrName: string | NodeType,\n        toggleTypeOrName: string | NodeType,\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleNode: RawCommands['toggleNode'] =\n  (typeOrName, toggleTypeOrName, attributes = {}) =>\n  ({ state, commands }) => {\n    const type = getNodeType(typeOrName, state.schema)\n    const toggleType = getNodeType(toggleTypeOrName, state.schema)\n    const isActive = isNodeActive(state, type, attributes)\n\n    let attributesToCopy: Record<string, any> | undefined\n\n    if (state.selection.$anchor.sameParent(state.selection.$head)) {\n      // only copy attributes if the selection is pointing to a node of the same type\n      attributesToCopy = state.selection.$anchor.parent.attrs\n    }\n\n    if (isActive) {\n      return commands.setNode(toggleType, attributesToCopy)\n    }\n\n    // If the node is not active, we want to set the new node type with the given attributes\n    // Copying over the attributes from the current node if the selection is pointing to a node of the same type\n    return commands.setNode(type, { ...attributesToCopy, ...attributes })\n  }\n", "import type { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleWrap: {\n      /**\n       * Wraps nodes in another node, or removes an existing wrap.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.toggleWrap('blockquote')\n       */\n      toggleWrap: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const toggleWrap: RawCommands['toggleWrap'] =\n  (typeOrName, attributes = {}) =>\n  ({ state, commands }) => {\n    const type = getNodeType(typeOrName, state.schema)\n    const isActive = isNodeActive(state, type, attributes)\n\n    if (isActive) {\n      return commands.lift(type)\n    }\n\n    return commands.wrapIn(type, attributes)\n  }\n", "import type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    undoInputRule: {\n      /**\n       * Undo an input rule.\n       * @example editor.commands.undoInputRule()\n       */\n      undoInputRule: () => ReturnType\n    }\n  }\n}\n\nexport const undoInputRule: RawCommands['undoInputRule'] =\n  () =>\n  ({ state, dispatch }) => {\n    const plugins = state.plugins\n\n    for (let i = 0; i < plugins.length; i += 1) {\n      const plugin = plugins[i]\n      let undoable\n\n      // @ts-ignore\n      // eslint-disable-next-line\n      if (plugin.spec.isInputRules && (undoable = plugin.getState(state))) {\n        if (dispatch) {\n          const tr = state.tr\n          const toUndo = undoable.transform\n\n          for (let j = toUndo.steps.length - 1; j >= 0; j -= 1) {\n            tr.step(toUndo.steps[j].invert(toUndo.docs[j]))\n          }\n\n          if (undoable.text) {\n            const marks = tr.doc.resolve(undoable.from).marks()\n\n            tr.replaceWith(undoable.from, undoable.to, state.schema.text(undoable.text, marks))\n          } else {\n            tr.delete(undoable.from, undoable.to)\n          }\n        }\n\n        return true\n      }\n    }\n\n    return false\n  }\n", "import type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    unsetAllMarks: {\n      /**\n       * Remove all marks in the current selection.\n       * @example editor.commands.unsetAllMarks()\n       */\n      unsetAllMarks: () => ReturnType\n    }\n  }\n}\n\nexport const unsetAllMarks: RawCommands['unsetAllMarks'] =\n  () =>\n  ({ tr, dispatch }) => {\n    const { selection } = tr\n    const { empty, ranges } = selection\n\n    if (empty) {\n      return true\n    }\n\n    if (dispatch) {\n      ranges.forEach(range => {\n        tr.removeMark(range.$from.pos, range.$to.pos)\n      })\n    }\n\n    return true\n  }\n", "import type { MarkType } from '@tiptap/pm/model'\n\nimport { getMarkRange } from '../helpers/getMarkRange.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    unsetMark: {\n      /**\n       * Remove all marks in the current selection.\n       * @param typeOrName The mark type or name.\n       * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.\n       * @example editor.commands.unsetMark('bold')\n       */\n      unsetMark: (\n        /**\n         * The mark type or name.\n         */\n        typeOrName: string | MarkType,\n\n        options?: {\n          /**\n           * Removes the mark even across the current selection. Defaults to `false`.\n           */\n          extendEmptyMarkRange?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const unsetMark: RawCommands['unsetMark'] =\n  (typeOrName, options = {}) =>\n  ({ tr, state, dispatch }) => {\n    const { extendEmptyMarkRange = false } = options\n    const { selection } = tr\n    const type = getMarkType(typeOrName, state.schema)\n    const { $from, empty, ranges } = selection\n\n    if (!dispatch) {\n      return true\n    }\n\n    if (empty && extendEmptyMarkRange) {\n      let { from, to } = selection\n      const attrs = $from.marks().find(mark => mark.type === type)?.attrs\n      const range = getMarkRange($from, type, attrs)\n\n      if (range) {\n        from = range.from\n        to = range.to\n      }\n\n      tr.removeMark(from, to, type)\n    } else {\n      ranges.forEach(range => {\n        tr.removeMark(range.$from.pos, range.$to.pos, type)\n      })\n    }\n\n    tr.removeStoredMark(type)\n\n    return true\n  }\n", "import type { Mark, MarkType, Node, NodeType } from '@tiptap/pm/model'\nimport type { SelectionRange } from '@tiptap/pm/state'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSchemaTypeNameByName } from '../helpers/getSchemaTypeNameByName.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    updateAttributes: {\n      /**\n       * Update attributes of a node or mark.\n       * @param typeOrName The type or name of the node or mark.\n       * @param attributes The attributes of the node or mark.\n       * @example editor.commands.updateAttributes('mention', { userId: \"2\" })\n       */\n      updateAttributes: (\n        /**\n         * The type or name of the node or mark.\n         */\n        typeOrName: string | NodeType | MarkType,\n\n        /**\n         * The attributes of the node or mark.\n         */\n        attributes: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const updateAttributes: RawCommands['updateAttributes'] =\n  (typeOrName, attributes = {}) =>\n  ({ tr, state, dispatch }) => {\n    let nodeType: NodeType | null = null\n    let markType: MarkType | null = null\n\n    const schemaType = getSchemaTypeNameByName(\n      typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n      state.schema,\n    )\n\n    if (!schemaType) {\n      return false\n    }\n\n    if (schemaType === 'node') {\n      nodeType = getNodeType(typeOrName as NodeType, state.schema)\n    }\n\n    if (schemaType === 'mark') {\n      markType = getMarkType(typeOrName as MarkType, state.schema)\n    }\n\n    if (dispatch) {\n      tr.selection.ranges.forEach((range: SelectionRange) => {\n        const from = range.$from.pos\n        const to = range.$to.pos\n\n        let lastPos: number | undefined\n        let lastNode: Node | undefined\n        let trimmedFrom: number\n        let trimmedTo: number\n\n        if (tr.selection.empty) {\n          state.doc.nodesBetween(from, to, (node: Node, pos: number) => {\n            if (nodeType && nodeType === node.type) {\n              trimmedFrom = Math.max(pos, from)\n              trimmedTo = Math.min(pos + node.nodeSize, to)\n              lastPos = pos\n              lastNode = node\n            }\n          })\n        } else {\n          state.doc.nodesBetween(from, to, (node: Node, pos: number) => {\n            if (pos < from && nodeType && nodeType === node.type) {\n              trimmedFrom = Math.max(pos, from)\n              trimmedTo = Math.min(pos + node.nodeSize, to)\n              lastPos = pos\n              lastNode = node\n            }\n\n            if (pos >= from && pos <= to) {\n              if (nodeType && nodeType === node.type) {\n                tr.setNodeMarkup(pos, undefined, {\n                  ...node.attrs,\n                  ...attributes,\n                })\n              }\n\n              if (markType && node.marks.length) {\n                node.marks.forEach((mark: Mark) => {\n                  if (markType === mark.type) {\n                    const trimmedFrom2 = Math.max(pos, from)\n                    const trimmedTo2 = Math.min(pos + node.nodeSize, to)\n\n                    tr.addMark(\n                      trimmedFrom2,\n                      trimmedTo2,\n                      markType.create({\n                        ...mark.attrs,\n                        ...attributes,\n                      }),\n                    )\n                  }\n                })\n              }\n            }\n          })\n        }\n\n        if (lastNode) {\n          if (lastPos !== undefined) {\n            tr.setNodeMarkup(lastPos, undefined, {\n              ...lastNode.attrs,\n              ...attributes,\n            })\n          }\n\n          if (markType && lastNode.marks.length) {\n            lastNode.marks.forEach((mark: Mark) => {\n              if (markType === mark.type) {\n                tr.addMark(\n                  trimmedFrom,\n                  trimmedTo,\n                  markType.create({\n                    ...mark.attrs,\n                    ...attributes,\n                  }),\n                )\n              }\n            })\n          }\n        }\n      })\n    }\n\n    return true\n  }\n", "import { wrapIn as originalWrapIn } from '@tiptap/pm/commands'\nimport type { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    wrapIn: {\n      /**\n       * Wraps nodes in another node.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.wrapIn('blockquote')\n       */\n      wrapIn: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const wrapIn: RawCommands['wrapIn'] =\n  (typeOrName, attributes = {}) =>\n  ({ state, dispatch }) => {\n    const type = getNodeType(typeOrName, state.schema)\n\n    return originalWrapIn(type, attributes)(state, dispatch)\n  }\n", "import type { NodeType } from '@tiptap/pm/model'\nimport { wrapInList as originalWrapInList } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport type { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    wrapInList: {\n      /**\n       * Wrap a node in a list.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.wrapInList('bulletList')\n       */\n      wrapInList: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const wrapInList: RawCommands['wrapInList'] =\n  (typeOrName, attributes = {}) =>\n  ({ state, dispatch }) => {\n    const type = getNodeType(typeOrName, state.schema)\n\n    return originalWrapInList(type, attributes)(state, dispatch)\n  }\n", "import * as commands from '../commands/index.js'\nimport { Extension } from '../Extension.js'\n\nexport * from '../commands/index.js'\n\nexport const Commands = Extension.create({\n  name: 'commands',\n\n  addCommands() {\n    return {\n      ...commands,\n    }\n  },\n})\n", "import { RemoveMarkStep } from '@tiptap/pm/transform'\n\nimport { Extension } from '../Extension.js'\nimport { combineTransactionSteps, getChangedRanges } from '../helpers/index.js'\n\n/**\n * This extension allows you to be notified when the user deletes content you are interested in.\n */\nexport const Delete = Extension.create({\n  name: 'delete',\n\n  onUpdate({ transaction, appendedTransactions }) {\n    const callback = () => {\n      if (\n        this.editor.options.coreExtensionOptions?.delete?.filterTransaction?.(transaction) ??\n        transaction.getMeta('y-sync$')\n      ) {\n        return\n      }\n      const nextTransaction = combineTransactionSteps(transaction.before, [transaction, ...appendedTransactions])\n      const changes = getChangedRanges(nextTransaction)\n\n      changes.forEach(change => {\n        if (\n          nextTransaction.mapping.mapResult(change.oldRange.from).deletedAfter &&\n          nextTransaction.mapping.mapResult(change.oldRange.to).deletedBefore\n        ) {\n          nextTransaction.before.nodesBetween(change.oldRange.from, change.oldRange.to, (node, from) => {\n            const to = from + node.nodeSize - 2\n            const isFullyWithinRange = change.oldRange.from <= from && to <= change.oldRange.to\n\n            this.editor.emit('delete', {\n              type: 'node',\n              node,\n              from,\n              to,\n              newFrom: nextTransaction.mapping.map(from),\n              newTo: nextTransaction.mapping.map(to),\n              deletedRange: change.oldRange,\n              newRange: change.newRange,\n              partial: !isFullyWithinRange,\n              editor: this.editor,\n              transaction,\n              combinedTransform: nextTransaction,\n            })\n          })\n        }\n      })\n\n      const mapping = nextTransaction.mapping\n      nextTransaction.steps.forEach((step, index) => {\n        if (step instanceof RemoveMarkStep) {\n          const newStart = mapping.slice(index).map(step.from, -1)\n          const newEnd = mapping.slice(index).map(step.to)\n          const oldStart = mapping.invert().map(newStart, -1)\n          const oldEnd = mapping.invert().map(newEnd)\n\n          const foundBeforeMark = nextTransaction.doc.nodeAt(newStart - 1)?.marks.some(mark => mark.eq(step.mark))\n          const foundAfterMark = nextTransaction.doc.nodeAt(newEnd)?.marks.some(mark => mark.eq(step.mark))\n\n          this.editor.emit('delete', {\n            type: 'mark',\n            mark: step.mark,\n            from: step.from,\n            to: step.to,\n            deletedRange: {\n              from: oldStart,\n              to: oldEnd,\n            },\n            newRange: {\n              from: newStart,\n              to: newEnd,\n            },\n            partial: Boolean(foundAfterMark || foundBeforeMark),\n            editor: this.editor,\n            transaction,\n            combinedTransform: nextTransaction,\n          })\n        }\n      })\n    }\n\n    if (this.editor.options.coreExtensionOptions?.delete?.async ?? true) {\n      setTimeout(callback, 0)\n    } else {\n      callback()\n    }\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Drop = Extension.create({\n  name: 'drop',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tiptapDrop'),\n\n        props: {\n          handleDrop: (_, e, slice, moved) => {\n            this.editor.emit('drop', {\n              editor: this.editor,\n              event: e,\n              slice,\n              moved,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Editable = Extension.create({\n  name: 'editable',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('editable'),\n        props: {\n          editable: () => this.editor.options.editable,\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const focusEventsPluginKey = new PluginKey('focusEvents')\n\nexport const FocusEvents = Extension.create({\n  name: 'focusEvents',\n\n  addProseMirrorPlugins() {\n    const { editor } = this\n\n    return [\n      new Plugin({\n        key: focusEventsPluginKey,\n        props: {\n          handleDOMEvents: {\n            focus: (view, event: Event) => {\n              editor.isFocused = true\n\n              const transaction = editor.state.tr.setMeta('focus', { event }).setMeta('addToHistory', false)\n\n              view.dispatch(transaction)\n\n              return false\n            },\n            blur: (view, event: Event) => {\n              editor.isFocused = false\n\n              const transaction = editor.state.tr.setMeta('blur', { event }).setMeta('addToHistory', false)\n\n              view.dispatch(transaction)\n\n              return false\n            },\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON>, <PERSON> } from '@tiptap/pm/state'\n\nimport { CommandManager } from '../CommandManager.js'\nimport { Extension } from '../Extension.js'\nimport { createChainableState } from '../helpers/createChainableState.js'\nimport { isNodeEmpty } from '../helpers/isNodeEmpty.js'\nimport { isiOS } from '../utilities/isiOS.js'\nimport { isMacOS } from '../utilities/isMacOS.js'\n\nexport const Keymap = Extension.create({\n  name: 'keymap',\n\n  addKeyboardShortcuts() {\n    const handleBackspace = () =>\n      this.editor.commands.first(({ commands }) => [\n        () => commands.undoInputRule(),\n\n        // maybe convert first text block node to default node\n        () =>\n          commands.command(({ tr }) => {\n            const { selection, doc } = tr\n            const { empty, $anchor } = selection\n            const { pos, parent } = $anchor\n            const $parentPos = $anchor.parent.isTextblock && pos > 0 ? tr.doc.resolve(pos - 1) : $anchor\n            const parentIsIsolating = $parentPos.parent.type.spec.isolating\n\n            const parentPos = $anchor.pos - $anchor.parentOffset\n\n            const isAtStart =\n              parentIsIsolating && $parentPos.parent.childCount === 1\n                ? parentPos === $anchor.pos\n                : Selection.atStart(doc).from === pos\n\n            if (\n              !empty ||\n              !parent.type.isTextblock ||\n              parent.textContent.length ||\n              !isAtStart ||\n              (isAtStart && $anchor.parent.type.name === 'paragraph') // prevent clearNodes when no nodes to clear, otherwise history stack is appended\n            ) {\n              return false\n            }\n\n            return commands.clearNodes()\n          }),\n\n        () => commands.deleteSelection(),\n        () => commands.joinBackward(),\n        () => commands.selectNodeBackward(),\n      ])\n\n    const handleDelete = () =>\n      this.editor.commands.first(({ commands }) => [\n        () => commands.deleteSelection(),\n        () => commands.deleteCurrentNode(),\n        () => commands.joinForward(),\n        () => commands.selectNodeForward(),\n      ])\n\n    const handleEnter = () =>\n      this.editor.commands.first(({ commands }) => [\n        () => commands.newlineInCode(),\n        () => commands.createParagraphNear(),\n        () => commands.liftEmptyBlock(),\n        () => commands.splitBlock(),\n      ])\n\n    const baseKeymap = {\n      Enter: handleEnter,\n      'Mod-Enter': () => this.editor.commands.exitCode(),\n      Backspace: handleBackspace,\n      'Mod-Backspace': handleBackspace,\n      'Shift-Backspace': handleBackspace,\n      Delete: handleDelete,\n      'Mod-Delete': handleDelete,\n      'Mod-a': () => this.editor.commands.selectAll(),\n    }\n\n    const pcKeymap = {\n      ...baseKeymap,\n    }\n\n    const macKeymap = {\n      ...baseKeymap,\n      'Ctrl-h': handleBackspace,\n      'Alt-Backspace': handleBackspace,\n      'Ctrl-d': handleDelete,\n      'Ctrl-Alt-Backspace': handleDelete,\n      'Alt-Delete': handleDelete,\n      'Alt-d': handleDelete,\n      'Ctrl-a': () => this.editor.commands.selectTextblockStart(),\n      'Ctrl-e': () => this.editor.commands.selectTextblockEnd(),\n    }\n\n    if (isiOS() || isMacOS()) {\n      return macKeymap\n    }\n\n    return pcKeymap\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      // With this plugin we check if the whole document was selected and deleted.\n      // In this case we will additionally call `clearNodes()` to convert e.g. a heading\n      // to a paragraph if necessary.\n      // This is an alternative to ProseMirror's `AllSelection`, which doesn’t work well\n      // with many other commands.\n      new Plugin({\n        key: new PluginKey('clearDocument'),\n        appendTransaction: (transactions, oldState, newState) => {\n          if (transactions.some(tr => tr.getMeta('composition'))) {\n            return\n          }\n\n          const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc)\n\n          const ignoreTr = transactions.some(transaction => transaction.getMeta('preventClearDocument'))\n\n          if (!docChanges || ignoreTr) {\n            return\n          }\n\n          const { empty, from, to } = oldState.selection\n          const allFrom = Selection.atStart(oldState.doc).from\n          const allEnd = Selection.atEnd(oldState.doc).to\n          const allWasSelected = from === allFrom && to === allEnd\n\n          if (empty || !allWasSelected) {\n            return\n          }\n\n          const isEmpty = isNodeEmpty(newState.doc)\n\n          if (!isEmpty) {\n            return\n          }\n\n          const tr = newState.tr\n          const state = createChainableState({\n            state: newState,\n            transaction: tr,\n          })\n          const { commands } = new CommandManager({\n            editor: this.editor,\n            state,\n          })\n\n          commands.clearNodes()\n\n          if (!tr.steps.length) {\n            return\n          }\n\n          return tr\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Paste = Extension.create({\n  name: 'paste',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tiptapPaste'),\n\n        props: {\n          handlePaste: (_view, e, slice) => {\n            this.editor.emit('paste', {\n              editor: this.editor,\n              event: e,\n              slice,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Tabindex = Extension.create({\n  name: 'tabindex',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tabindex'),\n        props: {\n          attributes: (): { [name: string]: string } => (this.editor.isEditable ? { tabindex: '0' } : {}),\n        },\n      }),\n    ]\n  },\n})\n", "import type { Fragment, Node, ResolvedPos } from '@tiptap/pm/model'\n\nimport type { Editor } from './Editor.js'\nimport type { Content, Range } from './types.js'\n\nexport class NodePos {\n  private resolvedPos: ResolvedPos\n\n  private isBlock: boolean\n\n  private editor: Editor\n\n  private get name(): string {\n    return this.node.type.name\n  }\n\n  constructor(pos: ResolvedPos, editor: Editor, isBlock = false, node: Node | null = null) {\n    this.isBlock = isBlock\n    this.resolvedPos = pos\n    this.editor = editor\n    this.currentNode = node\n  }\n\n  private currentNode: Node | null = null\n\n  get node(): Node {\n    return this.currentNode || this.resolvedPos.node()\n  }\n\n  get element(): HTMLElement {\n    return this.editor.view.domAtPos(this.pos).node as HTMLElement\n  }\n\n  public actualDepth: number | null = null\n\n  get depth(): number {\n    return this.actualDepth ?? this.resolvedPos.depth\n  }\n\n  get pos(): number {\n    return this.resolvedPos.pos\n  }\n\n  get content(): Fragment {\n    return this.node.content\n  }\n\n  set content(content: Content) {\n    let from = this.from\n    let to = this.to\n\n    if (this.isBlock) {\n      if (this.content.size === 0) {\n        console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`)\n        return\n      }\n\n      from = this.from + 1\n      to = this.to - 1\n    }\n\n    this.editor.commands.insertContentAt({ from, to }, content)\n  }\n\n  get attributes(): { [key: string]: any } {\n    return this.node.attrs\n  }\n\n  get textContent(): string {\n    return this.node.textContent\n  }\n\n  get size(): number {\n    return this.node.nodeSize\n  }\n\n  get from(): number {\n    if (this.isBlock) {\n      return this.pos\n    }\n\n    return this.resolvedPos.start(this.resolvedPos.depth)\n  }\n\n  get range(): Range {\n    return {\n      from: this.from,\n      to: this.to,\n    }\n  }\n\n  get to(): number {\n    if (this.isBlock) {\n      return this.pos + this.size\n    }\n\n    return this.resolvedPos.end(this.resolvedPos.depth) + (this.node.isText ? 0 : 1)\n  }\n\n  get parent(): NodePos | null {\n    if (this.depth === 0) {\n      return null\n    }\n\n    const parentPos = this.resolvedPos.start(this.resolvedPos.depth - 1)\n    const $pos = this.resolvedPos.doc.resolve(parentPos)\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get before(): NodePos | null {\n    let $pos = this.resolvedPos.doc.resolve(this.from - (this.isBlock ? 1 : 2))\n\n    if ($pos.depth !== this.depth) {\n      $pos = this.resolvedPos.doc.resolve(this.from - 3)\n    }\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get after(): NodePos | null {\n    let $pos = this.resolvedPos.doc.resolve(this.to + (this.isBlock ? 2 : 1))\n\n    if ($pos.depth !== this.depth) {\n      $pos = this.resolvedPos.doc.resolve(this.to + 3)\n    }\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get children(): NodePos[] {\n    const children: NodePos[] = []\n\n    this.node.content.forEach((node, offset) => {\n      const isBlock = node.isBlock && !node.isTextblock\n      const isNonTextAtom = node.isAtom && !node.isText\n\n      const targetPos = this.pos + offset + (isNonTextAtom ? 0 : 1)\n\n      // Check if targetPos is within valid document range\n      if (targetPos < 0 || targetPos > this.resolvedPos.doc.nodeSize - 2) {\n        return\n      }\n\n      const $pos = this.resolvedPos.doc.resolve(targetPos)\n\n      if (!isBlock && $pos.depth <= this.depth) {\n        return\n      }\n\n      const childNodePos = new NodePos($pos, this.editor, isBlock, isBlock ? node : null)\n\n      if (isBlock) {\n        childNodePos.actualDepth = this.depth + 1\n      }\n\n      children.push(new NodePos($pos, this.editor, isBlock, isBlock ? node : null))\n    })\n\n    return children\n  }\n\n  get firstChild(): NodePos | null {\n    return this.children[0] || null\n  }\n\n  get lastChild(): NodePos | null {\n    const children = this.children\n\n    return children[children.length - 1] || null\n  }\n\n  closest(selector: string, attributes: { [key: string]: any } = {}): NodePos | null {\n    let node: NodePos | null = null\n    let currentNode = this.parent\n\n    while (currentNode && !node) {\n      if (currentNode.node.type.name === selector) {\n        if (Object.keys(attributes).length > 0) {\n          const nodeAttributes = currentNode.node.attrs\n          const attrKeys = Object.keys(attributes)\n\n          for (let index = 0; index < attrKeys.length; index += 1) {\n            const key = attrKeys[index]\n\n            if (nodeAttributes[key] !== attributes[key]) {\n              break\n            }\n          }\n        } else {\n          node = currentNode\n        }\n      }\n\n      currentNode = currentNode.parent\n    }\n\n    return node\n  }\n\n  querySelector(selector: string, attributes: { [key: string]: any } = {}): NodePos | null {\n    return this.querySelectorAll(selector, attributes, true)[0] || null\n  }\n\n  querySelectorAll(selector: string, attributes: { [key: string]: any } = {}, firstItemOnly = false): NodePos[] {\n    let nodes: NodePos[] = []\n\n    if (!this.children || this.children.length === 0) {\n      return nodes\n    }\n    const attrKeys = Object.keys(attributes)\n\n    /**\n     * Finds all children recursively that match the selector and attributes\n     * If firstItemOnly is true, it will return the first item found\n     */\n    this.children.forEach(childPos => {\n      // If we already found a node and we only want the first item, we dont need to keep going\n      if (firstItemOnly && nodes.length > 0) {\n        return\n      }\n\n      if (childPos.node.type.name === selector) {\n        const doesAllAttributesMatch = attrKeys.every(key => attributes[key] === childPos.node.attrs[key])\n\n        if (doesAllAttributesMatch) {\n          nodes.push(childPos)\n        }\n      }\n\n      // If we already found a node and we only want the first item, we can stop here and skip the recursion\n      if (firstItemOnly && nodes.length > 0) {\n        return\n      }\n\n      nodes = nodes.concat(childPos.querySelectorAll(selector, attributes, firstItemOnly))\n    })\n\n    return nodes\n  }\n\n  setAttribute(attributes: { [key: string]: any }) {\n    const { tr } = this.editor.state\n\n    tr.setNodeMarkup(this.from, undefined, {\n      ...this.node.attrs,\n      ...attributes,\n    })\n\n    this.editor.view.dispatch(tr)\n  }\n}\n", "export const style = `.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: \"liga\" 0; /* the above doesn't seem to work in Edge */\n}\n\n.ProseMirror [contenteditable=\"false\"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable=\"false\"] [contenteditable=\"true\"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}`\n", "export function createStyleTag(style: string, nonce?: string, suffix?: string): HTMLStyleElement {\n  const tiptapStyleTag = <HTMLStyleElement>(\n    document.querySelector(`style[data-tiptap-style${suffix ? `-${suffix}` : ''}]`)\n  )\n\n  if (tiptapStyleTag !== null) {\n    return tiptapStyleTag\n  }\n\n  const styleNode = document.createElement('style')\n\n  if (nonce) {\n    styleNode.setAttribute('nonce', nonce)\n  }\n\n  styleNode.setAttribute(`data-tiptap-style${suffix ? `-${suffix}` : ''}`, '')\n  styleNode.innerHTML = style\n  document.getElementsByTagName('head')[0].appendChild(styleNode)\n\n  return styleNode\n}\n", "import type { MarkType } from '@tiptap/pm/model'\n\nimport { getMarksBetween } from '../helpers/getMarksBetween.js'\nimport type { InputRuleFinder } from '../InputRule.js'\nimport { InputRule } from '../InputRule.js'\nimport type { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that adds a mark when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function markInputRule(config: {\n  find: InputRuleFinder\n  type: MarkType\n  getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray) => Record<string, any>) | false | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const { tr } = state\n      const captureGroup = match[match.length - 1]\n      const fullMatch = match[0]\n\n      if (captureGroup) {\n        const startSpaces = fullMatch.search(/\\S/)\n        const textStart = range.from + fullMatch.indexOf(captureGroup)\n        const textEnd = textStart + captureGroup.length\n\n        const excludedMarks = getMarksBetween(range.from, range.to, state.doc)\n          .filter(item => {\n            // @ts-ignore\n            const excluded = item.mark.type.excluded as MarkType[]\n\n            return excluded.find(type => type === config.type && type !== item.mark.type)\n          })\n          .filter(item => item.to > textStart)\n\n        if (excludedMarks.length) {\n          return null\n        }\n\n        if (textEnd < range.to) {\n          tr.delete(textEnd, range.to)\n        }\n\n        if (textStart > range.from) {\n          tr.delete(range.from + startSpaces, textStart)\n        }\n\n        const markEnd = range.from + startSpaces + captureGroup.length\n\n        tr.addMark(range.from + startSpaces, markEnd, config.type.create(attributes || {}))\n\n        tr.removeStoredMark(config.type)\n      }\n    },\n  })\n}\n", "import type { NodeType } from '@tiptap/pm/model'\n\nimport type { InputRuleFinder } from '../InputRule.js'\nimport { InputRule } from '../InputRule.js'\nimport type { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that adds a node when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function nodeInputRule(config: {\n  /**\n   * The regex to match.\n   */\n  find: InputRuleFinder\n\n  /**\n   * The node type to add.\n   */\n  type: NodeType\n\n  /**\n   * A function that returns the attributes for the node\n   * can also be an object of attributes\n   */\n  getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray) => Record<string, any>) | false | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n      const { tr } = state\n      const start = range.from\n      let end = range.to\n\n      const newNode = config.type.create(attributes)\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n        let matchStart = start + offset\n\n        if (matchStart > end) {\n          matchStart = end\n        } else {\n          end = matchStart + match[1].length\n        }\n\n        // insert last typed character\n        const lastChar = match[0][match[0].length - 1]\n\n        tr.insertText(lastChar, start + match[0].length - 1)\n\n        // insert node from input rule\n        tr.replaceWith(matchStart, end, newNode)\n      } else if (match[0]) {\n        const insertionStart = config.type.isInline ? start : start - 1\n\n        tr.insert(insertionStart, config.type.create(attributes)).delete(tr.mapping.map(start), tr.mapping.map(end))\n      }\n\n      tr.scrollIntoView()\n    },\n  })\n}\n", "import type { NodeType } from '@tiptap/pm/model'\n\nimport type { InputRuleFinder } from '../InputRule.js'\nimport { InputRule } from '../InputRule.js'\nimport type { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that changes the type of a textblock when the\n * matched text is typed into it. When using a regular expresion you’ll\n * probably want the regexp to start with `^`, so that the pattern can\n * only occur at the start of a textblock.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function textblockTypeInputRule(config: {\n  find: InputRuleFinder\n  type: NodeType\n  getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray) => Record<string, any>) | false | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const $start = state.doc.resolve(range.from)\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n\n      if (!$start.node(-1).canReplaceWith($start.index(-1), $start.indexAfter(-1), config.type)) {\n        return null\n      }\n\n      state.tr.delete(range.from, range.to).setBlockType(range.from, range.from, config.type, attributes)\n    },\n  })\n}\n", "import type { InputRuleFinder } from '../InputRule.js'\nimport { InputRule } from '../InputRule.js'\n\n/**\n * Build an input rule that replaces text when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function textInputRule(config: { find: InputRuleFinder; replace: string }) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      let insert = config.replace\n      let start = range.from\n      const end = range.to\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n\n        insert += match[0].slice(offset + match[1].length)\n        start += offset\n\n        const cutOff = start - end\n\n        if (cutOff > 0) {\n          insert = match[0].slice(offset - cutOff, offset) + insert\n          start = end\n        }\n      }\n\n      state.tr.insertText(insert, start, end)\n    },\n  })\n}\n", "import type { Node as ProseMirrorNode, NodeType } from '@tiptap/pm/model'\nimport { canJoin, findWrapping } from '@tiptap/pm/transform'\n\nimport type { Editor } from '../Editor.js'\nimport type { InputRuleFinder } from '../InputRule.js'\nimport { InputRule } from '../InputRule.js'\nimport type { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule for automatically wrapping a textblock when a\n * given string is typed. When using a regular expresion you’ll\n * probably want the regexp to start with `^`, so that the pattern can\n * only occur at the start of a textblock.\n *\n * `type` is the type of node to wrap in.\n *\n * By default, if there’s a node with the same type above the newly\n * wrapped node, the rule will try to join those\n * two nodes. You can pass a join predicate, which takes a regular\n * expression match and the node before the wrapped node, and can\n * return a boolean to indicate whether a join should happen.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function wrappingInputRule(config: {\n  find: InputRuleFinder\n  type: NodeType\n  keepMarks?: boolean\n  keepAttributes?: boolean\n  editor?: Editor\n  getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray) => Record<string, any>) | false | null\n  joinPredicate?: (match: ExtendedRegExpMatchArray, node: ProseMirrorNode) => boolean\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match, chain }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n      const tr = state.tr.delete(range.from, range.to)\n      const $start = tr.doc.resolve(range.from)\n      const blockRange = $start.blockRange()\n      const wrapping = blockRange && findWrapping(blockRange, config.type, attributes)\n\n      if (!wrapping) {\n        return null\n      }\n\n      tr.wrap(blockRange, wrapping)\n\n      if (config.keepMarks && config.editor) {\n        const { selection, storedMarks } = state\n        const { splittableMarks } = config.editor.extensionManager\n        const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n        if (marks) {\n          const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n          tr.ensureMarks(filteredMarks)\n        }\n      }\n      if (config.keepAttributes) {\n        /** If the nodeType is `bulletList` or `orderedList` set the `nodeType` as `listItem` */\n        const nodeType =\n          config.type.name === 'bulletList' || config.type.name === 'orderedList' ? 'listItem' : 'taskList'\n\n        chain().updateAttributes(nodeType, attributes).run()\n      }\n\n      const before = tr.doc.resolve(range.from - 1).nodeBefore\n\n      if (\n        before &&\n        before.type === config.type &&\n        canJoin(tr.doc, range.from - 1) &&\n        (!config.joinPredicate || config.joinPredicate(match, before))\n      ) {\n        tr.join(range.from - 1)\n      }\n    },\n  })\n}\n", "export type Attributes = Record<string, any>\n\nexport type DOMOutputSpecElement = 0 | Attributes | DOMOutputSpecArray\n/**\n * Better describes the output of a `renderHTML` function in prosemirror\n * @see https://prosemirror.net/docs/ref/#model.DOMOutputSpec\n */\nexport type DOMOutputSpecArray =\n  | [string]\n  | [string, Attributes]\n  | [string, 0]\n  | [string, Attributes, 0]\n  | [string, Attributes, DOMOutputSpecArray | 0]\n  | [string, DOMOutputSpecArray]\n\n// JSX types for Tiptap's JSX runtime\n// These types only apply when using @jsxImportSource @tiptap/core\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace JSX {\n  export type Element = DOMOutputSpecArray\n  export interface IntrinsicElements {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    [key: string]: any\n  }\n  export interface ElementChildrenAttribute {\n    children: unknown\n  }\n}\n\nexport type JSXRenderer = (\n  tag: 'slot' | string | ((props?: Attributes) => DOMOutputSpecArray | DOMOutputSpecElement),\n  props?: Attributes,\n  ...children: JSXRenderer[]\n) => DOMOutputSpecArray | DOMOutputSpecElement\n\nexport function Fragment(props: { children: JSXRenderer[] }) {\n  return props.children\n}\n\nexport const h: JSXRenderer = (tag, attributes) => {\n  // Treat the slot tag as the Prosemirror hole to render content into\n  if (tag === 'slot') {\n    return 0\n  }\n\n  // If the tag is a function, call it with the props\n  if (tag instanceof Function) {\n    return tag(attributes)\n  }\n\n  const { children, ...rest } = attributes ?? {}\n\n  if (tag === 'svg') {\n    throw new Error('SVG elements are not supported in the JSX syntax, use the array syntax instead')\n  }\n\n  // Otherwise, return the tag, attributes, and children\n  return [tag, rest, children]\n}\n\n// See\n// https://esbuild.github.io/api/#jsx-import-source\n// https://www.typescriptlang.org/tsconfig/#jsxImportSource\n\nexport { h as createElement, h as jsx, h as jsxDEV, h as jsxs }\n", "import type { NodeType } from '@tiptap/pm/model'\nimport { type EditorState, NodeSelection } from '@tiptap/pm/state'\n\nexport function canInsertNode(state: EditorState, nodeType: NodeType): boolean {\n  const { selection } = state\n  const { $from } = selection\n\n  // Special handling for NodeSelection\n  if (selection instanceof NodeSelection) {\n    const index = $from.index()\n    const parent = $from.parent\n\n    // Can we replace the selected node with the horizontal rule?\n    return parent.canReplaceWith(index, index + 1, nodeType)\n  }\n\n  // Default: check if we can insert at the current position\n  let depth = $from.depth\n\n  while (depth >= 0) {\n    const index = $from.index(depth)\n    const parent = $from.node(depth)\n    const match = parent.contentMatchAt(index)\n    if (match.matchType(nodeType)) {\n      return true\n    }\n    depth -= 1\n  }\n  return false\n}\n", "// source: https://stackoverflow.com/a/6969486\nexport function escapeForRegEx(string: string): string {\n  return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&')\n}\n", "export function isString(value: any): value is string {\n  return typeof value === 'string'\n}\n", "import type { Mark } from '@tiptap/pm/model'\nimport type { ViewMutationRecord } from '@tiptap/pm/view'\n\nimport type { Editor } from './Editor.js'\nimport type { MarkViewProps, MarkViewRendererOptions } from './types.js'\nimport { isAndroid, isiOS } from './utilities/index.js'\n\nexport function updateMarkViewAttributes(checkMark: Mark, editor: Editor, attrs: Record<string, any> = {}): void {\n  const { state } = editor\n  const { doc, tr } = state\n  const thisMark = checkMark\n\n  doc.descendants((node, pos) => {\n    const from = tr.mapping.map(pos)\n    const to = tr.mapping.map(pos) + node.nodeSize\n    let foundMark: Mark | null = null\n\n    // find the mark on the current node\n    node.marks.forEach(mark => {\n      if (mark !== thisMark) {\n        return false\n      }\n\n      foundMark = mark\n    })\n\n    if (!foundMark) {\n      return\n    }\n\n    // check if we need to update given the attributes\n    let needsUpdate = false\n    Object.keys(attrs).forEach(k => {\n      if (attrs[k] !== foundMark!.attrs[k]) {\n        needsUpdate = true\n      }\n    })\n\n    if (needsUpdate) {\n      const updatedMark = checkMark.type.create({\n        ...checkMark.attrs,\n        ...attrs,\n      })\n\n      tr.removeMark(from, to, checkMark.type)\n      tr.addMark(from, to, updatedMark)\n    }\n  })\n\n  if (tr.docChanged) {\n    editor.view.dispatch(tr)\n  }\n}\n\nexport class MarkView<Component, Options extends MarkViewRendererOptions = MarkViewRendererOptions> {\n  component: Component\n  editor: Editor\n  options: Options\n  mark: MarkViewProps['mark']\n  HTMLAttributes: MarkViewProps['HTMLAttributes']\n\n  constructor(component: Component, props: MarkViewProps, options?: Partial<Options>) {\n    this.component = component\n    this.editor = props.editor\n    this.options = { ...options } as Options\n    this.mark = props.mark\n    this.HTMLAttributes = props.HTMLAttributes\n  }\n\n  get dom(): HTMLElement {\n    return this.editor.view.dom\n  }\n\n  get contentDOM(): HTMLElement | null {\n    return null\n  }\n\n  /**\n   * Update the attributes of the mark in the document.\n   * @param attrs The attributes to update.\n   */\n  updateAttributes(attrs: Record<string, any>, checkMark?: Mark): void {\n    updateMarkViewAttributes(checkMark || this.mark, this.editor, attrs)\n  }\n\n  ignoreMutation(mutation: ViewMutationRecord): boolean {\n    if (!this.dom || !this.contentDOM) {\n      return true\n    }\n\n    if (typeof this.options.ignoreMutation === 'function') {\n      return this.options.ignoreMutation({ mutation })\n    }\n\n    if (mutation.type === 'selection') {\n      return false\n    }\n\n    if (\n      this.dom.contains(mutation.target) &&\n      mutation.type === 'childList' &&\n      (isiOS() || isAndroid()) &&\n      this.editor.isFocused\n    ) {\n      const changedNodes = [...Array.from(mutation.addedNodes), ...Array.from(mutation.removedNodes)] as HTMLElement[]\n\n      if (changedNodes.every(node => node.isContentEditable)) {\n        return false\n      }\n    }\n\n    if (this.contentDOM === mutation.target && mutation.type === 'attributes') {\n      return true\n    }\n\n    if (this.contentDOM.contains(mutation.target)) {\n      return false\n    }\n\n    return true\n  }\n}\n", "import type { DOMOutputSpec, Node as ProseMirrorNode, NodeSpec, NodeType } from '@tiptap/pm/model'\n\nimport type { Editor } from './Editor.js'\nimport type { ExtendableConfig } from './Extendable.js'\nimport { Extendable } from './Extendable.js'\nimport type { Attributes, NodeViewRenderer, ParentConfig } from './types.js'\n\nexport interface NodeConfig<Options = any, Storage = any>\n  extends ExtendableConfig<Options, Storage, NodeConfig<Options, Storage>, NodeType> {\n  /**\n   * Node View\n   */\n  addNodeView?:\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        editor: Editor\n        type: NodeType\n        parent: ParentConfig<NodeConfig<Options, Storage>>['addNodeView']\n      }) => NodeViewRenderer)\n    | null\n\n  /**\n   * Defines if this node should be a top level node (doc)\n   * @default false\n   * @example true\n   */\n  topNode?: boolean\n\n  /**\n   * The content expression for this node, as described in the [schema\n   * guide](/docs/guide/#schema.content_expressions). When not given,\n   * the node does not allow any content.\n   *\n   * You can read more about it on the Prosemirror documentation here\n   * @see https://prosemirror.net/docs/guide/#schema.content_expressions\n   * @default undefined\n   * @example content: 'block+'\n   * @example content: 'headline paragraph block*'\n   */\n  content?:\n    | NodeSpec['content']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['content']\n        editor?: Editor\n      }) => NodeSpec['content'])\n\n  /**\n   * The marks that are allowed inside of this node. May be a\n   * space-separated string referring to mark names or groups, `\"_\"`\n   * to explicitly allow all marks, or `\"\"` to disallow marks. When\n   * not given, nodes with inline content default to allowing all\n   * marks, other nodes default to not allowing marks.\n   *\n   * @example marks: 'strong em'\n   */\n  marks?:\n    | NodeSpec['marks']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['marks']\n        editor?: Editor\n      }) => NodeSpec['marks'])\n\n  /**\n   * The group or space-separated groups to which this node belongs,\n   * which can be referred to in the content expressions for the\n   * schema.\n   *\n   * By default Tiptap uses the groups 'block' and 'inline' for nodes. You\n   * can also use custom groups if you want to group specific nodes together\n   * and handle them in your schema.\n   * @example group: 'block'\n   * @example group: 'inline'\n   * @example group: 'customBlock' // this uses a custom group\n   */\n  group?:\n    | NodeSpec['group']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['group']\n        editor?: Editor\n      }) => NodeSpec['group'])\n\n  /**\n   * Should be set to true for inline nodes. (Implied for text nodes.)\n   */\n  inline?:\n    | NodeSpec['inline']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['inline']\n        editor?: Editor\n      }) => NodeSpec['inline'])\n\n  /**\n   * Can be set to true to indicate that, though this isn't a [leaf\n   * node](https://prosemirror.net/docs/ref/#model.NodeType.isLeaf), it doesn't have directly editable\n   * content and should be treated as a single unit in the view.\n   *\n   * @example atom: true\n   */\n  atom?:\n    | NodeSpec['atom']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['atom']\n        editor?: Editor\n      }) => NodeSpec['atom'])\n\n  /**\n   * Controls whether nodes of this type can be selected as a [node\n   * selection](https://prosemirror.net/docs/ref/#state.NodeSelection). Defaults to true for non-text\n   * nodes.\n   *\n   * @default true\n   * @example selectable: false\n   */\n  selectable?:\n    | NodeSpec['selectable']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['selectable']\n        editor?: Editor\n      }) => NodeSpec['selectable'])\n\n  /**\n   * Determines whether nodes of this type can be dragged without\n   * being selected. Defaults to false.\n   *\n   * @default: false\n   * @example: draggable: true\n   */\n  draggable?:\n    | NodeSpec['draggable']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['draggable']\n        editor?: Editor\n      }) => NodeSpec['draggable'])\n\n  /**\n   * Can be used to indicate that this node contains code, which\n   * causes some commands to behave differently.\n   */\n  code?:\n    | NodeSpec['code']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['code']\n        editor?: Editor\n      }) => NodeSpec['code'])\n\n  /**\n   * Controls way whitespace in this a node is parsed. The default is\n   * `\"normal\"`, which causes the [DOM parser](https://prosemirror.net/docs/ref/#model.DOMParser) to\n   * collapse whitespace in normal mode, and normalize it (replacing\n   * newlines and such with spaces) otherwise. `\"pre\"` causes the\n   * parser to preserve spaces inside the node. When this option isn't\n   * given, but [`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) is true, `whitespace`\n   * will default to `\"pre\"`. Note that this option doesn't influence\n   * the way the node is rendered—that should be handled by `toDOM`\n   * and/or styling.\n   */\n  whitespace?:\n    | NodeSpec['whitespace']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['whitespace']\n        editor?: Editor\n      }) => NodeSpec['whitespace'])\n\n  /**\n   * Allows a **single** node to be set as linebreak equivalent (e.g. hardBreak).\n   * When converting between block types that have whitespace set to \"pre\"\n   * and don't support the linebreak node (e.g. codeBlock) and other block types\n   * that do support the linebreak node (e.g. paragraphs) - this node will be used\n   * as the linebreak instead of stripping the newline.\n   *\n   * See [linebreakReplacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement).\n   */\n  linebreakReplacement?:\n    | NodeSpec['linebreakReplacement']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['linebreakReplacement']\n        editor?: Editor\n      }) => NodeSpec['linebreakReplacement'])\n\n  /**\n   * When enabled, enables both\n   * [`definingAsContext`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext) and\n   * [`definingForContent`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n   *\n   * @default false\n   * @example isolating: true\n   */\n  defining?:\n    | NodeSpec['defining']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['defining']\n        editor?: Editor\n      }) => NodeSpec['defining'])\n\n  /**\n   * When enabled (default is false), the sides of nodes of this type\n   * count as boundaries that regular editing operations, like\n   * backspacing or lifting, won't cross. An example of a node that\n   * should probably have this enabled is a table cell.\n   */\n  isolating?:\n    | NodeSpec['isolating']\n    | ((this: {\n        name: string\n        options: Options\n        storage: Storage\n        parent: ParentConfig<NodeConfig<Options, Storage>>['isolating']\n        editor?: Editor\n      }) => NodeSpec['isolating'])\n\n  /**\n   * Associates DOM parser information with this node, which can be\n   * used by [`DOMParser.fromSchema`](https://prosemirror.net/docs/ref/#model.DOMParser^fromSchema) to\n   * automatically derive a parser. The `node` field in the rules is\n   * implied (the name of this node will be filled in automatically).\n   * If you supply your own parser, you do not need to also specify\n   * parsing rules in your schema.\n   *\n   * @example parseHTML: [{ tag: 'div', attrs: { 'data-id': 'my-block' } }]\n   */\n  parseHTML?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    parent: ParentConfig<NodeConfig<Options, Storage>>['parseHTML']\n    editor?: Editor\n  }) => NodeSpec['parseDOM']\n\n  /**\n   * A description of a DOM structure. Can be either a string, which is\n   * interpreted as a text node, a DOM node, which is interpreted as\n   * itself, a `{dom, contentDOM}` object, or an array.\n   *\n   * An array describes a DOM element. The first value in the array\n   * should be a string—the name of the DOM element, optionally prefixed\n   * by a namespace URL and a space. If the second element is plain\n   * object, it is interpreted as a set of attributes for the element.\n   * Any elements after that (including the 2nd if it's not an attribute\n   * object) are interpreted as children of the DOM elements, and must\n   * either be valid `DOMOutputSpec` values, or the number zero.\n   *\n   * The number zero (pronounced “hole”) is used to indicate the place\n   * where a node's child nodes should be inserted. If it occurs in an\n   * output spec, it should be the only child element in its parent\n   * node.\n   *\n   * @example toDOM: ['div[data-id=\"my-block\"]', { class: 'my-block' }, 0]\n   */\n  renderHTML?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['renderHTML']\n          editor?: Editor\n        },\n        props: {\n          node: ProseMirrorNode\n          HTMLAttributes: Record<string, any>\n        },\n      ) => DOMOutputSpec)\n    | null\n\n  /**\n   * renders the node as text\n   * @example renderText: () => 'foo\n   */\n  renderText?:\n    | ((\n        this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['renderText']\n          editor?: Editor\n        },\n        props: {\n          node: ProseMirrorNode\n          pos: number\n          parent: ProseMirrorNode\n          index: number\n        },\n      ) => string)\n    | null\n\n  /**\n   * Add attributes to the node\n   * @example addAttributes: () => ({ class: 'foo' })\n   */\n  addAttributes?: (this: {\n    name: string\n    options: Options\n    storage: Storage\n    parent: ParentConfig<NodeConfig<Options, Storage>>['addAttributes']\n    editor?: Editor\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n  }) => Attributes | {}\n}\n\n/**\n * The Node class is used to create custom node extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Node<Options = any, Storage = any> extends Extendable<Options, Storage, NodeConfig<Options, Storage>> {\n  type = 'node'\n\n  /**\n   * Create a new Node instance\n   * @param config - Node configuration object or a function that returns a configuration object\n   */\n  static create<O = any, S = any>(config: Partial<NodeConfig<O, S>> | (() => Partial<NodeConfig<O, S>>) = {}) {\n    // If the config is a function, execute it to get the configuration object\n    const resolvedConfig = typeof config === 'function' ? config() : config\n    return new Node<O, S>(resolvedConfig)\n  }\n\n  configure(options?: Partial<Options>) {\n    return super.configure(options) as Node<Options, Storage>\n  }\n\n  extend<\n    ExtendedOptions = Options,\n    ExtendedStorage = Storage,\n    ExtendedConfig = NodeConfig<ExtendedOptions, ExtendedStorage>,\n  >(\n    extendedConfig?:\n      | (() => Partial<ExtendedConfig>)\n      | (Partial<ExtendedConfig> &\n          ThisType<{\n            name: string\n            options: ExtendedOptions\n            storage: ExtendedStorage\n            editor: Editor\n            type: NodeType\n          }>),\n  ): Node<ExtendedOptions, ExtendedStorage> {\n    // If the extended config is a function, execute it to get the configuration object\n    const resolvedConfig = typeof extendedConfig === 'function' ? extendedConfig() : extendedConfig\n    return super.extend(resolvedConfig) as Node<ExtendedOptions, ExtendedStorage>\n  }\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\nimport type { NodeView as ProseMirrorNodeView, ViewMutationRecord } from '@tiptap/pm/view'\n\nimport type { Editor as CoreEditor } from './Editor.js'\nimport type { DecorationWithType, NodeViewRendererOptions, NodeViewRendererProps } from './types.js'\nimport { isAndroid } from './utilities/isAndroid.js'\nimport { isiOS } from './utilities/isiOS.js'\n\n/**\n * Node views are used to customize the rendered DOM structure of a node.\n * @see https://tiptap.dev/guide/node-views\n */\nexport class NodeView<\n  Component,\n  NodeEditor extends CoreEditor = CoreEditor,\n  Options extends NodeViewRendererOptions = NodeViewRendererOptions,\n> implements ProseMirrorNodeView\n{\n  component: Component\n\n  editor: NodeEditor\n\n  options: Options\n\n  extension: NodeViewRendererProps['extension']\n\n  node: NodeViewRendererProps['node']\n\n  decorations: NodeViewRendererProps['decorations']\n\n  innerDecorations: NodeViewRendererProps['innerDecorations']\n\n  view: NodeViewRendererProps['view']\n\n  getPos: NodeViewRendererProps['getPos']\n\n  HTMLAttributes: NodeViewRendererProps['HTMLAttributes']\n\n  isDragging = false\n\n  constructor(component: Component, props: NodeViewRendererProps, options?: Partial<Options>) {\n    this.component = component\n    this.editor = props.editor as NodeEditor\n    this.options = {\n      stopEvent: null,\n      ignoreMutation: null,\n      ...options,\n    } as Options\n    this.extension = props.extension\n    this.node = props.node\n    this.decorations = props.decorations as DecorationWithType[]\n    this.innerDecorations = props.innerDecorations\n    this.view = props.view\n    this.HTMLAttributes = props.HTMLAttributes\n    this.getPos = props.getPos\n    this.mount()\n  }\n\n  mount() {\n    // eslint-disable-next-line\n    return\n  }\n\n  get dom(): HTMLElement {\n    return this.editor.view.dom as HTMLElement\n  }\n\n  get contentDOM(): HTMLElement | null {\n    return null\n  }\n\n  onDragStart(event: DragEvent) {\n    const { view } = this.editor\n    const target = event.target as HTMLElement\n\n    // get the drag handle element\n    // `closest` is not available for text nodes so we may have to use its parent\n    const dragHandle =\n      target.nodeType === 3 ? target.parentElement?.closest('[data-drag-handle]') : target.closest('[data-drag-handle]')\n\n    if (!this.dom || this.contentDOM?.contains(target) || !dragHandle) {\n      return\n    }\n\n    let x = 0\n    let y = 0\n\n    // calculate offset for drag element if we use a different drag handle element\n    if (this.dom !== dragHandle) {\n      const domBox = this.dom.getBoundingClientRect()\n      const handleBox = dragHandle.getBoundingClientRect()\n\n      // In React, we have to go through nativeEvent to reach offsetX/offsetY.\n      const offsetX = event.offsetX ?? (event as any).nativeEvent?.offsetX\n      const offsetY = event.offsetY ?? (event as any).nativeEvent?.offsetY\n\n      x = handleBox.x - domBox.x + offsetX\n      y = handleBox.y - domBox.y + offsetY\n    }\n\n    const clonedNode = this.dom.cloneNode(true) as HTMLElement\n\n    // Preserve the visual size of the original when using the clone as\n    // the drag image.\n    try {\n      const domBox = this.dom.getBoundingClientRect()\n      clonedNode.style.width = `${Math.round(domBox.width)}px`\n      clonedNode.style.height = `${Math.round(domBox.height)}px`\n      clonedNode.style.boxSizing = 'border-box'\n      // Ensure the clone doesn't capture pointer events while offscreen\n      clonedNode.style.pointerEvents = 'none'\n    } catch {\n      // ignore measurement errors (e.g. if element not in DOM)\n    }\n\n    // Some browsers (notably Safari) require the element passed to\n    // setDragImage to be present in the DOM. Using a detached node can\n    // cause the drag to immediately end.\n    let dragImageWrapper: HTMLElement | null = null\n\n    try {\n      dragImageWrapper = document.createElement('div')\n      dragImageWrapper.style.position = 'absolute'\n      dragImageWrapper.style.top = '-9999px'\n      dragImageWrapper.style.left = '-9999px'\n      dragImageWrapper.style.pointerEvents = 'none'\n      dragImageWrapper.appendChild(clonedNode)\n      document.body.appendChild(dragImageWrapper)\n\n      event.dataTransfer?.setDragImage(clonedNode, x, y)\n    } finally {\n      // Remove the wrapper on the next tick so the browser can use the\n      // element as the drag image. A 0ms timeout is enough in practice.\n      if (dragImageWrapper) {\n        setTimeout(() => {\n          try {\n            dragImageWrapper?.remove()\n          } catch {\n            // ignore removal errors\n          }\n        }, 0)\n      }\n    }\n\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n    // we need to tell ProseMirror that we want to move the whole node\n    // so we create a NodeSelection\n    const selection = NodeSelection.create(view.state.doc, pos)\n    const transaction = view.state.tr.setSelection(selection)\n\n    view.dispatch(transaction)\n  }\n\n  stopEvent(event: Event) {\n    if (!this.dom) {\n      return false\n    }\n\n    if (typeof this.options.stopEvent === 'function') {\n      return this.options.stopEvent({ event })\n    }\n\n    const target = event.target as HTMLElement\n    const isInElement = this.dom.contains(target) && !this.contentDOM?.contains(target)\n\n    // any event from child nodes should be handled by ProseMirror\n    if (!isInElement) {\n      return false\n    }\n\n    const isDragEvent = event.type.startsWith('drag')\n    const isDropEvent = event.type === 'drop'\n    const isInput = ['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA'].includes(target.tagName) || target.isContentEditable\n\n    // any input event within node views should be ignored by ProseMirror\n    if (isInput && !isDropEvent && !isDragEvent) {\n      return true\n    }\n\n    const { isEditable } = this.editor\n    const { isDragging } = this\n    const isDraggable = !!this.node.type.spec.draggable\n    const isSelectable = NodeSelection.isSelectable(this.node)\n    const isCopyEvent = event.type === 'copy'\n    const isPasteEvent = event.type === 'paste'\n    const isCutEvent = event.type === 'cut'\n    const isClickEvent = event.type === 'mousedown'\n\n    // ProseMirror tries to drag selectable nodes\n    // even if `draggable` is set to `false`\n    // this fix prevents that\n    if (!isDraggable && isSelectable && isDragEvent && event.target === this.dom) {\n      event.preventDefault()\n    }\n\n    if (isDraggable && isDragEvent && !isDragging && event.target === this.dom) {\n      event.preventDefault()\n      return false\n    }\n\n    // we have to store that dragging started\n    if (isDraggable && isEditable && !isDragging && isClickEvent) {\n      const dragHandle = target.closest('[data-drag-handle]')\n      const isValidDragHandle = dragHandle && (this.dom === dragHandle || this.dom.contains(dragHandle))\n\n      if (isValidDragHandle) {\n        this.isDragging = true\n\n        document.addEventListener(\n          'dragend',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n\n        document.addEventListener(\n          'drop',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n\n        document.addEventListener(\n          'mouseup',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n      }\n    }\n\n    // these events are handled by prosemirror\n    if (isDragging || isDropEvent || isCopyEvent || isPasteEvent || isCutEvent || (isClickEvent && isSelectable)) {\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Called when a DOM [mutation](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) or a selection change happens within the view.\n   * @return `false` if the editor should re-read the selection or re-parse the range around the mutation\n   * @return `true` if it can safely be ignored.\n   */\n  ignoreMutation(mutation: ViewMutationRecord) {\n    if (!this.dom || !this.contentDOM) {\n      return true\n    }\n\n    if (typeof this.options.ignoreMutation === 'function') {\n      return this.options.ignoreMutation({ mutation })\n    }\n\n    // a leaf/atom node is like a black box for ProseMirror\n    // and should be fully handled by the node view\n    if (this.node.isLeaf || this.node.isAtom) {\n      return true\n    }\n\n    // ProseMirror should handle any selections\n    if (mutation.type === 'selection') {\n      return false\n    }\n\n    // try to prevent a bug on iOS and Android that will break node views on enter\n    // this is because ProseMirror can’t preventDispatch on enter\n    // this will lead to a re-render of the node view on enter\n    // see: https://github.com/ueberdosis/tiptap/issues/1214\n    // see: https://github.com/ueberdosis/tiptap/issues/2534\n    if (\n      this.dom.contains(mutation.target) &&\n      mutation.type === 'childList' &&\n      (isiOS() || isAndroid()) &&\n      this.editor.isFocused\n    ) {\n      const changedNodes = [...Array.from(mutation.addedNodes), ...Array.from(mutation.removedNodes)] as HTMLElement[]\n\n      // we’ll check if every changed node is contentEditable\n      // to make sure it’s probably mutated by ProseMirror\n      if (changedNodes.every(node => node.isContentEditable)) {\n        return false\n      }\n    }\n\n    // we will allow mutation contentDOM with attributes\n    // so we can for example adding classes within our node view\n    if (this.contentDOM === mutation.target && mutation.type === 'attributes') {\n      return true\n    }\n\n    // ProseMirror should handle any changes within contentDOM\n    if (this.contentDOM.contains(mutation.target)) {\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Update the attributes of the prosemirror node.\n   */\n  updateAttributes(attributes: Record<string, any>): void {\n    this.editor.commands.command(({ tr }) => {\n      const pos = this.getPos()\n\n      if (typeof pos !== 'number') {\n        return false\n      }\n\n      tr.setNodeMarkup(pos, undefined, {\n        ...this.node.attrs,\n        ...attributes,\n      })\n\n      return true\n    })\n  }\n\n  /**\n   * Delete the node.\n   */\n  deleteNode(): void {\n    const from = this.getPos()\n\n    if (typeof from !== 'number') {\n      return\n    }\n    const to = from + this.node.nodeSize\n\n    this.editor.commands.deleteRange({ from, to })\n  }\n}\n", "import type { MarkType } from '@tiptap/pm/model'\n\nimport { getMarksBetween } from '../helpers/getMarksBetween.js'\nimport type { PasteRuleFinder } from '../PasteRule.js'\nimport { PasteRule } from '../PasteRule.js'\nimport type { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an paste rule that adds a mark when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function markPasteRule(config: {\n  find: PasteRuleFinder\n  type: MarkType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler: ({ state, range, match, pasteEvent }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match, pasteEvent)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const { tr } = state\n      const captureGroup = match[match.length - 1]\n      const fullMatch = match[0]\n      let markEnd = range.to\n\n      if (captureGroup) {\n        const startSpaces = fullMatch.search(/\\S/)\n        const textStart = range.from + fullMatch.indexOf(captureGroup)\n        const textEnd = textStart + captureGroup.length\n\n        const excludedMarks = getMarksBetween(range.from, range.to, state.doc)\n          .filter(item => {\n            // @ts-ignore\n            const excluded = item.mark.type.excluded as MarkType[]\n\n            return excluded.find(type => type === config.type && type !== item.mark.type)\n          })\n          .filter(item => item.to > textStart)\n\n        if (excludedMarks.length) {\n          return null\n        }\n\n        if (textEnd < range.to) {\n          tr.delete(textEnd, range.to)\n        }\n\n        if (textStart > range.from) {\n          tr.delete(range.from + startSpaces, textStart)\n        }\n\n        markEnd = range.from + startSpaces + captureGroup.length\n\n        tr.addMark(range.from + startSpaces, markEnd, config.type.create(attributes || {}))\n\n        tr.removeStoredMark(config.type)\n      }\n    },\n  })\n}\n", "import type { NodeType } from '@tiptap/pm/model'\n\nimport type { PasteRuleFinder } from '../PasteRule.js'\nimport { PasteRule } from '../PasteRule.js'\nimport type { ExtendedRegExpMatchArray, JSONContent } from '../types.js'\nimport { callOrReturn } from '../utilities/index.js'\n\n/**\n * Build an paste rule that adds a node when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function nodePasteRule(config: {\n  find: PasteRuleFinder\n  type: NodeType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>)\n    | false\n    | null\n  getContent?: JSONContent[] | ((attrs: Record<string, any>) => JSONContent[]) | false | null\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler({ match, chain, range, pasteEvent }) {\n      const attributes = callOrReturn(config.getAttributes, undefined, match, pasteEvent)\n      const content = callOrReturn(config.getContent, undefined, attributes)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const node = { type: config.type.name, attrs: attributes } as JSONContent\n\n      if (content) {\n        node.content = content\n      }\n\n      if (match.input) {\n        chain().deleteRange(range).insertContentAt(range.from, node)\n      }\n    },\n  })\n}\n", "import type { PasteRuleFinder } from '../PasteRule.js'\nimport { PasteRule } from '../PasteRule.js'\n\n/**\n * Build an paste rule that replaces text when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function textPasteRule(config: { find: PasteRuleFinder; replace: string }) {\n  return new PasteRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      let insert = config.replace\n      let start = range.from\n      const end = range.to\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n\n        insert += match[0].slice(offset + match[1].length)\n        start += offset\n\n        const cutOff = start - end\n\n        if (cutOff > 0) {\n          insert = match[0].slice(offset - cutOff, offset) + insert\n          start = end\n        }\n      }\n\n      state.tr.insertText(insert, start, end)\n    },\n  })\n}\n", "import type { Transaction } from '@tiptap/pm/state'\n\nexport interface TrackerResult {\n  position: number\n  deleted: boolean\n}\n\nexport class Tracker {\n  transaction: Transaction\n\n  currentStep: number\n\n  constructor(transaction: Transaction) {\n    this.transaction = transaction\n    this.currentStep = this.transaction.steps.length\n  }\n\n  map(position: number): TrackerResult {\n    let deleted = false\n\n    const mappedPosition = this.transaction.steps.slice(this.currentStep).reduce((newPosition, step) => {\n      const mapResult = step.getMap().mapResult(newPosition)\n\n      if (mapResult.deleted) {\n        deleted = true\n      }\n\n      return mapResult.pos\n    }, position)\n\n    return {\n      position: mappedPosition,\n      deleted,\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAOO,SAAS,qBAAqB,QAAuE;AAC1G,QAAM,EAAE,OAAO,YAAY,IAAI;AAC/B,MAAI,EAAE,UAAU,IAAI;AACpB,MAAI,EAAE,IAAI,IAAI;AACd,MAAI,EAAE,YAAY,IAAI;AAEtB,SAAO;AAAA,IACL,GAAG;AAAA,IACH,OAAO,MAAM,MAAM,KAAK,KAAK;AAAA,IAC7B,kBAAkB,MAAM,iBAAiB,KAAK,KAAK;AAAA,IACnD,SAAS,MAAM;AAAA,IACf,QAAQ,MAAM;AAAA,IACd,aAAa,MAAM,YAAY,KAAK,KAAK;AAAA,IACzC,QAAQ,MAAM,OAAO,KAAK,KAAK;AAAA,IAC/B,IAAI,cAAc;AAChB,aAAO;AAAA,IACT;AAAA,IACA,IAAI,YAAY;AACd,aAAO;AAAA,IACT;AAAA,IACA,IAAI,MAAM;AACR,aAAO;AAAA,IACT;AAAA,IACA,IAAI,KAAK;AACP,kBAAY,YAAY;AACxB,YAAM,YAAY;AAClB,oBAAc,YAAY;AAE1B,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;AChCO,IAAM,iBAAN,MAAqB;AAAA,EAO1B,YAAY,OAAgD;AAC1D,SAAK,SAAS,MAAM;AACpB,SAAK,cAAc,KAAK,OAAO,iBAAiB;AAChD,SAAK,cAAc,MAAM;AAAA,EAC3B;AAAA,EAEA,IAAI,iBAA0B;AAC5B,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,QAAqB;AACvB,WAAO,KAAK,eAAe,KAAK,OAAO;AAAA,EACzC;AAAA,EAEA,IAAI,WAA2B;AAC7B,UAAM,EAAE,aAAa,QAAQ,MAAM,IAAI;AACvC,UAAM,EAAE,KAAK,IAAI;AACjB,UAAM,EAAE,GAAG,IAAI;AACf,UAAM,QAAQ,KAAK,WAAW,EAAE;AAEhC,WAAO,OAAO;AAAA,MACZ,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,MAAMA,QAAO,MAAM;AACnD,cAAM,SAAS,IAAI,SAAgB;AACjC,gBAAM,WAAWA,SAAQ,GAAG,IAAI,EAAE,KAAK;AAEvC,cAAI,CAAC,GAAG,QAAQ,iBAAiB,KAAK,CAAC,KAAK,gBAAgB;AAC1D,iBAAK,SAAS,EAAE;AAAA,UAClB;AAEA,iBAAO;AAAA,QACT;AAEA,eAAO,CAAC,MAAM,MAAM;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,IAAI,QAA+B;AACjC,WAAO,MAAM,KAAK,YAAY;AAAA,EAChC;AAAA,EAEA,IAAI,MAAyB;AAC3B,WAAO,MAAM,KAAK,UAAU;AAAA,EAC9B;AAAA,EAEO,YAAY,SAAuB,iBAAiB,MAAuB;AAChF,UAAM,EAAE,aAAa,QAAQ,MAAM,IAAI;AACvC,UAAM,EAAE,KAAK,IAAI;AACjB,UAAM,YAAuB,CAAC;AAC9B,UAAM,sBAAsB,CAAC,CAAC;AAC9B,UAAM,KAAK,WAAW,MAAM;AAE5B,UAAMC,OAAM,MAAM;AAChB,UAAI,CAAC,uBAAuB,kBAAkB,CAAC,GAAG,QAAQ,iBAAiB,KAAK,CAAC,KAAK,gBAAgB;AACpG,aAAK,SAAS,EAAE;AAAA,MAClB;AAEA,aAAO,UAAU,MAAM,cAAY,aAAa,IAAI;AAAA,IACtD;AAEA,UAAM,QAAQ;AAAA,MACZ,GAAG,OAAO;AAAA,QACR,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,MAAMD,QAAO,MAAM;AACnD,gBAAM,iBAAiB,IAAI,SAAkB;AAC3C,kBAAM,QAAQ,KAAK,WAAW,IAAI,cAAc;AAChD,kBAAM,WAAWA,SAAQ,GAAG,IAAI,EAAE,KAAK;AAEvC,sBAAU,KAAK,QAAQ;AAEvB,mBAAO;AAAA,UACT;AAEA,iBAAO,CAAC,MAAM,cAAc;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,MACA,KAAAC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,UAAU,SAAoC;AACnD,UAAM,EAAE,aAAa,MAAM,IAAI;AAC/B,UAAM,WAAW;AACjB,UAAM,KAAK,WAAW,MAAM;AAC5B,UAAM,QAAQ,KAAK,WAAW,IAAI,QAAQ;AAC1C,UAAM,oBAAoB,OAAO;AAAA,MAC/B,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,MAAMD,QAAO,MAAM;AACnD,eAAO,CAAC,MAAM,IAAI,SAAkBA,SAAQ,GAAG,IAAI,EAAE,EAAE,GAAG,OAAO,UAAU,OAAU,CAAC,CAAC;AAAA,MACzF,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,OAAO,MAAM,KAAK,YAAY,IAAI,QAAQ;AAAA,IAC5C;AAAA,EACF;AAAA,EAEO,WAAW,IAAiB,iBAAiB,MAAoB;AACtE,UAAM,EAAE,aAAa,QAAQ,MAAM,IAAI;AACvC,UAAM,EAAE,KAAK,IAAI;AAEjB,UAAM,QAAsB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,qBAAqB;AAAA,QAC1B;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AAAA,MACD,UAAU,iBAAiB,MAAM,SAAY;AAAA,MAC7C,OAAO,MAAM,KAAK,YAAY,IAAI,cAAc;AAAA,MAChD,KAAK,MAAM,KAAK,UAAU,EAAE;AAAA,MAC5B,IAAI,WAAW;AACb,eAAO,OAAO;AAAA,UACZ,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,MAAMA,QAAO,MAAM;AACnD,mBAAO,CAAC,MAAM,IAAI,SAAkBA,SAAQ,GAAG,IAAI,EAAE,KAAK,CAAC;AAAA,UAC7D,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;;;ACtIA,SAAS,mBAAmB;AAC5B,SAAS,kBAAkB;;;ACIpB,IAAM,eAAN,MAAkD;AAAA,EAAlD;AACL,SAAQ,YAAgE,CAAC;AAAA;AAAA,EAElE,GAAqC,OAAkB,IAA0C;AACtG,QAAI,CAAC,KAAK,UAAU,KAAK,GAAG;AAC1B,WAAK,UAAU,KAAK,IAAI,CAAC;AAAA,IAC3B;AAEA,SAAK,UAAU,KAAK,EAAE,KAAK,EAAE;AAE7B,WAAO;AAAA,EACT;AAAA,EAEO,KAAuC,UAAqB,MAAwC;AACzG,UAAM,YAAY,KAAK,UAAU,KAAK;AAEtC,QAAI,WAAW;AACb,gBAAU,QAAQ,cAAY,SAAS,MAAM,MAAM,IAAI,CAAC;AAAA,IAC1D;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,IAAsC,OAAkB,IAA2C;AACxG,UAAM,YAAY,KAAK,UAAU,KAAK;AAEtC,QAAI,WAAW;AACb,UAAI,IAAI;AACN,aAAK,UAAU,KAAK,IAAI,UAAU,OAAO,cAAY,aAAa,EAAE;AAAA,MACtE,OAAO;AACL,eAAO,KAAK,UAAU,KAAK;AAAA,MAC7B;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,KAAuC,OAAkB,IAA0C;AACxG,UAAM,SAAS,IAAI,SAAqC;AACtD,WAAK,IAAI,OAAO,MAAM;AACtB,SAAG,MAAM,MAAM,IAAI;AAAA,IACrB;AAEA,WAAO,KAAK,GAAG,OAAO,MAAM;AAAA,EAC9B;AAAA,EAEO,qBAA2B;AAChC,SAAK,YAAY,CAAC;AAAA,EACpB;AACF;;;ACzDA,SAAS,cAAc;;;ACEvB,SAAS,iBAAiB;AAQnB,SAAS,wBAAwB,QAAyB,cAAwC;AACvG,QAAM,YAAY,IAAI,UAAU,MAAM;AAEtC,eAAa,QAAQ,iBAAe;AAClC,gBAAY,MAAM,QAAQ,UAAQ;AAChC,gBAAU,KAAK,IAAI;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;;;ACnBA,SAAS,WAAW,UAAU,QAAQ,iBAAiB,cAAc;;;ACDrE,IAAM,oBAAoB,CAAC,SAAsB;AAC/C,QAAM,WAAW,KAAK;AAEtB,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAChD,UAAM,QAAQ,SAAS,CAAC;AAExB,QAAI,MAAM,aAAa,KAAK,MAAM,aAAa,gBAAgB,KAAK,MAAM,SAAS,GAAG;AACpF,WAAK,YAAY,KAAK;AAAA,IACxB,WAAW,MAAM,aAAa,GAAG;AAC/B,wBAAkB,KAAoB;AAAA,IACxC;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,kBAAkB,OAA4B;AAC5D,MAAI,OAAO,WAAW,aAAa;AACjC,UAAM,IAAI,MAAM,sFAAsF;AAAA,EACxG;AAEA,QAAM,eAAe,SAAS,KAAK;AAEnC,QAAM,OAAO,IAAI,OAAO,UAAU,EAAE,gBAAgB,cAAc,WAAW,EAAE;AAE/E,SAAO,kBAAkB,IAAI;AAC/B;;;ADPO,SAAS,sBACd,SACA,QACA,SAC4B;AAC5B,MAAI,mBAAmB,mBAAmB,mBAAmB,UAAU;AACrE,WAAO;AAAA,EACT;AACA,YAAU;AAAA,IACR,OAAO;AAAA,IACP,cAAc,CAAC;AAAA,IACf,GAAG;AAAA,EACL;AAEA,QAAM,gBAAgB,OAAO,YAAY,YAAY,YAAY;AACjE,QAAM,gBAAgB,OAAO,YAAY;AAEzC,MAAI,eAAe;AACjB,QAAI;AACF,YAAM,iBAAiB,MAAM,QAAQ,OAAO,KAAK,QAAQ,SAAS;AAGlE,UAAI,gBAAgB;AAClB,eAAO,SAAS,UAAU,QAAQ,IAAI,UAAQ,OAAO,aAAa,IAAI,CAAC,CAAC;AAAA,MAC1E;AAEA,YAAM,OAAO,OAAO,aAAa,OAAO;AAExC,UAAI,QAAQ,uBAAuB;AACjC,aAAK,MAAM;AAAA,MACb;AAEA,aAAO;AAAA,IACT,SAAS,OAAO;AACd,UAAI,QAAQ,uBAAuB;AACjC,cAAM,IAAI,MAAM,wCAAwC,EAAE,OAAO,MAAe,CAAC;AAAA,MACnF;AAEA,cAAQ,KAAK,mCAAmC,iBAAiB,SAAS,UAAU,KAAK;AAEzF,aAAO,sBAAsB,IAAI,QAAQ,OAAO;AAAA,IAClD;AAAA,EACF;AAEA,MAAI,eAAe;AAEjB,QAAI,QAAQ,uBAAuB;AACjC,UAAI,oBAAoB;AACxB,UAAI,iBAAiB;AAGrB,YAAM,qBAAqB,IAAI,OAAO;AAAA,QACpC,SAAS,OAAO,KAAK;AAAA,QACrB,OAAO,OAAO,KAAK;AAAA;AAAA;AAAA,QAGnB,OAAO,OAAO,KAAK,MAAM,OAAO;AAAA,UAC9B,8CAA8C;AAAA,YAC5C,SAAS;AAAA,YACT,OAAO;AAAA,YACP,UAAU;AAAA,cACR;AAAA,gBACE,KAAK;AAAA,gBACL,UAAU,OAAK;AAEb,sCAAoB;AAEpB,mCAAiB,OAAO,MAAM,WAAW,IAAI,EAAE;AAC/C,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,UAAI,QAAQ,OAAO;AACjB,kBAAU,WAAW,kBAAkB,EAAE,WAAW,kBAAkB,OAAO,GAAG,QAAQ,YAAY;AAAA,MACtG,OAAO;AACL,kBAAU,WAAW,kBAAkB,EAAE,MAAM,kBAAkB,OAAO,GAAG,QAAQ,YAAY;AAAA,MACjG;AAEA,UAAI,QAAQ,yBAAyB,mBAAmB;AACtD,cAAM,IAAI,MAAM,wCAAwC;AAAA,UACtD,OAAO,IAAI,MAAM,0BAA0B,cAAc,EAAE;AAAA,QAC7D,CAAC;AAAA,MACH;AAAA,IACF;AAEA,UAAM,SAAS,UAAU,WAAW,MAAM;AAE1C,QAAI,QAAQ,OAAO;AACjB,aAAO,OAAO,WAAW,kBAAkB,OAAO,GAAG,QAAQ,YAAY,EAAE;AAAA,IAC7E;AAEA,WAAO,OAAO,MAAM,kBAAkB,OAAO,GAAG,QAAQ,YAAY;AAAA,EACtE;AAEA,SAAO,sBAAsB,IAAI,QAAQ,OAAO;AAClD;;;AE1GO,SAAS,eACd,SACA,QACA,eAA6B,CAAC,GAC9B,UAA+C,CAAC,GAC/B;AACjB,SAAO,sBAAsB,SAAS,QAAQ;AAAA,IAC5C,OAAO;AAAA,IACP;AAAA,IACA,uBAAuB,QAAQ;AAAA,EACjC,CAAC;AACH;;;AChBO,SAAS,eAAe,OAAsC;AACnE,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW,KAAK,GAAG;AAC3C,UAAM,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC;AAE7B,QAAI,KAAK,eAAe,CAAC,KAAK,iBAAiB,GAAG;AAChD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACPO,SAAS,aAAa,MAAuB,WAAqC;AACvF,QAAM,eAA8B,CAAC;AAErC,OAAK,YAAY,CAAC,OAAO,QAAQ;AAC/B,QAAI,UAAU,KAAK,GAAG;AACpB,mBAAa,KAAK;AAAA,QAChB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;ACZO,SAAS,oBAAoB,MAAuB,OAAc,WAAqC;AAC5G,QAAM,eAA8B,CAAC;AAarC,OAAK,aAAa,MAAM,MAAM,MAAM,IAAI,CAAC,OAAO,QAAQ;AACtD,QAAI,UAAU,KAAK,GAAG;AACpB,mBAAa,KAAK;AAAA,QAChB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;ACtBO,SAAS,2BACd,MACA,WAQY;AACZ,WAAS,IAAI,KAAK,OAAO,IAAI,GAAG,KAAK,GAAG;AACtC,UAAM,OAAO,KAAK,KAAK,CAAC;AAExB,QAAI,UAAU,IAAI,GAAG;AACnB,aAAO;AAAA,QACL,KAAK,IAAI,IAAI,KAAK,OAAO,CAAC,IAAI;AAAA,QAC9B,OAAO,KAAK,MAAM,CAAC;AAAA,QACnB,OAAO;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACvBO,SAAS,eACd,WACyE;AACzE,SAAO,CAAC,cAAyB,2BAA2B,UAAU,OAAO,SAAS;AACxF;;;ACLO,SAAS,kBACd,WACA,OACA,SACe;AACf,MAAI,UAAU,OAAO,KAAsC,MAAM,UAAa,UAAU,QAAQ;AAC9F,WAAO,kBAAkB,UAAU,QAAQ,OAAO,OAAO;AAAA,EAC3D;AAEA,MAAI,OAAO,UAAU,OAAO,KAAsC,MAAM,YAAY;AAClF,UAAM,QAAS,UAAU,OAAO,KAAsC,EAAU,KAAK;AAAA,MACnF,GAAG;AAAA,MACH,QAAQ,UAAU,SAAS,kBAAkB,UAAU,QAAQ,OAAO,OAAO,IAAI;AAAA,IACnF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,OAAO,KAAsC;AAChE;;;ACvBO,SAAS,kBAAkB,YAAoC;AACpE,SACE,WACG,IAAI,eAAa;AAChB,UAAM,UAAU;AAAA,MACd,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,SAAS,UAAU;AAAA,IACrB;AAEA,UAAM,gBAAgB,kBAA8C,WAAW,iBAAiB,OAAO;AAEvG,QAAI,eAAe;AACjB,aAAO,CAAC,WAAW,GAAG,kBAAkB,cAAc,CAAC,CAAC;AAAA,IAC1D;AAEA,WAAO;AAAA,EACT,CAAC,EAEA,KAAK,EAAE;AAEd;;;AC7BA,SAAS,YAAY;;;ACCrB,SAAS,qBAAqB;AAEvB,SAAS,oBAAoB,UAAoB,QAAwB;AAC9E,QAAM,mBAAmB,cAAc,WAAW,MAAM,EAAE,kBAAkB,QAAQ;AAEpF,QAAM,oBAAoB,SAAS,eAAe,mBAAmB;AACrE,QAAM,YAAY,kBAAkB,cAAc,KAAK;AAEvD,YAAU,YAAY,gBAAgB;AAEtC,SAAO,UAAU;AACnB;;;ACXA,SAAS,UAAAE,eAAc;;;ACAhB,SAAS,WAAW,OAA+B;AACxD,SAAO,OAAO,UAAU;AAC1B;;;ACOO,SAAS,aAAgB,OAAU,UAAe,WAAc,OAAkC;AACvG,MAAI,WAAW,KAAK,GAAG;AACrB,QAAI,SAAS;AACX,aAAO,MAAM,KAAK,OAAO,EAAE,GAAG,KAAK;AAAA,IACrC;AAEA,WAAO,MAAM,GAAG,KAAK;AAAA,EACvB;AAEA,SAAO;AACT;;;ACpBO,SAAS,cAAc,QAAQ,CAAC,GAAY;AACjD,SAAO,OAAO,KAAK,KAAK,EAAE,WAAW,KAAK,MAAM,gBAAgB;AAClE;;;ACGO,SAAS,gBAAgB,YAAwB;AACtD,QAAM,iBAAiB,WAAW,OAAO,eAAa,UAAU,SAAS,WAAW;AACpF,QAAM,iBAAiB,WAAW,OAAO,eAAa,UAAU,SAAS,MAAM;AAC/E,QAAM,iBAAiB,WAAW,OAAO,eAAa,UAAU,SAAS,MAAM;AAE/E,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACNO,SAAS,4BAA4B,YAA8C;AACxF,QAAM,sBAA4C,CAAC;AACnD,QAAM,EAAE,gBAAgB,eAAe,IAAI,gBAAgB,UAAU;AACrE,QAAM,wBAAwB,CAAC,GAAG,gBAAgB,GAAG,cAAc;AACnE,QAAM,mBAAwF;AAAA,IAC5F,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,EACd;AAEA,aAAW,QAAQ,eAAa;AAC9B,UAAM,UAAU;AAAA,MACd,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,SAAS,UAAU;AAAA,MACnB,YAAY;AAAA,IACd;AAEA,UAAM,sBAAsB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,qBAAqB;AACxB;AAAA,IACF;AAEA,UAAM,mBAAmB,oBAAoB;AAE7C,qBAAiB,QAAQ,qBAAmB;AAC1C,sBAAgB,MAAM,QAAQ,UAAQ;AACpC,eAAO,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,CAAC,CAAC,MAAM,SAAS,MAAM;AACxE,8BAAoB,KAAK;AAAA,YACvB;AAAA,YACA;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,wBAAsB,QAAQ,eAAa;AACzC,UAAM,UAAU;AAAA,MACd,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,SAAS,UAAU;AAAA,IACrB;AAEA,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AAGA,UAAM,aAAa,cAAc;AAEjC,WAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,CAAC,MAAM,SAAS,MAAM;AACxD,YAAM,aAAa;AAAA,QACjB,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAEA,UAAI,QAAO,yCAAY,aAAY,YAAY;AAC7C,mBAAW,UAAU,WAAW,QAAQ;AAAA,MAC1C;AAEA,WAAI,yCAAY,gBAAc,yCAAY,aAAY,QAAW;AAC/D,eAAO,WAAW;AAAA,MACpB;AAEA,0BAAoB,KAAK;AAAA,QACvB,MAAM,UAAU;AAAA,QAChB;AAAA,QACA,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;;;ACtGO,SAAS,mBAAmB,SAAqD;AACtF,SAAO,QACJ,OAAO,UAAQ,CAAC,CAAC,IAAI,EACrB,OAAO,CAAC,OAAO,SAAS;AACvB,UAAM,mBAAmB,EAAE,GAAG,MAAM;AAEpC,WAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAM,SAAS,iBAAiB,GAAG;AAEnC,UAAI,CAAC,QAAQ;AACX,yBAAiB,GAAG,IAAI;AAExB;AAAA,MACF;AAEA,UAAI,QAAQ,SAAS;AACnB,cAAM,eAAyB,QAAQ,OAAO,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;AACnE,cAAM,kBAA4B,iBAAiB,GAAG,IAAI,iBAAiB,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;AAE9F,cAAM,gBAAgB,aAAa,OAAO,gBAAc,CAAC,gBAAgB,SAAS,UAAU,CAAC;AAE7F,yBAAiB,GAAG,IAAI,CAAC,GAAG,iBAAiB,GAAG,aAAa,EAAE,KAAK,GAAG;AAAA,MACzE,WAAW,QAAQ,SAAS;AAC1B,cAAM,YAAsB,QACxB,MACG,MAAM,GAAG,EACT,IAAI,CAACC,WAAkBA,OAAM,KAAK,CAAC,EACnC,OAAO,OAAO,IACjB,CAAC;AACL,cAAM,iBAA2B,iBAAiB,GAAG,IACjD,iBAAiB,GAAG,EACjB,MAAM,GAAG,EACT,IAAI,CAACA,WAAkBA,OAAM,KAAK,CAAC,EACnC,OAAO,OAAO,IACjB,CAAC;AAEL,cAAM,WAAW,oBAAI,IAAoB;AAEzC,uBAAe,QAAQ,CAAAA,WAAS;AAC9B,gBAAM,CAAC,UAAU,GAAG,IAAIA,OAAM,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AAEhE,mBAAS,IAAI,UAAU,GAAG;AAAA,QAC5B,CAAC;AAED,kBAAU,QAAQ,CAAAA,WAAS;AACzB,gBAAM,CAAC,UAAU,GAAG,IAAIA,OAAM,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AAEhE,mBAAS,IAAI,UAAU,GAAG;AAAA,QAC5B,CAAC;AAED,yBAAiB,GAAG,IAAI,MAAM,KAAK,SAAS,QAAQ,CAAC,EAClD,IAAI,CAAC,CAAC,UAAU,GAAG,MAAM,GAAG,QAAQ,KAAK,GAAG,EAAE,EAC9C,KAAK,IAAI;AAAA,MACd,OAAO;AACL,yBAAiB,GAAG,IAAI;AAAA,MAC1B;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACT;;;ACvDO,SAAS,sBACd,YACA,qBACqB;AACrB,SAAO,oBACJ,OAAO,eAAa,UAAU,SAAS,WAAW,KAAK,IAAI,EAC3D,OAAO,UAAQ,KAAK,UAAU,QAAQ,EACtC,IAAI,UAAQ;AACX,QAAI,CAAC,KAAK,UAAU,YAAY;AAC9B,aAAO;AAAA,QACL,CAAC,KAAK,IAAI,GAAG,WAAW,MAAM,KAAK,IAAI;AAAA,MACzC;AAAA,IACF;AAEA,WAAO,KAAK,UAAU,WAAW,WAAW,KAAK,KAAK,CAAC;AAAA,EACzD,CAAC,EACA,OAAO,CAAC,YAAY,cAAc,gBAAgB,YAAY,SAAS,GAAG,CAAC,CAAC;AACjF;;;ACtBO,SAAS,WAAW,OAAiB;AAC1C,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,MAAM,sBAAsB,GAAG;AACvC,WAAO,OAAO,KAAK;AAAA,EACrB;AAEA,MAAI,UAAU,QAAQ;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,SAAS;AACrB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACPO,SAAS,qCACd,WACA,qBACW;AACX,MAAI,WAAW,WAAW;AACxB,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,UAAU,CAAC,SAAsB;AAC/B,YAAM,gBAAgB,UAAU,WAAW,UAAU,SAAS,IAAI,IAAI,UAAU;AAEhF,UAAI,kBAAkB,OAAO;AAC3B,eAAO;AAAA,MACT;AAEA,YAAM,gBAAgB,oBAAoB,OAAO,CAAC,OAAO,SAAS;AAChE,cAAM,QAAQ,KAAK,UAAU,YACzB,KAAK,UAAU,UAAU,IAAI,IAC7B,WAAW,KAAK,aAAa,KAAK,IAAI,CAAC;AAE3C,YAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,UACL,GAAG;AAAA,UACH,CAAC,KAAK,IAAI,GAAG;AAAA,QACf;AAAA,MACF,GAAG,CAAC,CAAC;AAEL,aAAO,EAAE,GAAG,eAAe,GAAG,cAAc;AAAA,IAC9C;AAAA,EACF;AACF;;;ATjCA,SAAS,kBAAqB,MAAS;AACrC,SAAO,OAAO;AAAA;AAAA,IAEZ,OAAO,QAAQ,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,MAAM;AAC5C,UAAI,QAAQ,WAAW,cAAc,KAA2B,GAAG;AACjE,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,QAAQ,UAAU;AAAA,IACrC,CAAC;AAAA,EACH;AACF;AAQO,SAAS,8BAA8B,YAAwB,QAAyB;AAhC/F;AAiCE,QAAM,gBAAgB,4BAA4B,UAAU;AAC5D,QAAM,EAAE,gBAAgB,eAAe,IAAI,gBAAgB,UAAU;AACrE,QAAM,WAAU,oBAAe,KAAK,eAAa,kBAAkB,WAAW,SAAS,CAAC,MAAxE,mBAA2E;AAE3F,QAAM,QAAQ,OAAO;AAAA,IACnB,eAAe,IAAI,eAAa;AAC9B,YAAM,sBAAsB,cAAc,OAAO,eAAa,UAAU,SAAS,UAAU,IAAI;AAC/F,YAAM,UAAU;AAAA,QACd,MAAM,UAAU;AAAA,QAChB,SAAS,UAAU;AAAA,QACnB,SAAS,UAAU;AAAA,QACnB;AAAA,MACF;AAEA,YAAM,kBAAkB,WAAW,OAAO,CAAC,QAAQ,MAAM;AACvD,cAAM,mBAAmB,kBAAiD,GAAG,oBAAoB,OAAO;AAExG,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAI,mBAAmB,iBAAiB,SAAS,IAAI,CAAC;AAAA,QACxD;AAAA,MACF,GAAG,CAAC,CAAC;AAEL,YAAM,SAAmB,kBAAkB;AAAA,QACzC,GAAG;AAAA,QACH,SAAS,aAAa,kBAAyC,WAAW,WAAW,OAAO,CAAC;AAAA,QAC7F,OAAO,aAAa,kBAAuC,WAAW,SAAS,OAAO,CAAC;AAAA,QACvF,OAAO,aAAa,kBAAuC,WAAW,SAAS,OAAO,CAAC;AAAA,QACvF,QAAQ,aAAa,kBAAwC,WAAW,UAAU,OAAO,CAAC;AAAA,QAC1F,MAAM,aAAa,kBAAsC,WAAW,QAAQ,OAAO,CAAC;AAAA,QACpF,YAAY,aAAa,kBAA4C,WAAW,cAAc,OAAO,CAAC;AAAA,QACtG,WAAW,aAAa,kBAA2C,WAAW,aAAa,OAAO,CAAC;AAAA,QACnG,MAAM,aAAa,kBAAsC,WAAW,QAAQ,OAAO,CAAC;AAAA,QACpF,YAAY,aAAa,kBAA4C,WAAW,cAAc,OAAO,CAAC;AAAA,QACtG,sBAAsB;AAAA,UACpB,kBAAsD,WAAW,wBAAwB,OAAO;AAAA,QAClG;AAAA,QACA,UAAU,aAAa,kBAA0C,WAAW,YAAY,OAAO,CAAC;AAAA,QAChG,WAAW,aAAa,kBAA2C,WAAW,aAAa,OAAO,CAAC;AAAA,QACnG,OAAO,OAAO;AAAA,UACZ,oBAAoB,IAAI,wBAAsB;AAzExD,gBAAAC,KAAA;AA0EY,mBAAO;AAAA,cACL,mBAAmB;AAAA,cACnB,EAAE,UAASA,MAAA,yDAAoB,cAApB,gBAAAA,IAA+B,SAAS,WAAU,8DAAoB,cAApB,mBAA+B,SAAS;AAAA,YACvG;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,YAAM,YAAY,aAAa,kBAA2C,WAAW,aAAa,OAAO,CAAC;AAE1G,UAAI,WAAW;AACb,eAAO,WAAW,UAAU;AAAA,UAAI,eAC9B,qCAAqC,WAAW,mBAAmB;AAAA,QACrE;AAAA,MACF;AAEA,YAAM,aAAa,kBAA4C,WAAW,cAAc,OAAO;AAE/F,UAAI,YAAY;AACd,eAAO,QAAQ,UACb,WAAW;AAAA,UACT;AAAA,UACA,gBAAgB,sBAAsB,MAAM,mBAAmB;AAAA,QACjE,CAAC;AAAA,MACL;AAEA,YAAM,aAAa,kBAA4C,WAAW,cAAc,OAAO;AAE/F,UAAI,YAAY;AACd,eAAO,SAAS;AAAA,MAClB;AAEA,aAAO,CAAC,UAAU,MAAM,MAAM;AAAA,IAChC,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,OAAO;AAAA,IACnB,eAAe,IAAI,eAAa;AAC9B,YAAM,sBAAsB,cAAc,OAAO,eAAa,UAAU,SAAS,UAAU,IAAI;AAC/F,YAAM,UAAU;AAAA,QACd,MAAM,UAAU;AAAA,QAChB,SAAS,UAAU;AAAA,QACnB,SAAS,UAAU;AAAA,QACnB;AAAA,MACF;AAEA,YAAM,kBAAkB,WAAW,OAAO,CAAC,QAAQ,MAAM;AACvD,cAAM,mBAAmB,kBAAiD,GAAG,oBAAoB,OAAO;AAExG,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAI,mBAAmB,iBAAiB,SAAgB,IAAI,CAAC;AAAA,QAC/D;AAAA,MACF,GAAG,CAAC,CAAC;AAEL,YAAM,SAAmB,kBAAkB;AAAA,QACzC,GAAG;AAAA,QACH,WAAW,aAAa,kBAA2C,WAAW,aAAa,OAAO,CAAC;AAAA,QACnG,UAAU,aAAa,kBAA0C,WAAW,YAAY,OAAO,CAAC;AAAA,QAChG,OAAO,aAAa,kBAAuC,WAAW,SAAS,OAAO,CAAC;AAAA,QACvF,UAAU,aAAa,kBAA0C,WAAW,YAAY,OAAO,CAAC;AAAA,QAChG,MAAM,aAAa,kBAAsC,WAAW,QAAQ,OAAO,CAAC;AAAA,QACpF,OAAO,OAAO;AAAA,UACZ,oBAAoB,IAAI,wBAAsB;AAzIxD,gBAAAA,KAAA;AA0IY,mBAAO;AAAA,cACL,mBAAmB;AAAA,cACnB,EAAE,UAASA,MAAA,yDAAoB,cAApB,gBAAAA,IAA+B,SAAS,WAAU,8DAAoB,cAApB,mBAA+B,SAAS;AAAA,YACvG;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,YAAM,YAAY,aAAa,kBAA2C,WAAW,aAAa,OAAO,CAAC;AAE1G,UAAI,WAAW;AACb,eAAO,WAAW,UAAU;AAAA,UAAI,eAC9B,qCAAqC,WAAW,mBAAmB;AAAA,QACrE;AAAA,MACF;AAEA,YAAM,aAAa,kBAA4C,WAAW,cAAc,OAAO;AAE/F,UAAI,YAAY;AACd,eAAO,QAAQ,UACb,WAAW;AAAA,UACT;AAAA,UACA,gBAAgB,sBAAsB,MAAM,mBAAmB;AAAA,QACjE,CAAC;AAAA,MACL;AAEA,aAAO,CAAC,UAAU,MAAM,MAAM;AAAA,IAChC,CAAC;AAAA,EACH;AAEA,SAAO,IAAIC,QAAO;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AU1KO,SAAS,eAAkB,OAAiB;AACjD,QAAM,WAAW,MAAM,OAAO,CAAC,IAAI,UAAU,MAAM,QAAQ,EAAE,MAAM,KAAK;AAExE,SAAO,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC;AACrC;;;ACCO,SAAS,eAAe,YAAoC;AACjE,QAAM,kBAAkB;AAExB,SAAO,WAAW,KAAK,CAAC,GAAG,MAAM;AAC/B,UAAM,YAAY,kBAAyC,GAAG,UAAU,KAAK;AAC7E,UAAM,YAAY,kBAAyC,GAAG,UAAU,KAAK;AAE7E,QAAI,YAAY,WAAW;AACzB,aAAO;AAAA,IACT;AAEA,QAAI,YAAY,WAAW;AACzB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT,CAAC;AACH;;;ACdO,SAAS,kBAAkB,YAAoC;AACpE,QAAM,qBAAqB,eAAe,kBAAkB,UAAU,CAAC;AACvE,QAAM,kBAAkB,eAAe,mBAAmB,IAAI,eAAa,UAAU,IAAI,CAAC;AAE1F,MAAI,gBAAgB,QAAQ;AAC1B,YAAQ;AAAA,MACN,oDAAoD,gBACjD,IAAI,UAAQ,IAAI,IAAI,GAAG,EACvB,KAAK,IAAI,CAAC;AAAA,IACf;AAAA,EACF;AAEA,SAAO;AACT;;;ACjBO,SAAS,UAAU,YAAwB,QAAyB;AACzE,QAAM,qBAAqB,kBAAkB,UAAU;AAEvD,SAAO,8BAA8B,oBAAoB,MAAM;AACjE;;;AfCO,SAAS,aAAa,KAAkB,YAAgC;AAC7E,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,cAAc,KAAK,SAAS,QAAQ,GAAG;AAE7C,SAAO,oBAAoB,YAAY,SAAS,MAAM;AACxD;;;AgBjBA,SAAS,aAAAC,kBAAiB;AAYnB,SAAS,aAAa,MAAc,YAA6C;AACtF,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,MAAM,kBAAkB,IAAI;AAElC,SAAOC,WAAU,WAAW,MAAM,EAAE,MAAM,GAAG,EAAE,OAAO;AACxD;;;ACjBA,SAAS,QAAAC,aAAY;;;ACYd,SAAS,eACd,WACA,OACA,SAIQ;AACR,QAAM,EAAE,MAAM,GAAG,IAAI;AACrB,QAAM,EAAE,iBAAiB,QAAQ,kBAAkB,CAAC,EAAE,IAAI,WAAW,CAAC;AACtE,MAAI,OAAO;AAEX,YAAU,aAAa,MAAM,IAAI,CAAC,MAAM,KAAK,QAAQ,UAAU;AAxBjE;AAyBI,QAAI,KAAK,WAAW,MAAM,MAAM;AAC9B,cAAQ;AAAA,IACV;AAEA,UAAM,iBAAiB,mDAAkB,KAAK,KAAK;AAEnD,QAAI,gBAAgB;AAClB,UAAI,QAAQ;AACV,gBAAQ,eAAe;AAAA,UACrB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,QAAQ;AACf,eAAQ,kCAAM,SAAN,mBAAY,MAAM,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,KAAK;AAAA,IAC5D;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;ACrCO,SAAS,QACd,MACA,SAIA;AACA,QAAM,QAAQ;AAAA,IACZ,MAAM;AAAA,IACN,IAAI,KAAK,QAAQ;AAAA,EACnB;AAEA,SAAO,eAAe,MAAM,OAAO,OAAO;AAC5C;;;AClBO,SAAS,6BAA6B,QAAgD;AAC3F,SAAO,OAAO;AAAA,IACZ,OAAO,QAAQ,OAAO,KAAK,EACxB,OAAO,CAAC,CAAC,EAAE,IAAI,MAAM,KAAK,KAAK,MAAM,EACrC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,EACnD;AACF;;;AHDO,SAAS,aACd,KACA,YACA,SAIQ;AACR,QAAM,EAAE,iBAAiB,QAAQ,kBAAkB,CAAC,EAAE,IAAI,WAAW,CAAC;AACtE,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,cAAcC,MAAK,SAAS,QAAQ,GAAG;AAE7C,SAAO,QAAQ,aAAa;AAAA,IAC1B;AAAA,IACA,iBAAiB;AAAA,MACf,GAAG,6BAA6B,MAAM;AAAA,MACtC,GAAG;AAAA,IACL;AAAA,EACF,CAAC;AACH;;;AI/BO,SAAS,YAAY,YAA+B,QAA0B;AACnF,MAAI,OAAO,eAAe,UAAU;AAClC,QAAI,CAAC,OAAO,MAAM,UAAU,GAAG;AAC7B,YAAM,MAAM,gCAAgC,UAAU,2CAA2C;AAAA,IACnG;AAEA,WAAO,OAAO,MAAM,UAAU;AAAA,EAChC;AAEA,SAAO;AACT;;;ACPO,SAAS,kBAAkB,OAAoB,YAAoD;AACxG,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,MAAM,IAAI,MAAM,IAAI,MAAM;AAClC,QAAM,QAAgB,CAAC;AAEvB,MAAI,OAAO;AACT,QAAI,MAAM,aAAa;AACrB,YAAM,KAAK,GAAG,MAAM,WAAW;AAAA,IACjC;AAEA,UAAM,KAAK,GAAG,MAAM,UAAU,MAAM,MAAM,CAAC;AAAA,EAC7C,OAAO;AACL,UAAM,IAAI,aAAa,MAAM,IAAI,UAAQ;AACvC,YAAM,KAAK,GAAG,KAAK,KAAK;AAAA,IAC1B,CAAC;AAAA,EACH;AAEA,QAAM,OAAO,MAAM,KAAK,cAAY,SAAS,KAAK,SAAS,KAAK,IAAI;AAEpE,MAAI,CAAC,MAAM;AACT,WAAO,CAAC;AAAA,EACV;AAEA,SAAO,EAAE,GAAG,KAAK,MAAM;AACzB;;;AC3BO,SAAS,YAAY,YAA+B,QAA0B;AACnF,MAAI,OAAO,eAAe,UAAU;AAClC,QAAI,CAAC,OAAO,MAAM,UAAU,GAAG;AAC7B,YAAM,MAAM,gCAAgC,UAAU,2CAA2C;AAAA,IACnG;AAEA,WAAO,OAAO,MAAM,UAAU;AAAA,EAChC;AAEA,SAAO;AACT;;;ACPO,SAAS,kBAAkB,OAAoB,YAAoD;AACxG,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,MAAM,GAAG,IAAI,MAAM;AAC3B,QAAM,QAAgB,CAAC;AAEvB,QAAM,IAAI,aAAa,MAAM,IAAI,CAAAC,UAAQ;AACvC,UAAM,KAAKA,KAAI;AAAA,EACjB,CAAC;AAED,QAAM,OAAO,MAAM,QAAQ,EAAE,KAAK,cAAY,SAAS,KAAK,SAAS,KAAK,IAAI;AAE9E,MAAI,CAAC,MAAM;AACT,WAAO,CAAC;AAAA,EACV;AAEA,SAAO,EAAE,GAAG,KAAK,MAAM;AACzB;;;ACbO,SAAS,wBAAwB,MAAc,QAAwC;AAC5F,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACLO,SAAS,cAAc,OAAoB,YAA+D;AAC/G,QAAM,aAAa;AAAA,IACjB,OAAO,eAAe,WAAW,aAAa,WAAW;AAAA,IACzD,MAAM;AAAA,EACR;AAEA,MAAI,eAAe,QAAQ;AACzB,WAAO,kBAAkB,OAAO,UAAsB;AAAA,EACxD;AAEA,MAAI,eAAe,QAAQ;AACzB,WAAO,kBAAkB,OAAO,UAAsB;AAAA,EACxD;AAEA,SAAO,CAAC;AACV;;;ACxBO,SAAS,iBAAoB,OAAY,KAAK,KAAK,WAAgB;AACxE,QAAM,OAAyB,CAAC;AAEhC,SAAO,MAAM,OAAO,UAAQ;AAC1B,UAAM,MAAM,GAAG,IAAI;AAEnB,WAAO,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,IAAI,QAAS,KAAK,GAAG,IAAI;AAAA,EAChF,CAAC;AACH;;;ACEA,SAAS,sBAAsB,SAAyC;AACtE,QAAM,gBAAgB,iBAAiB,OAAO;AAE9C,SAAO,cAAc,WAAW,IAC5B,gBACA,cAAc,OAAO,CAAC,QAAQ,UAAU;AACtC,UAAM,OAAO,cAAc,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK;AAEvD,WAAO,CAAC,KAAK,KAAK,iBAAe;AAC/B,aACE,OAAO,SAAS,QAAQ,YAAY,SAAS,QAC7C,OAAO,SAAS,MAAM,YAAY,SAAS,MAC3C,OAAO,SAAS,QAAQ,YAAY,SAAS,QAC7C,OAAO,SAAS,MAAM,YAAY,SAAS;AAAA,IAE/C,CAAC;AAAA,EACH,CAAC;AACP;AAMO,SAAS,iBAAiB,WAAsC;AACrE,QAAM,EAAE,SAAS,MAAM,IAAI;AAC3B,QAAM,UAA0B,CAAC;AAEjC,UAAQ,KAAK,QAAQ,CAAC,SAAS,UAAU;AACvC,UAAM,SAAkB,CAAC;AAKzB,QAAI,CAAC,QAAQ,OAAO,QAAQ;AAC1B,YAAM,EAAE,MAAM,GAAG,IAAI,MAAM,KAAK;AAKhC,UAAI,SAAS,UAAa,OAAO,QAAW;AAC1C;AAAA,MACF;AAEA,aAAO,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,IAC1B,OAAO;AACL,cAAQ,QAAQ,CAAC,MAAM,OAAO;AAC5B,eAAO,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,WAAO,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;AAC/B,YAAM,WAAW,QAAQ,MAAM,KAAK,EAAE,IAAI,MAAM,EAAE;AAClD,YAAM,SAAS,QAAQ,MAAM,KAAK,EAAE,IAAI,EAAE;AAC1C,YAAM,WAAW,QAAQ,OAAO,EAAE,IAAI,UAAU,EAAE;AAClD,YAAM,SAAS,QAAQ,OAAO,EAAE,IAAI,MAAM;AAE1C,cAAQ,KAAK;AAAA,QACX,UAAU;AAAA,UACR,MAAM;AAAA,UACN,IAAI;AAAA,QACN;AAAA,QACA,UAAU;AAAA,UACR,MAAM;AAAA,UACN,IAAI;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,SAAO,sBAAsB,OAAO;AACtC;;;AC3EO,SAAS,aAAa,MAAuB,cAAc,GAAqB;AACrF,QAAM,YAAY,KAAK,SAAS,KAAK,KAAK,OAAO;AACjD,QAAM,YAAY,YAAY,IAAI;AAClC,QAAM,OAAO;AACb,QAAM,KAAK,OAAO,KAAK;AACvB,QAAM,QAAQ,KAAK,MAAM,IAAI,UAAQ;AACnC,UAAMC,UAAwD;AAAA,MAC5D,MAAM,KAAK,KAAK;AAAA,IAClB;AAEA,QAAI,OAAO,KAAK,KAAK,KAAK,EAAE,QAAQ;AAClC,MAAAA,QAAO,QAAQ,EAAE,GAAG,KAAK,MAAM;AAAA,IACjC;AAEA,WAAOA;AAAA,EACT,CAAC;AACD,QAAM,QAAQ,EAAE,GAAG,KAAK,MAAM;AAC9B,QAAM,SAA2B;AAAA,IAC/B,MAAM,KAAK,KAAK;AAAA,IAChB;AAAA,IACA;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,KAAK,EAAE,QAAQ;AAC7B,WAAO,QAAQ;AAAA,EACjB;AAEA,MAAI,MAAM,QAAQ;AAChB,WAAO,QAAQ;AAAA,EACjB;AAEA,MAAI,KAAK,QAAQ,YAAY;AAC3B,WAAO,UAAU,CAAC;AAElB,SAAK,QAAQ,CAAC,OAAO,WAAW;AA3CpC;AA4CM,mBAAO,YAAP,mBAAgB,KAAK,aAAa,OAAO,cAAc,SAAS,SAAS;AAAA,IAC3E,CAAC;AAAA,EACH;AAEA,MAAI,KAAK,MAAM;AACb,WAAO,OAAO,KAAK;AAAA,EACrB;AAEA,SAAO;AACT;;;ACrDO,SAAS,SAAS,OAA6B;AACpD,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACnD;;;ACKO,SAAS,eACd,SACA,SACA,UAA+B,EAAE,QAAQ,KAAK,GACrC;AACT,QAAM,OAAO,OAAO,KAAK,OAAO;AAEhC,MAAI,CAAC,KAAK,QAAQ;AAChB,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,SAAO;AACvB,QAAI,QAAQ,QAAQ;AAClB,aAAO,QAAQ,GAAG,MAAM,QAAQ,GAAG;AAAA,IACrC;AAEA,QAAI,SAAS,QAAQ,GAAG,CAAC,GAAG;AAC1B,aAAO,QAAQ,GAAG,EAAE,KAAK,QAAQ,GAAG,CAAC;AAAA,IACvC;AAEA,WAAO,QAAQ,GAAG,MAAM,QAAQ,GAAG;AAAA,EACrC,CAAC;AACH;;;ACxBA,SAAS,cACP,OACA,MACA,aAAkC,CAAC,GACN;AAC7B,SAAO,MAAM,KAAK,UAAQ;AACxB,WACE,KAAK,SAAS,QACd;AAAA;AAAA,MAEE,OAAO,YAAY,OAAO,KAAK,UAAU,EAAE,IAAI,OAAK,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,MACvE;AAAA,IACF;AAAA,EAEJ,CAAC;AACH;AAEA,SAAS,YAAY,OAA0B,MAAgB,aAAkC,CAAC,GAAY;AAC5G,SAAO,CAAC,CAAC,cAAc,OAAO,MAAM,UAAU;AAChD;AAKO,SAAS,aAId,MAIA,MAKA,YACc;AA3ChB;AA4CE,MAAI,CAAC,QAAQ,CAAC,MAAM;AAClB;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,OAAO,WAAW,KAAK,YAAY;AAGpD,MAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,CAAAC,UAAQA,MAAK,SAAS,IAAI,GAAG;AACrE,YAAQ,KAAK,OAAO,YAAY,KAAK,YAAY;AAAA,EACnD;AAGA,MAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,CAAAA,UAAQA,MAAK,SAAS,IAAI,GAAG;AACrE;AAAA,EACF;AAGA,eAAa,gBAAc,WAAM,KAAK,MAAM,CAAC,MAAlB,mBAAqB;AAIhD,QAAM,OAAO,cAAc,CAAC,GAAG,MAAM,KAAK,KAAK,GAAG,MAAM,UAAU;AAElE,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AAEA,MAAI,aAAa,MAAM;AACvB,MAAI,WAAW,KAAK,MAAM,IAAI,MAAM;AACpC,MAAI,WAAW,aAAa;AAC5B,MAAI,SAAS,WAAW,MAAM,KAAK;AAEnC,SAAO,aAAa,KAAK,YAAY,CAAC,GAAG,KAAK,OAAO,MAAM,aAAa,CAAC,EAAE,KAAK,GAAG,MAAM,UAAU,GAAG;AACpG,kBAAc;AACd,gBAAY,KAAK,OAAO,MAAM,UAAU,EAAE;AAAA,EAC5C;AAEA,SAAO,WAAW,KAAK,OAAO,cAAc,YAAY,CAAC,GAAG,KAAK,OAAO,MAAM,QAAQ,EAAE,KAAK,GAAG,MAAM,UAAU,GAAG;AACjH,cAAU,KAAK,OAAO,MAAM,QAAQ,EAAE;AACtC,gBAAY;AAAA,EACd;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,IAAI;AAAA,EACN;AACF;;;ACpFO,SAAS,gBAAgB,MAAc,IAAY,KAAmC;AAC3F,QAAM,QAAqB,CAAC;AAG5B,MAAI,SAAS,IAAI;AACf,QACG,QAAQ,IAAI,EACZ,MAAM,EACN,QAAQ,UAAQ;AACf,YAAM,OAAO,IAAI,QAAQ,IAAI;AAC7B,YAAM,QAAQ,aAAa,MAAM,KAAK,IAAI;AAE1C,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AAEA,YAAM,KAAK;AAAA,QACT;AAAA,QACA,GAAG;AAAA,MACL,CAAC;AAAA,IACH,CAAC;AAAA,EACL,OAAO;AACL,QAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AACxC,UAAI,CAAC,SAAQ,6BAAM,cAAa,QAAW;AACzC;AAAA,MACF;AAEA,YAAM;AAAA,QACJ,GAAG,KAAK,MAAM,IAAI,WAAS;AAAA,UACzB,MAAM;AAAA,UACN,IAAI,MAAM,KAAK;AAAA,UACf;AAAA,QACF,EAAE;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AChCO,IAAM,oBAAoB,CAAC,OAAoB,YAA+B,KAAa,WAAW,OAAO;AAClH,QAAM,OAAO,MAAM,IAAI,QAAQ,GAAG;AAElC,MAAI,eAAe;AACnB,MAAI,OAAoB;AAExB,SAAO,eAAe,KAAK,SAAS,MAAM;AACxC,UAAM,cAAc,KAAK,KAAK,YAAY;AAE1C,SAAI,2CAAa,KAAK,UAAS,YAAY;AACzC,aAAO;AAAA,IACT,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF;AAEA,SAAO,CAAC,MAAM,YAAY;AAC5B;;;ACpBO,SAAS,oBAAoB,MAAc,QAA4C;AAC5F,SAAO,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK;AACrD;;;ACDO,SAAS,sBACd,qBACA,UACA,YACqB;AACrB,SAAO,OAAO;AAAA,IACZ,OAAO,QAAQ,UAAU,EAAE,OAAO,CAAC,CAAC,IAAI,MAAM;AAC5C,YAAM,qBAAqB,oBAAoB,KAAK,UAAQ;AAC1D,eAAO,KAAK,SAAS,YAAY,KAAK,SAAS;AAAA,MACjD,CAAC;AAED,UAAI,CAAC,oBAAoB;AACvB,eAAO;AAAA,MACT;AAEA,aAAO,mBAAmB,UAAU;AAAA,IACtC,CAAC;AAAA,EACH;AACF;;;ACnBO,IAAM,0BAA0B,CAAC,OAAoB,WAAW,QAAQ;AAC7E,MAAI,aAAa;AAEjB,QAAM,cAAc,MAAM;AAE1B,QAAM,OAAO,aAAa,KAAK,IAAI,GAAG,cAAc,QAAQ,GAAG,aAAa,CAAC,MAAM,KAAK,QAAQ,UAAU;AAb5G;AAcI,UAAM,UACJ,gBAAK,KAAK,MAAK,WAAf,4BAAwB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,OACA,KAAK,eACL;AAEF,kBAAc,KAAK,UAAU,CAAC,KAAK,SAAS,QAAQ,MAAM,MAAM,GAAG,KAAK,IAAI,GAAG,cAAc,GAAG,CAAC;AAAA,EACnG,CAAC;AAED,SAAO;AACT;;;ACrBO,SAAS,aACd,OACA,YACA,aAAkC,CAAC,GAC1B;AACT,QAAM,EAAE,OAAO,OAAO,IAAI,MAAM;AAChC,QAAM,OAAO,aAAa,YAAY,YAAY,MAAM,MAAM,IAAI;AAElE,MAAI,OAAO;AACT,WAAO,CAAC,EAAE,MAAM,eAAe,MAAM,UAAU,MAAM,MAAM,GACxD,OAAO,UAAQ;AACd,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,SAAS,KAAK,KAAK;AAAA,IACjC,CAAC,EACA,KAAK,UAAQ,eAAe,KAAK,OAAO,YAAY,EAAE,QAAQ,MAAM,CAAC,CAAC;AAAA,EAC3E;AAEA,MAAI,iBAAiB;AACrB,QAAM,aAA0B,CAAC;AAEjC,SAAO,QAAQ,CAAC,EAAE,OAAO,IAAI,MAAM;AACjC,UAAM,OAAO,MAAM;AACnB,UAAM,KAAK,IAAI;AAEf,UAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AAC9C,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,MAAM,QAAQ;AACtC;AAAA,MACF;AAEA,YAAM,eAAe,KAAK,IAAI,MAAM,GAAG;AACvC,YAAM,aAAa,KAAK,IAAI,IAAI,MAAM,KAAK,QAAQ;AACnD,YAAMC,SAAQ,aAAa;AAE3B,wBAAkBA;AAElB,iBAAW;AAAA,QACT,GAAG,KAAK,MAAM,IAAI,WAAS;AAAA,UACzB;AAAA,UACA,MAAM;AAAA,UACN,IAAI;AAAA,QACN,EAAE;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,MAAI,mBAAmB,GAAG;AACxB,WAAO;AAAA,EACT;AAGA,QAAM,eAAe,WAClB,OAAO,eAAa;AACnB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,SAAS,UAAU,KAAK,KAAK;AAAA,EAC3C,CAAC,EACA,OAAO,eAAa,eAAe,UAAU,KAAK,OAAO,YAAY,EAAE,QAAQ,MAAM,CAAC,CAAC,EACvF,OAAO,CAAC,KAAK,cAAc,MAAM,UAAU,KAAK,UAAU,MAAM,CAAC;AAIpE,QAAM,gBAAgB,WACnB,OAAO,eAAa;AACnB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,KAAK,SAAS,QAAQ,UAAU,KAAK,KAAK,SAAS,IAAI;AAAA,EAC1E,CAAC,EACA,OAAO,CAAC,KAAK,cAAc,MAAM,UAAU,KAAK,UAAU,MAAM,CAAC;AAIpE,QAAM,QAAQ,eAAe,IAAI,eAAe,gBAAgB;AAEhE,SAAO,SAAS;AAClB;;;ACjFO,SAAS,aACd,OACA,YACA,aAAkC,CAAC,GAC1B;AACT,QAAM,EAAE,MAAM,IAAI,MAAM,IAAI,MAAM;AAClC,QAAM,OAAO,aAAa,YAAY,YAAY,MAAM,MAAM,IAAI;AAElE,QAAM,aAA0B,CAAC;AAEjC,QAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AAC9C,QAAI,KAAK,QAAQ;AACf;AAAA,IACF;AAEA,UAAM,eAAe,KAAK,IAAI,MAAM,GAAG;AACvC,UAAM,aAAa,KAAK,IAAI,IAAI,MAAM,KAAK,QAAQ;AAEnD,eAAW,KAAK;AAAA,MACd;AAAA,MACA,MAAM;AAAA,MACN,IAAI;AAAA,IACN,CAAC;AAAA,EACH,CAAC;AAED,QAAM,iBAAiB,KAAK;AAC5B,QAAM,oBAAoB,WACvB,OAAO,eAAa;AACnB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,SAAS,UAAU,KAAK,KAAK;AAAA,EAC3C,CAAC,EACA,OAAO,eAAa,eAAe,UAAU,KAAK,OAAO,YAAY,EAAE,QAAQ,MAAM,CAAC,CAAC;AAE1F,MAAI,OAAO;AACT,WAAO,CAAC,CAAC,kBAAkB;AAAA,EAC7B;AAEA,QAAM,QAAQ,kBAAkB,OAAO,CAAC,KAAK,cAAc,MAAM,UAAU,KAAK,UAAU,MAAM,CAAC;AAEjG,SAAO,SAAS;AAClB;;;AC5CO,SAAS,SAAS,OAAoB,MAAqB,aAAkC,CAAC,GAAY;AAC/G,MAAI,CAAC,MAAM;AACT,WAAO,aAAa,OAAO,MAAM,UAAU,KAAK,aAAa,OAAO,MAAM,UAAU;AAAA,EACtF;AAEA,QAAM,aAAa,wBAAwB,MAAM,MAAM,MAAM;AAE7D,MAAI,eAAe,QAAQ;AACzB,WAAO,aAAa,OAAO,MAAM,UAAU;AAAA,EAC7C;AAEA,MAAI,eAAe,QAAQ;AACzB,WAAO,aAAa,OAAO,MAAM,UAAU;AAAA,EAC7C;AAEA,SAAO;AACT;;;AClBO,IAAM,gBAAgB,CAAC,OAAoB,aAAsB;AACtE,QAAM,EAAE,OAAO,KAAK,QAAQ,IAAI,MAAM;AAEtC,MAAI,UAAU;AACZ,UAAM,aAAa,eAAe,UAAQ,KAAK,KAAK,SAAS,QAAQ,EAAE,MAAM,SAAS;AAEtF,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AAEA,UAAM,aAAa,MAAM,IAAI,QAAQ,WAAW,MAAM,CAAC;AAEvD,QAAI,QAAQ,MAAM,MAAM,WAAW,IAAI,GAAG;AACxC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,IAAI,eAAe,IAAI,OAAO,WAAW,KAAK,MAAM,QAAQ,IAAI,KAAK;AACvE,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC1BO,IAAM,kBAAkB,CAAC,UAAuB;AACrD,QAAM,EAAE,OAAO,IAAI,IAAI,MAAM;AAE7B,MAAI,MAAM,eAAe,KAAK,MAAM,QAAQ,IAAI,KAAK;AACnD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACRO,SAAS,wBAAwB,WAAyB,SAA+B;AAC9F,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,WAAO,QAAQ,KAAK,sBAAoB;AACtC,YAAM,OAAO,OAAO,qBAAqB,WAAW,mBAAmB,iBAAiB;AAExF,aAAO,SAAS,UAAU;AAAA,IAC5B,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;ACNO,SAAS,OAAO,MAAc,YAAiC;AACpE,QAAM,EAAE,eAAe,IAAI,gBAAgB,UAAU;AACrD,QAAM,YAAY,eAAe,KAAK,UAAQ,KAAK,SAAS,IAAI;AAEhE,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AAEA,QAAM,UAAU;AAAA,IACd,MAAM,UAAU;AAAA,IAChB,SAAS,UAAU;AAAA,IACnB,SAAS,UAAU;AAAA,EACrB;AACA,QAAM,QAAQ,aAAa,kBAAuC,WAAW,SAAS,OAAO,CAAC;AAE9F,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,MAAM,GAAG,EAAE,SAAS,MAAM;AACzC;;;ACrBO,SAAS,YACd,MACA;AAAA,EACE,gBAAgB;AAAA,EAChB,mBAAmB;AACrB,IASI,CAAC,GACI;AApBX;AAqBE,MAAI,kBAAkB;AACpB,QAAI,KAAK,KAAK,SAAS,aAAa;AAElC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,QAAQ;AACf,aAAO,SAAS,MAAK,UAAK,SAAL,YAAa,EAAE;AAAA,IACtC;AAAA,EACF;AAEA,MAAI,KAAK,QAAQ;AACf,WAAO,CAAC,KAAK;AAAA,EACf;AAEA,MAAI,KAAK,UAAU,KAAK,QAAQ;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,eAAe;AACjB,QAAI,iBAAiB;AAErB,SAAK,QAAQ,QAAQ,eAAa;AAChC,UAAI,mBAAmB,OAAO;AAE5B;AAAA,MACF;AAEA,UAAI,CAAC,YAAY,WAAW,EAAE,kBAAkB,cAAc,CAAC,GAAG;AAChE,yBAAiB;AAAA,MACnB;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC7DA,SAAS,qBAAqB;AAEvB,SAAS,gBAAgB,OAAwC;AACtE,SAAO,iBAAiB;AAC1B;;;ACJA,SAAS,qBAAqB;AAEvB,SAAS,gBAAgB,OAAwC;AACtE,SAAO,iBAAiB;AAC1B;;;ACJO,SAAS,OAAO,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAW;AAC1D,SAAO,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,GAAG,GAAG;AAC3C;;;ACEO,SAAS,aAAa,MAAkB,MAAc,IAAqB;AAChF,QAAM,SAAS;AACf,QAAM,SAAS,KAAK,MAAM,IAAI,QAAQ;AACtC,QAAM,eAAe,OAAO,MAAM,QAAQ,MAAM;AAChD,QAAM,cAAc,OAAO,IAAI,QAAQ,MAAM;AAC7C,QAAM,QAAQ,KAAK,YAAY,YAAY;AAC3C,QAAM,MAAM,KAAK,YAAY,aAAa,EAAE;AAC5C,QAAM,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,GAAG;AACvC,QAAM,SAAS,KAAK,IAAI,MAAM,QAAQ,IAAI,MAAM;AAChD,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,IAAI,IAAI;AAC1C,QAAM,QAAQ,KAAK,IAAI,MAAM,OAAO,IAAI,KAAK;AAC7C,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,SAAS;AACxB,QAAM,IAAI;AACV,QAAM,IAAI;AACV,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ,MAAM;AAAA,EAChB;AACF;;;ACjCA,SAAS,WAAW,iBAAAC,sBAAqB;AAKlC,SAAS,qBAAqB,KAAsB,WAA0B,MAAwB;AAC3G,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAEA,QAAM,mBAAmB,UAAU,QAAQ,GAAG;AAC9C,QAAM,iBAAiB,UAAU,MAAM,GAAG;AAE1C,MAAI,aAAa,WAAW,aAAa,MAAM;AAC7C,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,OAAO;AACtB,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,iBAAiB;AAChC,QAAM,SAAS,eAAe;AAE9B,MAAI,aAAa,OAAO;AACtB,WAAOC,eAAc,OAAO,KAAK,OAAO,GAAG,QAAQ,MAAM,GAAG,OAAO,IAAI,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,EACtG;AAEA,SAAOA,eAAc,OAAO,KAAK,OAAO,UAAU,QAAQ,MAAM,GAAG,OAAO,UAAU,QAAQ,MAAM,CAAC;AACrG;;;ACJA,SAAS,2BAA2B;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB,CAAC;AACtB,GAeE;AACA,MAAI,KAAK,SAAS,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC3C,SAAK,QAAQ,KAAK,MAAM,OAAO,UAAQ;AACrC,YAAM,OAAO,OAAO,SAAS,WAAW,OAAO,KAAK;AAEpD,UAAI,WAAW,IAAI,IAAI,GAAG;AACxB,eAAO;AAAA,MACT;AAEA,uBAAiB,KAAK;AAAA,QACpB,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AAAA,QACzC,aAAa;AAAA,MACf,CAAC;AAED,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,MAAI,KAAK,WAAW,MAAM,QAAQ,KAAK,OAAO,GAAG;AAC/C,SAAK,UAAU,KAAK,QACjB;AAAA,MACC,WACE,2BAA2B;AAAA,QACzB,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC,EAAE;AAAA,IACP,EACC,OAAO,OAAK,MAAM,QAAQ,MAAM,MAAS;AAAA,EAC9C;AAEA,MAAI,KAAK,QAAQ,CAAC,WAAW,IAAI,KAAK,IAAI,GAAG;AAC3C,qBAAiB,KAAK;AAAA,MACpB,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AAAA,MACzC,aAAa,KAAK;AAAA,IACpB,CAAC;AAED,QAAI,KAAK,WAAW,MAAM,QAAQ,KAAK,OAAO,MAAK,mCAAS,yBAAwB,OAAO;AAEzF,WAAK,OAAO;AAEZ,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAGA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAEA,SAAO,EAAE,MAAM,iBAAiB;AAClC;AAMO,SAAS,sBAId,MAIA,QAIA,SAmBA;AACA,SAAO,2BAA2B;AAAA,IAChC;AAAA,IACA,YAAY,IAAI,IAAI,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,IAC7C,YAAY,IAAI,IAAI,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,IAC7C;AAAA,EACF,CAAC;AACH;;;ACnJA,SAAS,aAAAC,kBAAiB;AAC1B,SAAS,mBAAmB,mBAAmB;AAGxC,SAAS,wBAAwB,IAAiB,UAAkB,MAAc;AACvF,QAAM,OAAO,GAAG,MAAM,SAAS;AAE/B,MAAI,OAAO,UAAU;AACnB;AAAA,EACF;AAEA,QAAM,OAAO,GAAG,MAAM,IAAI;AAE1B,MAAI,EAAE,gBAAgB,eAAe,gBAAgB,oBAAoB;AACvE;AAAA,EACF;AAEA,QAAM,MAAM,GAAG,QAAQ,KAAK,IAAI;AAChC,MAAI,MAAM;AAEV,MAAI,QAAQ,CAAC,OAAO,KAAK,UAAU,UAAU;AAC3C,QAAI,QAAQ,GAAG;AACb,YAAM;AAAA,IACR;AAAA,EACF,CAAC;AAED,KAAG,aAAaA,WAAU,KAAK,GAAG,IAAI,QAAQ,GAAG,GAAG,IAAI,CAAC;AAC3D;;;AC3BA,SAAS,YAAAC,iBAAgB;AAEzB,SAAS,cAAc;AAoBhB,IAAM,YAAN,MAAgB;AAAA,EAYrB,YAAY,QAUT;AACD,SAAK,OAAO,OAAO;AACnB,SAAK,UAAU,OAAO;AAAA,EACxB;AACF;AAEA,IAAM,0BAA0B,CAAC,MAAc,SAA2D;AACxG,MAAI,SAAS,IAAI,GAAG;AAClB,WAAO,KAAK,KAAK,IAAI;AAAA,EACvB;AAEA,QAAM,iBAAiB,KAAK,IAAI;AAEhC,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AAEA,QAAM,SAAmC,CAAC,eAAe,IAAI;AAE7D,SAAO,QAAQ,eAAe;AAC9B,SAAO,QAAQ;AACf,SAAO,OAAO,eAAe;AAE7B,MAAI,eAAe,aAAa;AAC9B,QAAI,CAAC,eAAe,KAAK,SAAS,eAAe,WAAW,GAAG;AAC7D,cAAQ,KAAK,oFAAoF;AAAA,IACnG;AAEA,WAAO,KAAK,eAAe,WAAW;AAAA,EACxC;AAEA,SAAO;AACT;AAEA,SAAS,IAAI,QAOD;AAtFZ;AAuFE,QAAM,EAAE,QAAQ,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;AAClD,QAAM,EAAE,KAAK,IAAI;AAEjB,MAAI,KAAK,WAAW;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,KAAK,MAAM,IAAI,QAAQ,IAAI;AAEzC;AAAA;AAAA,IAEE,MAAM,OAAO,KAAK,KAAK;AAAA,IAEvB,CAAC,GAAE,WAAM,cAAc,MAAM,cAA1B,mBAAsC,MAAM,KAAK,UAAQ,KAAK,KAAK,KAAK;AAAA,IAC3E;AACA,WAAO;AAAA,EACT;AAEA,MAAI,UAAU;AAEd,QAAM,aAAa,wBAAwB,KAAK,IAAI;AAEpD,QAAM,QAAQ,UAAQ;AACpB,QAAI,SAAS;AACX;AAAA,IACF;AAEA,UAAM,QAAQ,wBAAwB,YAAY,KAAK,IAAI;AAE3D,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAEA,UAAM,KAAK,KAAK,MAAM;AACtB,UAAM,QAAQ,qBAAqB;AAAA,MACjC,OAAO,KAAK;AAAA,MACZ,aAAa;AAAA,IACf,CAAC;AACD,UAAM,QAAQ;AAAA,MACZ,MAAM,QAAQ,MAAM,CAAC,EAAE,SAAS,KAAK;AAAA,MACrC;AAAA,IACF;AAEA,UAAM,EAAE,UAAU,OAAO,IAAI,IAAI,IAAI,eAAe;AAAA,MAClD;AAAA,MACA;AAAA,IACF,CAAC;AAED,UAAM,UAAU,KAAK,QAAQ;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAGD,QAAI,YAAY,QAAQ,CAAC,GAAG,MAAM,QAAQ;AACxC;AAAA,IACF;AAIA,OAAG,QAAQ,QAAQ;AAAA,MACjB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,SAAK,SAAS,EAAE;AAChB,cAAU;AAAA,EACZ,CAAC;AAED,SAAO;AACT;AAOO,SAAS,iBAAiB,OAAuD;AACtF,QAAM,EAAE,QAAQ,MAAM,IAAI;AAC1B,QAAM,SAAS,IAAI,OAAO;AAAA,IACxB,OAAO;AAAA,MACL,OAAO;AACL,eAAO;AAAA,MACT;AAAA,MACA,MAAM,IAAI,MAAM,OAAO;AACrB,cAAM,SAAS,GAAG,QAAQ,MAAM;AAEhC,YAAI,QAAQ;AACV,iBAAO;AAAA,QACT;AAGA,cAAM,qBAAqB,GAAG,QAAQ,iBAAiB;AAMvD,cAAM,mBAAmB,CAAC,CAAC;AAE3B,YAAI,kBAAkB;AACpB,qBAAW,MAAM;AACf,gBAAI,EAAE,KAAK,IAAI;AAEf,gBAAI,OAAO,SAAS,UAAU;AAC5B,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO,oBAAoBC,UAAS,KAAK,IAAI,GAAG,MAAM,MAAM;AAAA,YAC9D;AAEA,kBAAM,EAAE,KAAK,IAAI;AACjB,kBAAM,KAAK,OAAO,KAAK;AAEvB,gBAAI;AAAA,cACF;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAEA,eAAO,GAAG,gBAAgB,GAAG,aAAa,OAAO;AAAA,MACnD;AAAA,IACF;AAAA,IAEA,OAAO;AAAA,MACL,gBAAgB,MAAM,MAAM,IAAI,MAAM;AACpC,eAAO,IAAI;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,iBAAiB;AAAA,QACf,gBAAgB,UAAQ;AACtB,qBAAW,MAAM;AACf,kBAAM,EAAE,QAAQ,IAAI,KAAK,MAAM;AAE/B,gBAAI,SAAS;AACX,kBAAI;AAAA,gBACF;AAAA,gBACA,MAAM,QAAQ;AAAA,gBACd,IAAI,QAAQ;AAAA,gBACZ,MAAM;AAAA,gBACN;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAED,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,cAAc,MAAM,OAAO;AACzB,YAAI,MAAM,QAAQ,SAAS;AACzB,iBAAO;AAAA,QACT;AAEA,cAAM,EAAE,QAAQ,IAAI,KAAK,MAAM;AAE/B,YAAI,SAAS;AACX,iBAAO,IAAI;AAAA,YACT;AAAA,YACA,MAAM,QAAQ;AAAA,YACd,IAAI,QAAQ;AAAA,YACZ,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA,IAGA,cAAc;AAAA,EAChB,CAAC;AAED,SAAO;AACT;;;ACzRA,SAAS,QAAQ,OAAoB;AACnC,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AAC1D;AAEO,SAAS,cAAc,OAA0C;AACtE,MAAI,QAAQ,KAAK,MAAM,UAAU;AAC/B,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,gBAAgB,UAAU,OAAO,eAAe,KAAK,MAAM,OAAO;AACjF;;;ACVO,SAAS,UAAU,QAA6B,QAAkD;AACvG,QAAM,SAAS,EAAE,GAAG,OAAO;AAE3B,MAAI,cAAc,MAAM,KAAK,cAAc,MAAM,GAAG;AAClD,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,UAAI,cAAc,OAAO,GAAG,CAAC,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AAC5D,eAAO,GAAG,IAAI,UAAU,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,MAClD,OAAO;AACL,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;ACuYO,IAAM,aAAN,MAIL;AAAA,EAYA,YAAY,SAA0B,CAAC,GAAG;AAX1C,gBAAO;AACP,kBAA4B;AAE5B,iBAA2B;AAE3B,gBAAO;AAEP,kBAAiB;AAAA,MACf,MAAM,KAAK;AAAA,IACb;AAGE,SAAK,SAAS;AAAA,MACZ,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACL;AAEA,SAAK,OAAQ,KAAK,OAAe;AAAA,EACnC;AAAA,EAEA,IAAI,UAAmB;AACrB,WAAO;AAAA,MACL,GAAI;AAAA,QACF,kBAA2C,MAAa,cAAc;AAAA,UACpE,MAAM,KAAK;AAAA,QACb,CAAC;AAAA,MACH,KAAK,CAAC;AAAA,IACR;AAAA,EACF;AAAA,EAEA,IAAI,UAA6B;AAC/B,WAAO;AAAA,MACL,GAAI;AAAA,QACF,kBAA2C,MAAa,cAAc;AAAA,UACpE,MAAM,KAAK;AAAA,UACX,SAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH,KAAK,CAAC;AAAA,IACR;AAAA,EACF;AAAA,EAEA,UAAU,UAA4B,CAAC,GAAG;AACxC,UAAM,YAAY,KAAK,OAAiC;AAAA,MACtD,GAAG,KAAK;AAAA,MACR,YAAY,MAAM;AAChB,eAAO,UAAU,KAAK,SAAgC,OAAO;AAAA,MAC/D;AAAA,IACF,CAAC;AAED,cAAU,OAAO,KAAK;AACtB,cAAU,SAAS,KAAK;AAExB,WAAO;AAAA,EACT;AAAA,EAEA,OAOE,iBAA0C,CAAC,GAAiD;AAC5F,UAAM,YAAY,IAAK,KAAK,YAAoB,EAAE,GAAG,KAAK,QAAQ,GAAG,eAAe,CAAC;AAErF,cAAU,SAAS;AACnB,SAAK,QAAQ;AACb,cAAU,OAAO,UAAU,iBAAiB,eAAe,OAAO,UAAU,OAAO;AAEnF,WAAO;AAAA,EACT;AACF;;;AClVO,IAAM,OAAN,MAAM,cAA2C,WAA2D;AAAA,EAA5G;AAAA;AACL,gBAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMP,OAAO,OAAyB,SAAwE,CAAC,GAAG;AAE1G,UAAM,iBAAiB,OAAO,WAAW,aAAa,OAAO,IAAI;AACjE,WAAO,IAAI,MAAW,cAAc;AAAA,EACtC;AAAA,EAEA,OAAO,WAAW,EAAE,QAAQ,KAAK,GAAmC;AAClE,UAAM,EAAE,GAAG,IAAI,OAAO;AACtB,UAAM,aAAa,OAAO,MAAM,UAAU;AAC1C,UAAM,UAAU,WAAW,QAAQ,WAAW,IAAI;AAElD,QAAI,SAAS;AACX,YAAM,eAAe,WAAW,MAAM;AACtC,YAAM,WAAW,CAAC,CAAC,aAAa,KAAK,QAAK,uBAAG,KAAK,UAAS,KAAK,IAAI;AAEpE,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AAEA,YAAM,aAAa,aAAa,KAAK,QAAK,uBAAG,KAAK,UAAS,KAAK,IAAI;AAEpE,UAAI,YAAY;AACd,WAAG,iBAAiB,UAAU;AAAA,MAChC;AACA,SAAG,WAAW,KAAK,WAAW,GAAG;AAEjC,aAAO,KAAK,SAAS,EAAE;AAEvB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,UAAU,SAA4B;AACpC,WAAO,MAAM,UAAU,OAAO;AAAA,EAChC;AAAA,EAEA,OAKE,gBAUwC;AAExC,UAAM,iBAAiB,OAAO,mBAAmB,aAAa,eAAe,IAAI;AACjF,WAAO,MAAM,OAAO,cAAc;AAAA,EACpC;AACF;;;ACjNA,SAAS,YAAAC,iBAAgB;AAEzB,SAAS,UAAAC,eAAc;;;ACHhB,SAAS,SAAS,OAA6B;AACpD,SAAO,OAAO,UAAU;AAC1B;;;AD2BO,IAAM,YAAN,MAAgB;AAAA,EAcrB,YAAY,QAYT;AACD,SAAK,OAAO,OAAO;AACnB,SAAK,UAAU,OAAO;AAAA,EACxB;AACF;AAEA,IAAM,0BAA0B,CAC9B,MACA,MACA,UAC+B;AAC/B,MAAI,SAAS,IAAI,GAAG;AAClB,WAAO,CAAC,GAAG,KAAK,SAAS,IAAI,CAAC;AAAA,EAChC;AAEA,QAAM,UAAU,KAAK,MAAM,KAAK;AAEhC,MAAI,CAAC,SAAS;AACZ,WAAO,CAAC;AAAA,EACV;AAEA,SAAO,QAAQ,IAAI,oBAAkB;AACnC,UAAM,SAAmC,CAAC,eAAe,IAAI;AAE7D,WAAO,QAAQ,eAAe;AAC9B,WAAO,QAAQ;AACf,WAAO,OAAO,eAAe;AAE7B,QAAI,eAAe,aAAa;AAC9B,UAAI,CAAC,eAAe,KAAK,SAAS,eAAe,WAAW,GAAG;AAC7D,gBAAQ,KAAK,oFAAoF;AAAA,MACnG;AAEA,aAAO,KAAK,eAAe,WAAW;AAAA,IACxC;AAEA,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAASC,KAAI,QAQD;AACV,QAAM,EAAE,QAAQ,OAAO,MAAM,IAAI,MAAM,YAAY,UAAU,IAAI;AAEjE,QAAM,EAAE,UAAU,OAAO,IAAI,IAAI,IAAI,eAAe;AAAA,IAClD;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,WAA4B,CAAC;AAEnC,QAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AAjHlD;AAqHI,UAAI,gBAAK,SAAL,mBAAW,SAAX,mBAAiB,SAAQ,EAAE,KAAK,UAAU,KAAK,eAAe,KAAK,WAAW;AAChF;AAAA,IACF;AAKA,UAAM,eAAc,sBAAK,YAAL,mBAAc,SAAd,YAAsB,KAAK,aAA3B,YAAuC;AAC3D,UAAM,eAAe,KAAK,IAAI,MAAM,GAAG;AACvC,UAAM,aAAa,KAAK,IAAI,IAAI,MAAM,WAAW;AAKjD,QAAI,gBAAgB,YAAY;AAC9B;AAAA,IACF;AAEA,UAAM,cAAc,KAAK,SACrB,KAAK,QAAQ,KACb,KAAK,YAAY,eAAe,KAAK,aAAa,KAAK,QAAW,QAAQ;AAE9E,UAAM,UAAU,wBAAwB,aAAa,KAAK,MAAM,UAAU;AAE1E,YAAQ,QAAQ,WAAS;AACvB,UAAI,MAAM,UAAU,QAAW;AAC7B;AAAA,MACF;AAEA,YAAM,QAAQ,eAAe,MAAM,QAAQ;AAC3C,YAAM,MAAM,QAAQ,MAAM,CAAC,EAAE;AAC7B,YAAM,QAAQ;AAAA,QACZ,MAAM,MAAM,GAAG,QAAQ,IAAI,KAAK;AAAA,QAChC,IAAI,MAAM,GAAG,QAAQ,IAAI,GAAG;AAAA,MAC9B;AAEA,YAAM,UAAU,KAAK,QAAQ;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,eAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AAED,QAAM,UAAU,SAAS,MAAM,aAAW,YAAY,IAAI;AAE1D,SAAO;AACT;AAGA,IAAI,4BAA2C;AAE/C,IAAM,4BAA4B,CAAC,SAAiB;AAhLpD;AAiLE,QAAM,QAAQ,IAAI,eAAe,SAAS;AAAA,IACxC,eAAe,IAAI,aAAa;AAAA,EAClC,CAAC;AAED,cAAM,kBAAN,mBAAqB,QAAQ,aAAa;AAE1C,SAAO;AACT;AAOO,SAAS,iBAAiB,OAAyD;AACxF,QAAM,EAAE,QAAQ,MAAM,IAAI;AAC1B,MAAI,oBAAoC;AACxC,MAAI,0BAA0B;AAC9B,MAAI,2BAA2B;AAC/B,MAAI,aAAa,OAAO,mBAAmB,cAAc,IAAI,eAAe,OAAO,IAAI;AACvF,MAAI;AAEJ,MAAI;AACF,gBAAY,OAAO,cAAc,cAAc,IAAI,UAAU,MAAM,IAAI;AAAA,EACzE,QAAQ;AACN,gBAAY;AAAA,EACd;AAEA,QAAM,eAAe,CAAC;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAMM;AACJ,UAAM,KAAK,MAAM;AACjB,UAAM,iBAAiB,qBAAqB;AAAA,MAC1C;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAED,UAAM,UAAUA,KAAI;AAAA,MAClB;AAAA,MACA,OAAO;AAAA,MACP,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC;AAAA,MAC1B,IAAI,GAAG,IAAI;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,IACF,CAAC;AAED,QAAI,CAAC,WAAW,CAAC,GAAG,MAAM,QAAQ;AAChC;AAAA,IACF;AAEA,QAAI;AACF,kBAAY,OAAO,cAAc,cAAc,IAAI,UAAU,MAAM,IAAI;AAAA,IACzE,QAAQ;AACN,kBAAY;AAAA,IACd;AACA,iBAAa,OAAO,mBAAmB,cAAc,IAAI,eAAe,OAAO,IAAI;AAEnF,WAAO;AAAA,EACT;AAEA,QAAM,UAAU,MAAM,IAAI,UAAQ;AAChC,WAAO,IAAIC,QAAO;AAAA;AAAA,MAEhB,KAAK,MAAM;AACT,cAAM,kBAAkB,CAAC,UAAqB;AA5PtD;AA6PU,gCAAoB,UAAK,IAAI,kBAAT,mBAAwB,SAAS,MAAM,WAAqB,KAAK,IAAI,gBAAgB;AAEzG,cAAI,mBAAmB;AACrB,wCAA4B;AAAA,UAC9B;AAAA,QACF;AAEA,cAAM,gBAAgB,MAAM;AAC1B,cAAI,2BAA2B;AAC7B,wCAA4B;AAAA,UAC9B;AAAA,QACF;AAEA,eAAO,iBAAiB,aAAa,eAAe;AACpD,eAAO,iBAAiB,WAAW,aAAa;AAEhD,eAAO;AAAA,UACL,UAAU;AACR,mBAAO,oBAAoB,aAAa,eAAe;AACvD,mBAAO,oBAAoB,WAAW,aAAa;AAAA,UACrD;AAAA,QACF;AAAA,MACF;AAAA,MAEA,OAAO;AAAA,QACL,iBAAiB;AAAA,UACf,MAAM,CAAC,MAAM,UAAiB;AAC5B,uCAA2B,sBAAsB,KAAK,IAAI;AAC1D,wBAAY;AAEZ,gBAAI,CAAC,0BAA0B;AAC7B,oBAAM,sBAAsB;AAE5B,kBAAI,2DAAqB,YAAY;AAEnC,2BAAW,MAAM;AACf,wBAAM,YAAY,oBAAoB,MAAM;AAE5C,sBAAI,WAAW;AACb,wCAAoB,SAAS,YAAY,EAAE,MAAM,UAAU,MAAM,IAAI,UAAU,GAAG,CAAC;AAAA,kBACrF;AAAA,gBACF,GAAG,EAAE;AAAA,cACP;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,CAAC,OAAO,UAAiB;AA5S1C;AA6SY,kBAAM,QAAQ,WAAyB,kBAAzB,mBAAwC,QAAQ;AAE9D,yBAAa;AAEb,sCAA0B,CAAC,EAAC,6BAAM,SAAS;AAE3C,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,MAEA,mBAAmB,CAAC,cAAc,UAAU,UAAU;AACpD,cAAM,cAAc,aAAa,CAAC;AAClC,cAAM,UAAU,YAAY,QAAQ,SAAS,MAAM,WAAW,CAAC;AAC/D,cAAM,SAAS,YAAY,QAAQ,SAAS,MAAM,UAAU,CAAC;AAG7D,cAAM,qBAAqB,YAAY,QAAQ,iBAAiB;AAGhE,cAAM,mBAAmB,CAAC,CAAC;AAE3B,YAAI,CAAC,WAAW,CAAC,UAAU,CAAC,kBAAkB;AAC5C;AAAA,QACF;AAGA,YAAI,kBAAkB;AACpB,cAAI,EAAE,KAAK,IAAI;AAEf,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,oBAAoBC,UAAS,KAAK,IAAI,GAAG,MAAM,MAAM;AAAA,UAC9D;AAEA,gBAAM,EAAE,MAAAC,MAAK,IAAI;AACjB,gBAAMC,MAAKD,QAAO,KAAK;AAEvB,gBAAM,WAAW,0BAA0B,IAAI;AAE/C,iBAAO,aAAa;AAAA,YAClB;AAAA,YACA;AAAA,YACA,MAAAA;AAAA,YACA,IAAI,EAAE,GAAGC,IAAG;AAAA,YACZ;AAAA,UACF,CAAC;AAAA,QACH;AAGA,cAAM,OAAO,SAAS,IAAI,QAAQ,cAAc,MAAM,IAAI,OAAO;AACjE,cAAM,KAAK,SAAS,IAAI,QAAQ,YAAY,MAAM,IAAI,OAAO;AAG7D,YAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,SAAS,GAAG,GAAG;AAC3C;AAAA,QACF;AAEA,eAAO,aAAa;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;;;AtE1VO,IAAM,mBAAN,MAAuB;AAAA,EAS5B,YAAY,YAAwB,QAAgB;AAFpD,2BAA4B,CAAC;AAG3B,SAAK,SAAS;AACd,SAAK,aAAa,kBAAkB,UAAU;AAC9C,SAAK,SAAS,8BAA8B,KAAK,YAAY,MAAM;AACnE,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,IAAI,WAAwB;AAC1B,WAAO,KAAK,WAAW,OAAO,CAAC,UAAU,cAAc;AACrD,YAAM,UAAU;AAAA,QACd,MAAM,UAAU;AAAA,QAChB,SAAS,UAAU;AAAA,QACnB,SAAS,KAAK,OAAO,iBAAiB,UAAU,IAAqB;AAAA,QACrE,QAAQ,KAAK;AAAA,QACb,MAAM,oBAAoB,UAAU,MAAM,KAAK,MAAM;AAAA,MACvD;AAEA,YAAM,cAAc,kBAA4C,WAAW,eAAe,OAAO;AAEjG,UAAI,CAAC,aAAa;AAChB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG,YAAY;AAAA,MACjB;AAAA,IACF,GAAG,CAAC,CAAgB;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAoB;AACtB,UAAM,EAAE,OAAO,IAAI;AAOnB,UAAM,aAAa,eAAe,CAAC,GAAG,KAAK,UAAU,EAAE,QAAQ,CAAC;AAEhE,UAAM,aAAa,WAChB,IAAI,eAAa;AAChB,YAAM,UAAU;AAAA,QACd,MAAM,UAAU;AAAA,QAChB,SAAS,UAAU;AAAA,QACnB,SAAS,KAAK,OAAO,iBAAiB,UAAU,IAAqB;AAAA,QACrE;AAAA,QACA,MAAM,oBAAoB,UAAU,MAAM,KAAK,MAAM;AAAA,MACvD;AAEA,YAAM,UAAoB,CAAC;AAE3B,YAAM,uBAAuB;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,kBAAiD,CAAC;AAGtD,UAAI,UAAU,SAAS,UAAU,kBAA0C,WAAW,YAAY,OAAO,GAAG;AAC1G,wBAAgB,aAAa,MAAM,KAAK,WAAW,EAAE,QAAQ,MAAM,UAAkB,CAAC;AAAA,MACxF;AAEA,UAAI,sBAAsB;AACxB,cAAM,WAAW,OAAO;AAAA,UACtB,OAAO,QAAQ,qBAAqB,CAAC,EAAE,IAAI,CAAC,CAAC,UAAU,MAAM,MAAM;AACjE,mBAAO,CAAC,UAAU,MAAM,OAAO,EAAE,OAAO,CAAC,CAAC;AAAA,UAC5C,CAAC;AAAA,QACH;AAEA,0BAAkB,EAAE,GAAG,iBAAiB,GAAG,SAAS;AAAA,MACtD;AAEA,YAAM,eAAe,OAAO,eAAe;AAE3C,cAAQ,KAAK,YAAY;AAEzB,YAAM,gBAAgB,kBAA8C,WAAW,iBAAiB,OAAO;AAEvG,UAAI,wBAAwB,WAAW,OAAO,QAAQ,gBAAgB,KAAK,eAAe;AACxF,cAAM,QAAQ,cAAc;AAE5B,YAAI,SAAS,MAAM,QAAQ;AACzB,gBAAM,cAAc,iBAAiB;AAAA,YACnC;AAAA,YACA;AAAA,UACF,CAAC;AAED,gBAAM,eAAe,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW;AAE5E,kBAAQ,KAAK,GAAG,YAAY;AAAA,QAC9B;AAAA,MACF;AAEA,YAAM,gBAAgB,kBAA8C,WAAW,iBAAiB,OAAO;AAEvG,UAAI,wBAAwB,WAAW,OAAO,QAAQ,gBAAgB,KAAK,eAAe;AACxF,cAAM,QAAQ,cAAc;AAE5B,YAAI,SAAS,MAAM,QAAQ;AACzB,gBAAM,aAAa,iBAAiB,EAAE,QAAQ,MAAM,CAAC;AAErD,kBAAQ,KAAK,GAAG,UAAU;AAAA,QAC5B;AAAA,MACF;AAEA,YAAM,wBAAwB;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,uBAAuB;AACzB,cAAM,qBAAqB,sBAAsB;AAEjD,gBAAQ,KAAK,GAAG,kBAAkB;AAAA,MACpC;AAEA,aAAO;AAAA,IACT,CAAC,EACA,KAAK;AAER,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AACf,WAAO,4BAA4B,KAAK,UAAU;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAiD;AACnD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,EAAE,eAAe,IAAI,gBAAgB,KAAK,UAAU;AAE1D,WAAO,OAAO;AAAA,MACZ,eACG,OAAO,eAAa,CAAC,CAAC,kBAAkB,WAAW,aAAa,CAAC,EACjE,IAAI,eAAa;AAChB,cAAM,sBAAsB,KAAK,WAAW,OAAO,eAAa,UAAU,SAAS,UAAU,IAAI;AACjG,cAAM,UAAU;AAAA,UACd,MAAM,UAAU;AAAA,UAChB,SAAS,UAAU;AAAA,UACnB,SAAS,KAAK,OAAO,iBAAiB,UAAU,IAAqB;AAAA,UACrE;AAAA,UACA,MAAM,YAAY,UAAU,MAAM,KAAK,MAAM;AAAA,QAC/C;AACA,cAAM,cAAc,kBAA6C,WAAW,eAAe,OAAO;AAElG,YAAI,CAAC,aAAa;AAChB,iBAAO,CAAC;AAAA,QACV;AAEA,cAAM,WAAgC,CAAC,MAAM,MAAM,QAAQ,aAAa,qBAAqB;AAC3F,gBAAM,iBAAiB,sBAAsB,MAAM,mBAAmB;AAEtE,iBAAO,YAAY,EAAE;AAAA;AAAA,YAEnB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAEA,eAAO,CAAC,UAAU,MAAM,QAAQ;AAAA,MAClC,CAAC;AAAA,IACL;AAAA,EACF;AAAA,EAEA,IAAI,YAAiD;AACnD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,EAAE,eAAe,IAAI,gBAAgB,KAAK,UAAU;AAE1D,WAAO,OAAO;AAAA,MACZ,eACG,OAAO,eAAa,CAAC,CAAC,kBAAkB,WAAW,aAAa,CAAC,EACjE,IAAI,eAAa;AAChB,cAAM,sBAAsB,KAAK,WAAW,OAAO,eAAa,UAAU,SAAS,UAAU,IAAI;AACjG,cAAM,UAAU;AAAA,UACd,MAAM,UAAU;AAAA,UAChB,SAAS,UAAU;AAAA,UACnB,SAAS,KAAK,OAAO,iBAAiB,UAAU,IAAqB;AAAA,UACrE;AAAA,UACA,MAAM,YAAY,UAAU,MAAM,KAAK,MAAM;AAAA,QAC/C;AACA,cAAM,cAAc,kBAA6C,WAAW,eAAe,OAAO;AAElG,YAAI,CAAC,aAAa;AAChB,iBAAO,CAAC;AAAA,QACV;AAEA,cAAM,WAAgC,CAAC,MAAM,MAAM,WAAW;AAC5D,gBAAM,iBAAiB,sBAAsB,MAAM,mBAAmB;AAEtE,iBAAO,YAAY,EAAE;AAAA;AAAA,YAEnB;AAAA,YACA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA,YACA;AAAA,YACA;AAAA,YACA,kBAAkB,CAAC,UAA+B;AAChD,uCAAyB,MAAM,QAAQ,KAAK;AAAA,YAC9C;AAAA,UACF,CAAC;AAAA,QACH;AAEA,eAAO,CAAC,UAAU,MAAM,QAAQ;AAAA,MAClC,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,kBAAkB;AACxB,UAAM,aAAa,KAAK;AAExB,SAAK,OAAO,mBAAmB,OAAO;AAAA,MACpC,WAAW,IAAI,eAAa,CAAC,UAAU,MAAM,UAAU,OAAO,CAAC;AAAA,IACjE;AAEA,eAAW,QAAQ,eAAa;AAhSpC;AAiSM,YAAM,UAAU;AAAA,QACd,MAAM,UAAU;AAAA,QAChB,SAAS,UAAU;AAAA,QACnB,SAAS,KAAK,OAAO,iBAAiB,UAAU,IAAqB;AAAA,QACrE,QAAQ,KAAK;AAAA,QACb,MAAM,oBAAoB,UAAU,MAAM,KAAK,MAAM;AAAA,MACvD;AAEA,UAAI,UAAU,SAAS,QAAQ;AAC7B,cAAM,eAAc,kBAAa,kBAAkB,WAAW,eAAe,OAAO,CAAC,MAAjE,YAAsE;AAE1F,YAAI,aAAa;AACf,eAAK,gBAAgB,KAAK,UAAU,IAAI;AAAA,QAC1C;AAAA,MACF;AAEA,YAAM,iBAAiB,kBAA+C,WAAW,kBAAkB,OAAO;AAC1G,YAAM,WAAW,kBAAyC,WAAW,YAAY,OAAO;AACxF,YAAM,WAAW,kBAAyC,WAAW,YAAY,OAAO;AACxF,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,gBAAgB,kBAA8C,WAAW,iBAAiB,OAAO;AACvG,YAAM,UAAU,kBAAwC,WAAW,WAAW,OAAO;AACrF,YAAM,SAAS,kBAAuC,WAAW,UAAU,OAAO;AAClF,YAAM,YAAY,kBAA0C,WAAW,aAAa,OAAO;AAE3F,UAAI,gBAAgB;AAClB,aAAK,OAAO,GAAG,gBAAgB,cAAc;AAAA,MAC/C;AAEA,UAAI,UAAU;AACZ,aAAK,OAAO,GAAG,UAAU,QAAQ;AAAA,MACnC;AAEA,UAAI,UAAU;AACZ,aAAK,OAAO,GAAG,UAAU,QAAQ;AAAA,MACnC;AAEA,UAAI,mBAAmB;AACrB,aAAK,OAAO,GAAG,mBAAmB,iBAAiB;AAAA,MACrD;AAEA,UAAI,eAAe;AACjB,aAAK,OAAO,GAAG,eAAe,aAAa;AAAA,MAC7C;AAEA,UAAI,SAAS;AACX,aAAK,OAAO,GAAG,SAAS,OAAO;AAAA,MACjC;AAEA,UAAI,QAAQ;AACV,aAAK,OAAO,GAAG,QAAQ,MAAM;AAAA,MAC/B;AAEA,UAAI,WAAW;AACb,aAAK,OAAO,GAAG,WAAW,SAAS;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AArUa,iBAgBJ,UAAU;AAhBN,iBAkBJ,OAAO;AAlBH,iBAoBJ,UAAU;;;AwE9CnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,SAAS,UAAAC,SAAQ,iBAAiB;;;ACW3B,IAAM,YAAN,MAAM,mBAAgD,WAI3D;AAAA,EAJK;AAAA;AAKL,gBAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMP,OAAO,OACL,SAAkF,CAAC,GACnF;AAEA,UAAM,iBAAiB,OAAO,WAAW,aAAa,OAAO,IAAI;AACjE,WAAO,IAAI,WAAgB,cAAc;AAAA,EAC3C;AAAA,EAEA,UAAU,SAA4B;AACpC,WAAO,MAAM,UAAU,OAAO;AAAA,EAChC;AAAA,EAEA,OAKE,gBAU6C;AAE7C,UAAM,iBAAiB,OAAO,mBAAmB,aAAa,eAAe,IAAI;AACjF,WAAO,MAAM,OAAO,cAAc;AAAA,EACpC;AACF;;;AD5CO,IAAM,0BAA0B,UAAU,OAAuC;AAAA,EACtF,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,WAAO;AAAA,MACL,IAAIC,QAAO;AAAA,QACT,KAAK,IAAI,UAAU,yBAAyB;AAAA,QAC5C,OAAO;AAAA,UACL,yBAAyB,MAAM;AAC7B,kBAAM,EAAE,OAAO,IAAI;AACnB,kBAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,kBAAM,EAAE,KAAK,UAAU,IAAI;AAC3B,kBAAM,EAAE,OAAO,IAAI;AACnB,kBAAM,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,CAAAC,WAASA,OAAM,MAAM,GAAG,CAAC;AAC7D,kBAAM,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,CAAAA,WAASA,OAAM,IAAI,GAAG,CAAC;AACzD,kBAAM,kBAAkB,6BAA6B,MAAM;AAC3D,kBAAM,QAAQ,EAAE,MAAM,GAAG;AAEzB,mBAAO,eAAe,KAAK,OAAO;AAAA,cAChC,GAAI,KAAK,QAAQ,mBAAmB,SAAY,EAAE,gBAAgB,KAAK,QAAQ,eAAe,IAAI,CAAC;AAAA,cACnG;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AE3CD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACcO,IAAM,OACX,MACA,CAAC,EAAE,QAAQ,KAAK,MAAM;AACpB,wBAAsB,MAAM;AAjBhC;AAkBM,QAAI,CAAC,OAAO,aAAa;AACvB;AAAC,MAAC,KAAK,IAAoB,KAAK;AAIhC,6CAAQ,mBAAR,mBAAwB;AAAA,IAC1B;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;ACRK,IAAM,eACX,CAAC,aAAa,SACd,CAAC,EAAE,SAAS,MAAM;AAChB,SAAO,SAAS,WAAW,IAAI,EAAE,WAAW,CAAC;AAC/C;;;ACxBF,SAAS,kBAAkB;AAgBpB,IAAM,aACX,MACA,CAAC,EAAE,OAAO,IAAI,SAAS,MAAM;AAC3B,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,OAAO,IAAI;AAEnB,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,CAAC,EAAE,OAAO,IAAI,MAAM;AACjC,UAAM,IAAI,aAAa,MAAM,KAAK,IAAI,KAAK,CAAC,MAAM,QAAQ;AACxD,UAAI,KAAK,KAAK,QAAQ;AACpB;AAAA,MACF;AAEA,YAAM,EAAE,KAAK,QAAQ,IAAI;AACzB,YAAM,cAAc,IAAI,QAAQ,QAAQ,IAAI,GAAG,CAAC;AAChD,YAAM,YAAY,IAAI,QAAQ,QAAQ,IAAI,MAAM,KAAK,QAAQ,CAAC;AAC9D,YAAM,YAAY,YAAY,WAAW,SAAS;AAElD,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AAEA,YAAM,kBAAkB,WAAW,SAAS;AAE5C,UAAI,KAAK,KAAK,aAAa;AACzB,cAAM,EAAE,YAAY,IAAI,YAAY,OAAO,eAAe,YAAY,MAAM,CAAC;AAE7E,WAAG,cAAc,UAAU,OAAO,WAAW;AAAA,MAC/C;AAEA,UAAI,mBAAmB,oBAAoB,GAAG;AAC5C,WAAG,KAAK,WAAW,eAAe;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;;;ACrCK,IAAM,UAAkC,QAAM,WAAS;AAC5D,SAAO,GAAG,KAAK;AACjB;;;ACrBA,SAAS,uBAAuB,mCAAmC;AAgB5D,IAAM,sBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,4BAA4B,OAAO,QAAQ;AACpD;;;ACpBF,SAAS,iBAAAC,sBAAqB;AAoBvB,IAAM,MACX,CAAC,aAAa,cACd,CAAC,EAAE,QAAQ,GAAG,MAAM;AAClB,QAAM,EAAE,MAAM,IAAI;AAElB,QAAM,eAAe,MAAM,IAAI,MAAM,YAAY,MAAM,YAAY,EAAE;AAErE,KAAG,YAAY,YAAY,MAAM,YAAY,EAAE;AAC/C,QAAM,SAAS,GAAG,QAAQ,IAAI,SAAS;AAEvC,KAAG,OAAO,QAAQ,aAAa,OAAO;AAEtC,KAAG,aAAa,IAAIA,eAAc,GAAG,IAAI,QAAQ,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AAE1E,SAAO;AACT;;;ACrBK,IAAM,oBACX,MACA,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,cAAc,UAAU,QAAQ,KAAK;AAG3C,MAAI,YAAY,QAAQ,OAAO,GAAG;AAChC,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,GAAG,UAAU;AAE1B,WAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAClD,UAAM,OAAO,KAAK,KAAK,KAAK;AAE5B,QAAI,KAAK,SAAS,YAAY,MAAM;AAClC,UAAI,UAAU;AACZ,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,cAAM,KAAK,KAAK,MAAM,KAAK;AAE3B,WAAG,OAAO,MAAM,EAAE,EAAE,eAAe;AAAA,MACrC;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACzBK,IAAM,aACX,gBACA,CAAC,EAAE,IAAI,OAAO,SAAS,MAAM;AAC3B,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,OAAO,GAAG,UAAU;AAE1B,WAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAClD,UAAM,OAAO,KAAK,KAAK,KAAK;AAE5B,QAAI,KAAK,SAAS,MAAM;AACtB,UAAI,UAAU;AACZ,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,cAAM,KAAK,KAAK,MAAM,KAAK;AAE3B,WAAG,OAAO,MAAM,EAAE,EAAE,eAAe;AAAA,MACrC;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACzBK,IAAM,cACX,WACA,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,QAAM,EAAE,MAAM,GAAG,IAAI;AAErB,MAAI,UAAU;AACZ,OAAG,OAAO,MAAM,EAAE;AAAA,EACpB;AAEA,SAAO;AACT;;;ACzBF,SAAS,mBAAmB,+BAA+B;AAgBpD,IAAM,kBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,wBAAwB,OAAO,QAAQ;AAChD;;;ACNK,IAAM,QACX,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,SAAO,SAAS,iBAAiB,OAAO;AAC1C;;;AClBF,SAAS,YAAY,wBAAwB;AAgBtC,IAAM,WACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,iBAAiB,OAAO,QAAQ;AACzC;;;ACnBF,SAAS,iBAAAC,sBAAqB;AA+BvB,IAAM,kBACX,CAAC,YAAY,aAAa,CAAC,MAC3B,CAAC,EAAE,IAAI,OAAO,SAAS,MAAM;AAC3B,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,KAAK,UAAU,IAAI;AAC3B,QAAM,EAAE,OAAO,MAAM,GAAG,IAAI;AAE5B,MAAI,UAAU;AACZ,UAAM,QAAQ,aAAa,OAAO,MAAM,UAAU;AAElD,QAAI,SAAS,MAAM,QAAQ,QAAQ,MAAM,MAAM,IAAI;AACjD,YAAM,eAAeC,eAAc,OAAO,KAAK,MAAM,MAAM,MAAM,EAAE;AAEnE,SAAG,aAAa,YAAY;AAAA,IAC9B;AAAA,EACF;AAEA,SAAO;AACT;;;ACnCK,IAAM,QAA8B,cAAY,WAAS;AAC9D,QAAM,QAAQ,OAAO,aAAa,aAAa,SAAS,KAAK,IAAI;AAEjE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,QAAI,MAAM,CAAC,EAAE,KAAK,GAAG;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACzBO,SAAS,YAAqB;AACnC,SAAO,UAAU,aAAa,aAAa,WAAW,KAAK,UAAU,SAAS;AAChF;;;ACFO,SAAS,QAAiB;AAC/B,SACE,CAAC,kBAAkB,oBAAoB,kBAAkB,QAAQ,UAAU,MAAM,EAAE,SAAS,UAAU,QAAQ;AAAA,EAE7G,UAAU,UAAU,SAAS,KAAK,KAAK,gBAAgB;AAE5D;;;AC4BO,IAAM,QACX,CAAC,WAAW,MAAM,UAAU,CAAC,MAC7B,CAAC,EAAE,QAAQ,MAAM,IAAI,SAAS,MAAM;AAClC,YAAU;AAAA,IACR,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AAEA,QAAM,eAAe,MAAM;AAGzB,QAAI,MAAM,KAAK,UAAU,GAAG;AAC1B;AAAC,MAAC,KAAK,IAAoB,MAAM;AAAA,IACnC;AAIA,0BAAsB,MAAM;AAC1B,UAAI,CAAC,OAAO,aAAa;AACvB,aAAK,MAAM;AAEX,YAAI,mCAAS,gBAAgB;AAC3B,iBAAO,SAAS,eAAe;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAK,KAAK,SAAS,KAAK,aAAa,QAAS,aAAa,OAAO;AAChE,WAAO;AAAA,EACT;AAGA,MAAI,YAAY,aAAa,QAAQ,CAAC,gBAAgB,OAAO,MAAM,SAAS,GAAG;AAC7E,iBAAa;AACb,WAAO;AAAA,EACT;AAIA,QAAM,YAAY,qBAAqB,GAAG,KAAK,QAAQ,KAAK,OAAO,MAAM;AACzE,QAAM,kBAAkB,OAAO,MAAM,UAAU,GAAG,SAAS;AAE3D,MAAI,UAAU;AACZ,QAAI,CAAC,iBAAiB;AACpB,SAAG,aAAa,SAAS;AAAA,IAC3B;AAIA,QAAI,mBAAmB,GAAG,aAAa;AACrC,SAAG,eAAe,GAAG,WAAW;AAAA,IAClC;AAEA,iBAAa;AAAA,EACf;AAEA,SAAO;AACT;;;ACvEK,IAAM,UAAkC,CAAC,OAAO,OAAO,WAAS;AACrE,SAAO,MAAM,MAAM,CAAC,MAAM,UAAU,GAAG,MAAM,EAAE,GAAG,OAAO,MAAM,CAAC,CAAC;AACnE;;;ACgBO,IAAM,gBACX,CAAC,OAAO,YACR,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,SAAO,SAAS,gBAAgB,EAAE,MAAM,GAAG,UAAU,MAAM,IAAI,GAAG,UAAU,GAAG,GAAG,OAAO,OAAO;AAClG;;;AC1CF,SAAS,YAAAC,iBAAgB;AA0DzB,IAAM,aAAa,CAAC,mBAA2E;AAC7F,SAAO,EAAE,UAAU;AACrB;AAEO,IAAM,kBACX,CAAC,UAAU,OAAO,YAClB,CAAC,EAAE,IAAI,UAAU,OAAO,MAAM;AAjEhC;AAkEI,MAAI,UAAU;AACZ,cAAU;AAAA,MACR,cAAc,OAAO,QAAQ;AAAA,MAC7B,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,GAAG;AAAA,IACL;AAEA,QAAI;AACJ,UAAM,EAAE,UAAU,IAAI,OAAO;AAE7B,UAAM,mBAAmB,CAAC,UAAiB;AACzC,aAAO,KAAK,gBAAgB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA,sBAAsB,MAAM;AAC1B,cACE,mBAAmB,OAAO,WAC1B,OAAO,OAAO,QAAQ,kBAAkB,YACxC,OAAO,QAAQ,eACf;AACA;AAAC,YAAC,OAAO,QAAQ,cAAsB,aAAa;AAAA,UACtD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,eAA6B;AAAA,MACjC,oBAAoB;AAAA,MACpB,GAAG,QAAQ;AAAA,IACb;AAIA,QAAI,CAAC,QAAQ,yBAAyB,CAAC,OAAO,QAAQ,sBAAsB,OAAO,QAAQ,kBAAkB;AAC3G,UAAI;AACF,8BAAsB,OAAO,OAAO,QAAQ;AAAA,UAC1C;AAAA,UACA,uBAAuB;AAAA,QACzB,CAAC;AAAA,MACH,SAAS,GAAG;AACV,yBAAiB,CAAU;AAAA,MAC7B;AAAA,IACF;AAEA,QAAI;AACF,gBAAU,sBAAsB,OAAO,OAAO,QAAQ;AAAA,QACpD;AAAA,QACA,wBAAuB,aAAQ,0BAAR,YAAiC,OAAO,QAAQ;AAAA,MACzE,CAAC;AAAA,IACH,SAAS,GAAG;AACV,uBAAiB,CAAU;AAC3B,aAAO;AAAA,IACT;AAEA,QAAI,EAAE,MAAM,GAAG,IACb,OAAO,aAAa,WAAW,EAAE,MAAM,UAAU,IAAI,SAAS,IAAI,EAAE,MAAM,SAAS,MAAM,IAAI,SAAS,GAAG;AAE3G,QAAI,oBAAoB;AACxB,QAAI,qBAAqB;AACzB,UAAM,QAAQ,WAAW,OAAO,IAAI,UAAU,CAAC,OAAO;AAEtD,UAAM,QAAQ,UAAQ;AAEpB,WAAK,MAAM;AAEX,0BAAoB,oBAAoB,KAAK,UAAU,KAAK,MAAM,WAAW,IAAI;AAEjF,2BAAqB,qBAAqB,KAAK,UAAU;AAAA,IAC3D,CAAC;AAOD,QAAI,SAAS,MAAM,oBAAoB;AACrC,YAAM,EAAE,OAAO,IAAI,GAAG,IAAI,QAAQ,IAAI;AACtC,YAAM,mBAAmB,OAAO,eAAe,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,OAAO;AAEjF,UAAI,kBAAkB;AACpB,gBAAQ;AACR,cAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI;AAIJ,QAAI,mBAAmB;AAGrB,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,qBAAa,MAAM,IAAI,OAAK,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE;AAAA,MACnD,WAAW,iBAAiBC,WAAU;AACpC,YAAI,OAAO;AAEX,cAAM,QAAQ,UAAQ;AACpB,cAAI,KAAK,MAAM;AACb,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF,CAAC;AAED,qBAAa;AAAA,MACf,WAAW,OAAO,UAAU,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,MAAM;AAC/D,qBAAa,MAAM;AAAA,MACrB,OAAO;AACL,qBAAa;AAAA,MACf;AAEA,SAAG,WAAW,YAAY,MAAM,EAAE;AAAA,IACpC,OAAO;AACL,mBAAa;AAEb,YAAM,uBAAuB,UAAU,MAAM,iBAAiB;AAC9D,YAAMC,mBAAkB,UAAU,MAAM,KAAK,EAAE,UAAU,UAAU,MAAM,KAAK,EAAE;AAChF,YAAM,aAAa,UAAU,MAAM,KAAK,EAAE,QAAQ,OAAO;AAEzD,UAAI,wBAAwBA,oBAAmB,YAAY;AACzD,eAAO,KAAK,IAAI,GAAG,OAAO,CAAC;AAAA,MAC7B;AAEA,SAAG,YAAY,MAAM,IAAI,UAAU;AAAA,IACrC;AAGA,QAAI,QAAQ,iBAAiB;AAC3B,8BAAwB,IAAI,GAAG,MAAM,SAAS,GAAG,EAAE;AAAA,IACrD;AAEA,QAAI,QAAQ,iBAAiB;AAC3B,SAAG,QAAQ,mBAAmB,EAAE,MAAM,MAAM,WAAW,CAAC;AAAA,IAC1D;AAEA,QAAI,QAAQ,iBAAiB;AAC3B,SAAG,QAAQ,mBAAmB,EAAE,MAAM,MAAM,WAAW,CAAC;AAAA,IAC1D;AAAA,EACF;AAEA,SAAO;AACT;;;AChNF;AAAA,EACE,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,UAAU;AAAA,OACL;AAyCA,IAAM,SACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,eAAe,OAAO,QAAQ;AACvC;AAEK,IAAM,WACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,iBAAiB,OAAO,QAAQ;AACzC;AAEK,IAAM,eACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,qBAAqB,OAAO,QAAQ;AAC7C;AAEK,IAAM,cACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,oBAAoB,OAAO,QAAQ;AAC5C;;;ACpEF,SAAS,iBAAiB;AAgBnB,IAAM,mBACX,MACA,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM;AAC3B,MAAI;AACF,UAAM,QAAQ,UAAU,MAAM,KAAK,MAAM,UAAU,MAAM,KAAK,EAAE;AAEhE,QAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,aAAO;AAAA,IACT;AAEA,OAAG,KAAK,OAAO,CAAC;AAEhB,QAAI,UAAU;AACZ,eAAS,EAAE;AAAA,IACb;AAEA,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;;;ACpCF,SAAS,aAAAC,kBAAiB;AAgBnB,IAAM,kBACX,MACA,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM;AAC3B,MAAI;AACF,UAAM,QAAQA,WAAU,MAAM,KAAK,MAAM,UAAU,MAAM,KAAK,CAAE;AAEhE,QAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,aAAO;AAAA,IACT;AAEA,OAAG,KAAK,OAAO,CAAC;AAEhB,QAAI,UAAU;AACZ,eAAS,EAAE;AAAA,IACb;AAEA,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;;;ACpCF,SAAS,yBAAyB,uBAAuB;AAelD,IAAM,wBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,gBAAgB,OAAO,QAAQ;AACxC;;;ACnBF,SAAS,wBAAwBC,wBAAuB;AAejD,IAAM,uBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAOA,iBAAgB,OAAO,QAAQ;AACxC;;;ACnBK,SAAS,UAAmB;AACjC,SAAO,OAAO,cAAc,cAAc,MAAM,KAAK,UAAU,QAAQ,IAAI;AAC7E;;;ACEA,SAAS,iBAAiB,MAAc;AACtC,QAAM,QAAQ,KAAK,MAAM,QAAQ;AACjC,MAAI,SAAS,MAAM,MAAM,SAAS,CAAC;AAEnC,MAAI,WAAW,SAAS;AACtB,aAAS;AAAA,EACX;AAEA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG;AAC5C,UAAM,MAAM,MAAM,CAAC;AAEnB,QAAI,kBAAkB,KAAK,GAAG,GAAG;AAC/B,aAAO;AAAA,IACT,WAAW,YAAY,KAAK,GAAG,GAAG;AAChC,YAAM;AAAA,IACR,WAAW,sBAAsB,KAAK,GAAG,GAAG;AAC1C,aAAO;AAAA,IACT,WAAW,cAAc,KAAK,GAAG,GAAG;AAClC,cAAQ;AAAA,IACV,WAAW,SAAS,KAAK,GAAG,GAAG;AAC7B,UAAI,MAAM,KAAK,QAAQ,GAAG;AACxB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,+BAA+B,GAAG,EAAE;AAAA,IACtD;AAAA,EACF;AAEA,MAAI,KAAK;AACP,aAAS,OAAO,MAAM;AAAA,EACxB;AAEA,MAAI,MAAM;AACR,aAAS,QAAQ,MAAM;AAAA,EACzB;AAEA,MAAI,MAAM;AACR,aAAS,QAAQ,MAAM;AAAA,EACzB;AAEA,MAAI,OAAO;AACT,aAAS,SAAS,MAAM;AAAA,EAC1B;AAEA,SAAO;AACT;AAeO,IAAM,mBACX,UACA,CAAC,EAAE,QAAQ,MAAM,IAAI,SAAS,MAAM;AAClC,QAAM,OAAO,iBAAiB,IAAI,EAAE,MAAM,QAAQ;AAClD,QAAM,MAAM,KAAK,KAAK,UAAQ,CAAC,CAAC,OAAO,QAAQ,QAAQ,OAAO,EAAE,SAAS,IAAI,CAAC;AAC9E,QAAM,QAAQ,IAAI,cAAc,WAAW;AAAA,IACzC,KAAK,QAAQ,UAAU,MAAM;AAAA,IAC7B,QAAQ,KAAK,SAAS,KAAK;AAAA,IAC3B,SAAS,KAAK,SAAS,MAAM;AAAA,IAC7B,SAAS,KAAK,SAAS,MAAM;AAAA,IAC7B,UAAU,KAAK,SAAS,OAAO;AAAA,IAC/B,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC;AAED,QAAM,sBAAsB,OAAO,mBAAmB,MAAM;AAC1D,SAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,KAAK,CAAC;AAAA,EACpD,CAAC;AAED,6DAAqB,MAAM,QAAQ,UAAQ;AACzC,UAAM,UAAU,KAAK,IAAI,GAAG,OAAO;AAEnC,QAAI,WAAW,UAAU;AACvB,SAAG,UAAU,OAAO;AAAA,IACtB;AAAA,EACF;AAEA,SAAO;AACT;;;ACnGF,SAAS,QAAQ,oBAAoB;AAsB9B,IAAM,OACX,CAAC,YAAY,aAAa,CAAC,MAC3B,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAMC,YAAW,aAAa,OAAO,MAAM,UAAU;AAErD,MAAI,CAACA,WAAU;AACb,WAAO;AAAA,EACT;AAEA,SAAO,aAAa,OAAO,QAAQ;AACrC;;;ACjCF,SAAS,kBAAkB,8BAA8B;AAgBlD,IAAM,iBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,uBAAuB,OAAO,QAAQ;AAC/C;;;ACnBF,SAAS,gBAAgB,4BAA4B;AAkB9C,IAAM,eACX,gBACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,SAAO,qBAAqB,IAAI,EAAE,OAAO,QAAQ;AACnD;;;ACzBF,SAAS,iBAAiB,6BAA6B;AAgBhD,IAAM,gBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,sBAAsB,OAAO,QAAQ;AAC9C;;;ACfK,SAAS,YAAY,KAA0B,aAAqD;AACzG,QAAM,QAAQ,OAAO,gBAAgB,WAAW,CAAC,WAAW,IAAI;AAEhE,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,CAAC,QAA6B,SAAS;AACpE,QAAI,CAAC,MAAM,SAAS,IAAI,GAAG;AACzB,aAAO,IAAI,IAAI,IAAI,IAAI;AAAA,IACzB;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;;;ACOO,IAAM,kBACX,CAAC,YAAY,eACb,CAAC,EAAE,IAAI,OAAO,SAAS,MAAM;AAC3B,MAAI,WAA4B;AAChC,MAAI,WAA4B;AAEhC,QAAM,aAAa;AAAA,IACjB,OAAO,eAAe,WAAW,aAAa,WAAW;AAAA,IACzD,MAAM;AAAA,EACR;AAEA,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,QAAQ;AACzB,eAAW,YAAY,YAAwB,MAAM,MAAM;AAAA,EAC7D;AAEA,MAAI,eAAe,QAAQ;AACzB,eAAW,YAAY,YAAwB,MAAM,MAAM;AAAA,EAC7D;AAEA,MAAI,UAAU;AACZ,OAAG,UAAU,OAAO,QAAQ,WAAS;AACnC,YAAM,IAAI,aAAa,MAAM,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,QAAQ;AACpE,YAAI,YAAY,aAAa,KAAK,MAAM;AACtC,aAAG,cAAc,KAAK,QAAW,YAAY,KAAK,OAAO,UAAU,CAAC;AAAA,QACtE;AAEA,YAAI,YAAY,KAAK,MAAM,QAAQ;AACjC,eAAK,MAAM,QAAQ,UAAQ;AACzB,gBAAI,aAAa,KAAK,MAAM;AAC1B,iBAAG,QAAQ,KAAK,MAAM,KAAK,UAAU,SAAS,OAAO,YAAY,KAAK,OAAO,UAAU,CAAC,CAAC;AAAA,YAC3F;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AClDK,IAAM,iBACX,MACA,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,MAAI,UAAU;AACZ,OAAG,eAAe;AAAA,EACpB;AAEA,SAAO;AACT;;;ACtBF,SAAS,oBAAoB;AAgBtB,IAAM,YACX,MACA,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,MAAI,UAAU;AACZ,UAAM,YAAY,IAAI,aAAa,GAAG,GAAG;AAEzC,OAAG,aAAa,SAAS;AAAA,EAC3B;AAEA,SAAO;AACT;;;AC1BF,SAAS,sBAAsB,kCAAkC;AAgB1D,IAAM,qBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,2BAA2B,OAAO,QAAQ;AACnD;;;ACpBF,SAAS,qBAAqB,iCAAiC;AAgBxD,IAAM,oBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,0BAA0B,OAAO,QAAQ;AAClD;;;ACpBF,SAAS,oBAAoB,gCAAgC;AAgBtD,IAAM,mBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,yBAAyB,OAAO,QAAQ;AACjD;;;AClBF,SAAS,sBAAsB,kCAAkC;AAgB1D,IAAM,qBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,2BAA2B,OAAO,QAAQ;AACnD;;;ACpBF,SAAS,wBAAwB,oCAAoC;AAgB9D,IAAM,uBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,SAAO,6BAA6B,OAAO,QAAQ;AACrD;;;ACyBK,IAAM,aACX,CAAC,SAAS,EAAE,uBAAuB,aAAa,MAAM,eAAe,CAAC,EAAE,IAAI,CAAC,MAC7E,CAAC,EAAE,QAAQ,IAAI,UAAU,SAAS,MAAM;AACtC,QAAM,EAAE,IAAI,IAAI;AAIhB,MAAI,aAAa,uBAAuB,QAAQ;AAC9C,UAAMC,YAAW,eAAe,SAAS,OAAO,QAAQ,cAAc;AAAA,MACpE,uBAAuB,wDAAyB,OAAO,QAAQ;AAAA,IACjE,CAAC;AAED,QAAI,UAAU;AACZ,SAAG,YAAY,GAAG,IAAI,QAAQ,MAAMA,SAAQ,EAAE,QAAQ,iBAAiB,CAAC,UAAU;AAAA,IACpF;AACA,WAAO;AAAA,EACT;AAEA,MAAI,UAAU;AACZ,OAAG,QAAQ,iBAAiB,CAAC,UAAU;AAAA,EACzC;AAEA,SAAO,SAAS,gBAAgB,EAAE,MAAM,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG,SAAS;AAAA,IAC1E;AAAA,IACA,uBAAuB,wDAAyB,OAAO,QAAQ;AAAA,EACjE,CAAC;AACH;;;ACpDF,SAAS,WAAW,OAAoB,IAAiB,aAAuB;AArBhF;AAsBE,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,SAA6B;AAEjC,MAAI,gBAAgB,SAAS,GAAG;AAC9B,aAAS,UAAU;AAAA,EACrB;AAEA,MAAI,QAAQ;AACV,UAAM,gBAAe,WAAM,gBAAN,YAAqB,OAAO,MAAM;AACvD,UAAM,uBAAuB,OAAO,OAAO,KAAK,eAAe,WAAW;AAG1E,WACE,yBACC,CAAC,CAAC,YAAY,QAAQ,YAAY,KAAK,CAAC,aAAa,KAAK,UAAQ,KAAK,KAAK,SAAS,WAAW,CAAC;AAAA,EAEtG;AAEA,QAAM,EAAE,OAAO,IAAI;AAEnB,SAAO,OAAO,KAAK,CAAC,EAAE,OAAO,IAAI,MAAM;AACrC,QAAI,uBACF,MAAM,UAAU,IAAI,MAAM,IAAI,iBAAiB,MAAM,IAAI,KAAK,eAAe,WAAW,IAAI;AAE9F,UAAM,IAAI,aAAa,MAAM,KAAK,IAAI,KAAK,CAAC,MAAM,MAAM,WAAW;AAEjE,UAAI,sBAAsB;AACxB,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,UAAU;AACjB,cAAM,uBAAuB,CAAC,UAAU,OAAO,KAAK,eAAe,WAAW;AAC9E,cAAM,4BACJ,CAAC,CAAC,YAAY,QAAQ,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAa,UAAU,KAAK,SAAS,WAAW,CAAC;AAEzG,+BAAuB,wBAAwB;AAAA,MACjD;AACA,aAAO,CAAC;AAAA,IACV,CAAC;AAED,WAAO;AAAA,EACT,CAAC;AACH;AACO,IAAM,UACX,CAAC,YAAY,aAAa,CAAC,MAC3B,CAAC,EAAE,IAAI,OAAO,SAAS,MAAM;AAC3B,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,MAAI,UAAU;AACZ,QAAI,OAAO;AACT,YAAM,gBAAgB,kBAAkB,OAAO,IAAI;AAEnD,SAAG;AAAA,QACD,KAAK,OAAO;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACL,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,WAAS;AACtB,cAAM,OAAO,MAAM,MAAM;AACzB,cAAM,KAAK,MAAM,IAAI;AAErB,cAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AAC9C,gBAAM,cAAc,KAAK,IAAI,KAAK,IAAI;AACtC,gBAAM,YAAY,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAClD,gBAAM,cAAc,KAAK,MAAM,KAAK,UAAQ,KAAK,SAAS,IAAI;AAK9D,cAAI,aAAa;AACf,iBAAK,MAAM,QAAQ,UAAQ;AACzB,kBAAI,SAAS,KAAK,MAAM;AACtB,mBAAG;AAAA,kBACD;AAAA,kBACA;AAAA,kBACA,KAAK,OAAO;AAAA,oBACV,GAAG,KAAK;AAAA,oBACR,GAAG;AAAA,kBACL,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,eAAG,QAAQ,aAAa,WAAW,KAAK,OAAO,UAAU,CAAC;AAAA,UAC5D;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,IAAI,IAAI;AACnC;;;ACnGK,IAAM,UACX,CAAC,KAAK,UACN,CAAC,EAAE,GAAG,MAAM;AACV,KAAG,QAAQ,KAAK,KAAK;AAErB,SAAO;AACT;;;ACxBF,SAAS,oBAAoB;AAoBtB,IAAM,UACX,CAAC,YAAY,aAAa,CAAC,MAC3B,CAAC,EAAE,OAAO,UAAU,MAAM,MAAM;AAC9B,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,MAAI;AAEJ,MAAI,MAAM,UAAU,QAAQ,WAAW,MAAM,UAAU,KAAK,GAAG;AAE7D,uBAAmB,MAAM,UAAU,QAAQ,OAAO;AAAA,EACpD;AAGA,MAAI,CAAC,KAAK,aAAa;AACrB,YAAQ,KAAK,sEAAsE;AAEnF,WAAO;AAAA,EACT;AAEA,SACE,MAAM,EAEH,QAAQ,CAAC,EAAE,SAAS,MAAM;AACzB,UAAM,cAAc,aAAa,MAAM,EAAE,GAAG,kBAAkB,GAAG,WAAW,CAAC,EAAE,KAAK;AAEpF,QAAI,aAAa;AACf,aAAO;AAAA,IACT;AAEA,WAAO,SAAS,WAAW;AAAA,EAC7B,CAAC,EACA,QAAQ,CAAC,EAAE,OAAO,aAAa,MAAM;AACpC,WAAO,aAAa,MAAM,EAAE,GAAG,kBAAkB,GAAG,WAAW,CAAC,EAAE,cAAc,QAAQ;AAAA,EAC1F,CAAC,EACA,IAAI;AAEX;;;ACxDF,SAAS,iBAAAC,sBAAqB;AAkBvB,IAAM,mBACX,cACA,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,MAAI,UAAU;AACZ,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,OAAO,OAAO,UAAU,GAAG,IAAI,QAAQ,IAAI;AACjD,UAAM,YAAYC,eAAc,OAAO,KAAK,IAAI;AAEhD,OAAG,aAAa,SAAS;AAAA,EAC3B;AAEA,SAAO;AACT;;;AC9BF,SAAS,iBAAAC,sBAAqB;AAkBvB,IAAM,mBACX,cACA,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,MAAI,UAAU;AACZ,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,EAAE,MAAM,GAAG,IAAI,OAAO,aAAa,WAAW,EAAE,MAAM,UAAU,IAAI,SAAS,IAAI;AACvF,UAAM,SAASC,eAAc,QAAQ,GAAG,EAAE;AAC1C,UAAM,SAASA,eAAc,MAAM,GAAG,EAAE;AACxC,UAAM,eAAe,OAAO,MAAM,QAAQ,MAAM;AAChD,UAAM,cAAc,OAAO,IAAI,QAAQ,MAAM;AAC7C,UAAM,YAAYA,eAAc,OAAO,KAAK,cAAc,WAAW;AAErE,OAAG,aAAa,SAAS;AAAA,EAC3B;AAEA,SAAO;AACT;;;ACjCF,SAAS,gBAAgB,4BAA4B;AAkB9C,IAAM,eACX,gBACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,SAAO,qBAAqB,IAAI,EAAE,OAAO,QAAQ;AACnD;;;ACxBF,SAAS,iBAAAC,gBAAe,iBAAAC,sBAAqB;AAC7C,SAAS,gBAAgB;AAMzB,SAAS,YAAY,OAAoB,iBAA4B;AACnE,QAAM,QAAQ,MAAM,eAAgB,MAAM,UAAU,IAAI,gBAAgB,MAAM,UAAU,MAAM,MAAM;AAEpG,MAAI,OAAO;AACT,UAAM,gBAAgB,MAAM,OAAO,UAAQ,mDAAiB,SAAS,KAAK,KAAK,KAAK;AAEpF,UAAM,GAAG,YAAY,aAAa;AAAA,EACpC;AACF;AAgBO,IAAM,aACX,CAAC,EAAE,YAAY,KAAK,IAAI,CAAC,MACzB,CAAC,EAAE,IAAI,OAAO,UAAU,OAAO,MAAM;AACnC,QAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,QAAM,EAAE,OAAO,IAAI,IAAI;AACvB,QAAM,sBAAsB,OAAO,iBAAiB;AACpD,QAAM,gBAAgB,sBAAsB,qBAAqB,MAAM,KAAK,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,KAAK;AAE3G,MAAI,qBAAqBC,kBAAiB,UAAU,KAAK,SAAS;AAChE,QAAI,CAAC,MAAM,gBAAgB,CAAC,SAAS,KAAK,MAAM,GAAG,GAAG;AACpD,aAAO;AAAA,IACT;AAEA,QAAI,UAAU;AACZ,UAAI,WAAW;AACb,oBAAY,OAAO,OAAO,iBAAiB,eAAe;AAAA,MAC5D;AAEA,SAAG,MAAM,MAAM,GAAG,EAAE,eAAe;AAAA,IACrC;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM,OAAO,SAAS;AACzB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,IAAI,iBAAiB,IAAI,OAAO,QAAQ;AAEtD,QAAM,QAAQ,MAAM,UAAU,IAAI,SAAY,eAAe,MAAM,KAAK,EAAE,EAAE,eAAe,MAAM,WAAW,EAAE,CAAC,CAAC;AAEhH,MAAI,QACF,SAAS,QACL;AAAA,IACE;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF,IACA;AAEN,MAAI,MAAM,SAAS,GAAG,KAAK,GAAG,QAAQ,IAAI,MAAM,GAAG,GAAG,GAAG,KAAK;AAE9D,MAAI,CAAC,SAAS,CAAC,OAAO,SAAS,GAAG,KAAK,GAAG,QAAQ,IAAI,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,EAAE,MAAM,MAAM,CAAC,IAAI,MAAS,GAAG;AAC3G,UAAM;AACN,YAAQ,QACJ;AAAA,MACE;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,IACA;AAAA,EACN;AAEA,MAAI,UAAU;AACZ,QAAI,KAAK;AACP,UAAI,qBAAqBC,gBAAe;AACtC,WAAG,gBAAgB;AAAA,MACrB;AAEA,SAAG,MAAM,GAAG,QAAQ,IAAI,MAAM,GAAG,GAAG,GAAG,KAAK;AAE5C,UAAI,SAAS,CAAC,SAAS,CAAC,MAAM,gBAAgB,MAAM,OAAO,SAAS,OAAO;AACzE,cAAMC,SAAQ,GAAG,QAAQ,IAAI,MAAM,OAAO,CAAC;AAC3C,cAAM,SAAS,GAAG,IAAI,QAAQA,MAAK;AAEnC,YAAI,MAAM,KAAK,EAAE,EAAE,eAAe,OAAO,MAAM,GAAG,OAAO,MAAM,IAAI,GAAG,KAAK,GAAG;AAC5E,aAAG,cAAc,GAAG,QAAQ,IAAI,MAAM,OAAO,CAAC,GAAG,KAAK;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,WAAW;AACb,kBAAY,OAAO,OAAO,iBAAiB,eAAe;AAAA,IAC5D;AAEA,OAAG,eAAe;AAAA,EACpB;AAEA,SAAO;AACT;;;ACjHF,SAAS,YAAAC,WAAU,aAAa;AAChC,SAAS,iBAAAC,sBAAqB;AAC9B,SAAS,YAAAC,iBAAgB;AAoBlB,IAAM,gBACX,CAAC,YAAY,gBAAgB,CAAC,MAC9B,CAAC,EAAE,IAAI,OAAO,UAAU,OAAO,MAAM;AAzBvC;AA0BI,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,OAAO,IAAI,IAAI,MAAM;AAI7B,QAAM,OAAwB,MAAM,UAAU;AAE9C,MAAK,QAAQ,KAAK,WAAY,MAAM,QAAQ,KAAK,CAAC,MAAM,WAAW,GAAG,GAAG;AACvE,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,MAAM,KAAK,EAAE;AAEjC,MAAI,YAAY,SAAS,MAAM;AAC7B,WAAO;AAAA,EACT;AAEA,QAAM,sBAAsB,OAAO,iBAAiB;AAEpD,MAAI,MAAM,OAAO,QAAQ,SAAS,KAAK,MAAM,KAAK,EAAE,EAAE,eAAe,MAAM,WAAW,EAAE,GAAG;AAIzF,QAAI,MAAM,UAAU,KAAK,MAAM,KAAK,EAAE,EAAE,SAAS,QAAQ,MAAM,MAAM,EAAE,MAAM,MAAM,KAAK,EAAE,EAAE,aAAa,GAAG;AAC1G,aAAO;AAAA,IACT;AAEA,QAAI,UAAU;AACZ,UAAI,OAAOC,UAAS;AAEpB,YAAM,cAAc,MAAM,MAAM,EAAE,IAAI,IAAI,MAAM,MAAM,EAAE,IAAI,IAAI;AAIhE,eAAS,IAAI,MAAM,QAAQ,aAAa,KAAK,MAAM,QAAQ,GAAG,KAAK,GAAG;AACpE,eAAOA,UAAS,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,MAC/C;AAEA,YAAM;AAAA;AAAA,QAEJ,MAAM,WAAW,EAAE,IAAI,MAAM,KAAK,EAAE,EAAE,aAClC,IACA,MAAM,WAAW,EAAE,IAAI,MAAM,KAAK,EAAE,EAAE,aACpC,IACA;AAAA;AAGR,YAAMC,yBAAwB;AAAA,QAC5B,GAAG,sBAAsB,qBAAqB,MAAM,KAAK,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,KAAK;AAAA,QACxF,GAAG;AAAA,MACL;AACA,YAAMC,cAAW,UAAK,aAAa,gBAAlB,mBAA+B,cAAcD,4BAA0B;AAExF,aAAO,KAAK,OAAOD,UAAS,KAAK,KAAK,cAAc,MAAME,SAAQ,KAAK,MAAS,CAAC;AAEjF,YAAM,QAAQ,MAAM,OAAO,MAAM,SAAS,cAAc,EAAE;AAE1D,SAAG,QAAQ,OAAO,MAAM,MAAM,CAAC,UAAU,GAAG,IAAI,MAAM,MAAM,IAAI,aAAa,CAAC,CAAC;AAE/E,UAAI,MAAM;AAEV,SAAG,IAAI,aAAa,OAAO,GAAG,IAAI,QAAQ,MAAM,CAAC,GAAG,QAAQ;AAC1D,YAAI,MAAM,IAAI;AACZ,iBAAO;AAAA,QACT;AAEA,YAAI,EAAE,eAAe,EAAE,QAAQ,SAAS,GAAG;AACzC,gBAAM,MAAM;AAAA,QACd;AAAA,MACF,CAAC;AAED,UAAI,MAAM,IAAI;AACZ,WAAG,aAAaC,eAAc,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC;AAAA,MACzD;AAEA,SAAG,eAAe;AAAA,IACpB;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,IAAI,QAAQ,MAAM,IAAI,IAAI,YAAY,eAAe,CAAC,EAAE,cAAc;AAEvF,QAAM,oBAAoB;AAAA,IACxB,GAAG,sBAAsB,qBAAqB,YAAY,KAAK,MAAM,YAAY,KAAK;AAAA,IACtF,GAAG;AAAA,EACL;AACA,QAAM,wBAAwB;AAAA,IAC5B,GAAG,sBAAsB,qBAAqB,MAAM,KAAK,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,KAAK;AAAA,IACxF,GAAG;AAAA,EACL;AAEA,KAAG,OAAO,MAAM,KAAK,IAAI,GAAG;AAE5B,QAAM,QAAQ,WACV;AAAA,IACE,EAAE,MAAM,OAAO,kBAAkB;AAAA,IACjC,EAAE,MAAM,UAAU,OAAO,sBAAsB;AAAA,EACjD,IACA,CAAC,EAAE,MAAM,OAAO,kBAAkB,CAAC;AAEvC,MAAI,CAACC,UAAS,GAAG,KAAK,MAAM,KAAK,CAAC,GAAG;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,UAAU;AACZ,UAAM,EAAE,WAAW,YAAY,IAAI;AACnC,UAAM,EAAE,gBAAgB,IAAI,OAAO;AACnC,UAAM,QAAQ,eAAgB,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAM;AAElF,OAAG,MAAM,MAAM,KAAK,GAAG,KAAK,EAAE,eAAe;AAE7C,QAAI,CAAC,SAAS,CAAC,UAAU;AACvB,aAAO;AAAA,IACT;AAEA,UAAM,gBAAgB,MAAM,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEnF,OAAG,YAAY,aAAa;AAAA,EAC9B;AAEA,SAAO;AACT;;;AClJF,SAAS,eAAe;AAOxB,IAAM,oBAAoB,CAAC,IAAiB,aAAgC;AAC1E,QAAM,OAAO,eAAe,UAAQ,KAAK,SAAS,QAAQ,EAAE,GAAG,SAAS;AAExE,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,GAAG,IAAI,QAAQ,KAAK,IAAI,GAAG,KAAK,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,KAAK;AAE1E,MAAI,WAAW,QAAW;AACxB,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,GAAG,IAAI,OAAO,MAAM;AACvC,QAAM,mBAAmB,KAAK,KAAK,UAAS,yCAAY,SAAQ,QAAQ,GAAG,KAAK,KAAK,GAAG;AAExF,MAAI,CAAC,kBAAkB;AACrB,WAAO;AAAA,EACT;AAEA,KAAG,KAAK,KAAK,GAAG;AAEhB,SAAO;AACT;AAEA,IAAM,mBAAmB,CAAC,IAAiB,aAAgC;AACzE,QAAM,OAAO,eAAe,UAAQ,KAAK,SAAS,QAAQ,EAAE,GAAG,SAAS;AAExE,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,GAAG,IAAI,QAAQ,KAAK,KAAK,EAAE,MAAM,KAAK,KAAK;AAEzD,MAAI,UAAU,QAAW;AACvB,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,GAAG,IAAI,OAAO,KAAK;AACrC,QAAM,kBAAkB,KAAK,KAAK,UAAS,uCAAW,SAAQ,QAAQ,GAAG,KAAK,KAAK;AAEnF,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AAEA,KAAG,KAAK,KAAK;AAEb,SAAO;AACT;AAuBO,IAAM,aACX,CAAC,gBAAgB,gBAAgB,WAAW,aAAa,CAAC,MAC1D,CAAC,EAAE,QAAQ,IAAI,OAAO,UAAU,OAAO,UAAU,IAAI,MAAM;AACzD,QAAM,EAAE,YAAY,gBAAgB,IAAI,OAAO;AAC/C,QAAM,WAAW,YAAY,gBAAgB,MAAM,MAAM;AACzD,QAAM,WAAW,YAAY,gBAAgB,MAAM,MAAM;AACzD,QAAM,EAAE,WAAW,YAAY,IAAI;AACnC,QAAM,EAAE,OAAO,IAAI,IAAI;AACvB,QAAM,QAAQ,MAAM,WAAW,GAAG;AAElC,QAAM,QAAQ,eAAgB,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAM;AAElF,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,eAAe,UAAQ,OAAO,KAAK,KAAK,MAAM,UAAU,CAAC,EAAE,SAAS;AAEvF,MAAI,MAAM,SAAS,KAAK,cAAc,MAAM,QAAQ,WAAW,SAAS,GAAG;AAEzE,QAAI,WAAW,KAAK,SAAS,UAAU;AACrC,aAAO,SAAS,aAAa,QAAQ;AAAA,IACvC;AAGA,QAAI,OAAO,WAAW,KAAK,KAAK,MAAM,UAAU,KAAK,SAAS,aAAa,WAAW,KAAK,OAAO,KAAK,UAAU;AAC/G,aAAO,MAAM,EACV,QAAQ,MAAM;AACb,WAAG,cAAc,WAAW,KAAK,QAAQ;AAEzC,eAAO;AAAA,MACT,CAAC,EACA,QAAQ,MAAM,kBAAkB,IAAI,QAAQ,CAAC,EAC7C,QAAQ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,EAC5C,IAAI;AAAA,IACT;AAAA,EACF;AACA,MAAI,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU;AACrC,WACE,MAAM,EAEH,QAAQ,MAAM;AACb,YAAM,gBAAgB,IAAI,EAAE,WAAW,UAAU,UAAU;AAE3D,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAEA,aAAO,SAAS,WAAW;AAAA,IAC7B,CAAC,EACA,WAAW,UAAU,UAAU,EAC/B,QAAQ,MAAM,kBAAkB,IAAI,QAAQ,CAAC,EAC7C,QAAQ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,EAC5C,IAAI;AAAA,EAEX;AAEA,SACE,MAAM,EAEH,QAAQ,MAAM;AACb,UAAM,gBAAgB,IAAI,EAAE,WAAW,UAAU,UAAU;AAE3D,UAAM,gBAAgB,MAAM,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEnF,OAAG,YAAY,aAAa;AAE5B,QAAI,eAAe;AACjB,aAAO;AAAA,IACT;AAEA,WAAO,SAAS,WAAW;AAAA,EAC7B,CAAC,EACA,WAAW,UAAU,UAAU,EAC/B,QAAQ,MAAM,kBAAkB,IAAI,QAAQ,CAAC,EAC7C,QAAQ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,EAC5C,IAAI;AAEX;;;ACxHK,IAAM,aACX,CAAC,YAAY,aAAa,CAAC,GAAG,UAAU,CAAC,MACzC,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,EAAE,uBAAuB,MAAM,IAAI;AACzC,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAMC,YAAW,aAAa,OAAO,MAAM,UAAU;AAErD,MAAIA,WAAU;AACZ,WAAO,SAAS,UAAU,MAAM,EAAE,qBAAqB,CAAC;AAAA,EAC1D;AAEA,SAAO,SAAS,QAAQ,MAAM,UAAU;AAC1C;;;ACzBK,IAAM,aACX,CAAC,YAAY,kBAAkB,aAAa,CAAC,MAC7C,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,aAAa,YAAY,kBAAkB,MAAM,MAAM;AAC7D,QAAMC,YAAW,aAAa,OAAO,MAAM,UAAU;AAErD,MAAI;AAEJ,MAAI,MAAM,UAAU,QAAQ,WAAW,MAAM,UAAU,KAAK,GAAG;AAE7D,uBAAmB,MAAM,UAAU,QAAQ,OAAO;AAAA,EACpD;AAEA,MAAIA,WAAU;AACZ,WAAO,SAAS,QAAQ,YAAY,gBAAgB;AAAA,EACtD;AAIA,SAAO,SAAS,QAAQ,MAAM,EAAE,GAAG,kBAAkB,GAAG,WAAW,CAAC;AACtE;;;AC1BK,IAAM,aACX,CAAC,YAAY,aAAa,CAAC,MAC3B,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAMC,YAAW,aAAa,OAAO,MAAM,UAAU;AAErD,MAAIA,WAAU;AACZ,WAAO,SAAS,KAAK,IAAI;AAAA,EAC3B;AAEA,SAAO,SAAS,OAAO,MAAM,UAAU;AACzC;;;ACjBK,IAAM,gBACX,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,UAAU,MAAM;AAEtB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1C,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI;AAIJ,QAAI,OAAO,KAAK,iBAAiB,WAAW,OAAO,SAAS,KAAK,IAAI;AACnE,UAAI,UAAU;AACZ,cAAM,KAAK,MAAM;AACjB,cAAM,SAAS,SAAS;AAExB,iBAAS,IAAI,OAAO,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AACpD,aAAG,KAAK,OAAO,MAAM,CAAC,EAAE,OAAO,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,QAChD;AAEA,YAAI,SAAS,MAAM;AACjB,gBAAM,QAAQ,GAAG,IAAI,QAAQ,SAAS,IAAI,EAAE,MAAM;AAElD,aAAG,YAAY,SAAS,MAAM,SAAS,IAAI,MAAM,OAAO,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,QACpF,OAAO;AACL,aAAG,OAAO,SAAS,MAAM,SAAS,EAAE;AAAA,QACtC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AClCK,IAAM,gBACX,MACA,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,OAAO,OAAO,IAAI;AAE1B,MAAI,OAAO;AACT,WAAO;AAAA,EACT;AAEA,MAAI,UAAU;AACZ,WAAO,QAAQ,WAAS;AACtB,SAAG,WAAW,MAAM,MAAM,KAAK,MAAM,IAAI,GAAG;AAAA,IAC9C,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;ACCK,IAAM,YACX,CAAC,YAAY,UAAU,CAAC,MACxB,CAAC,EAAE,IAAI,OAAO,SAAS,MAAM;AAlC/B;AAmCI,QAAM,EAAE,uBAAuB,MAAM,IAAI;AACzC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,OAAO,OAAO,OAAO,IAAI;AAEjC,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,sBAAsB;AACjC,QAAI,EAAE,MAAM,GAAG,IAAI;AACnB,UAAM,SAAQ,WAAM,MAAM,EAAE,KAAK,UAAQ,KAAK,SAAS,IAAI,MAA7C,mBAAgD;AAC9D,UAAM,QAAQ,aAAa,OAAO,MAAM,KAAK;AAE7C,QAAI,OAAO;AACT,aAAO,MAAM;AACb,WAAK,MAAM;AAAA,IACb;AAEA,OAAG,WAAW,MAAM,IAAI,IAAI;AAAA,EAC9B,OAAO;AACL,WAAO,QAAQ,WAAS;AACtB,SAAG,WAAW,MAAM,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI;AAAA,IACpD,CAAC;AAAA,EACH;AAEA,KAAG,iBAAiB,IAAI;AAExB,SAAO;AACT;;;AChCK,IAAM,mBACX,CAAC,YAAY,aAAa,CAAC,MAC3B,CAAC,EAAE,IAAI,OAAO,SAAS,MAAM;AAC3B,MAAI,WAA4B;AAChC,MAAI,WAA4B;AAEhC,QAAM,aAAa;AAAA,IACjB,OAAO,eAAe,WAAW,aAAa,WAAW;AAAA,IACzD,MAAM;AAAA,EACR;AAEA,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,QAAQ;AACzB,eAAW,YAAY,YAAwB,MAAM,MAAM;AAAA,EAC7D;AAEA,MAAI,eAAe,QAAQ;AACzB,eAAW,YAAY,YAAwB,MAAM,MAAM;AAAA,EAC7D;AAEA,MAAI,UAAU;AACZ,OAAG,UAAU,OAAO,QAAQ,CAAC,UAA0B;AACrD,YAAM,OAAO,MAAM,MAAM;AACzB,YAAM,KAAK,MAAM,IAAI;AAErB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,GAAG,UAAU,OAAO;AACtB,cAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAY,QAAgB;AAC5D,cAAI,YAAY,aAAa,KAAK,MAAM;AACtC,0BAAc,KAAK,IAAI,KAAK,IAAI;AAChC,wBAAY,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAC5C,sBAAU;AACV,uBAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAY,QAAgB;AAC5D,cAAI,MAAM,QAAQ,YAAY,aAAa,KAAK,MAAM;AACpD,0BAAc,KAAK,IAAI,KAAK,IAAI;AAChC,wBAAY,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAC5C,sBAAU;AACV,uBAAW;AAAA,UACb;AAEA,cAAI,OAAO,QAAQ,OAAO,IAAI;AAC5B,gBAAI,YAAY,aAAa,KAAK,MAAM;AACtC,iBAAG,cAAc,KAAK,QAAW;AAAA,gBAC/B,GAAG,KAAK;AAAA,gBACR,GAAG;AAAA,cACL,CAAC;AAAA,YACH;AAEA,gBAAI,YAAY,KAAK,MAAM,QAAQ;AACjC,mBAAK,MAAM,QAAQ,CAAC,SAAe;AACjC,oBAAI,aAAa,KAAK,MAAM;AAC1B,wBAAM,eAAe,KAAK,IAAI,KAAK,IAAI;AACvC,wBAAM,aAAa,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAEnD,qBAAG;AAAA,oBACD;AAAA,oBACA;AAAA,oBACA,SAAS,OAAO;AAAA,sBACd,GAAG,KAAK;AAAA,sBACR,GAAG;AAAA,oBACL,CAAC;AAAA,kBACH;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,UAAU;AACZ,YAAI,YAAY,QAAW;AACzB,aAAG,cAAc,SAAS,QAAW;AAAA,YACnC,GAAG,SAAS;AAAA,YACZ,GAAG;AAAA,UACL,CAAC;AAAA,QACH;AAEA,YAAI,YAAY,SAAS,MAAM,QAAQ;AACrC,mBAAS,MAAM,QAAQ,CAAC,SAAe;AACrC,gBAAI,aAAa,KAAK,MAAM;AAC1B,iBAAG;AAAA,gBACD;AAAA,gBACA;AAAA,gBACA,SAAS,OAAO;AAAA,kBACd,GAAG,KAAK;AAAA,kBACR,GAAG;AAAA,gBACL,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AC3IF,SAAS,UAAU,sBAAsB;AAoBlC,IAAM,SACX,CAAC,YAAY,aAAa,CAAC,MAC3B,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,SAAO,eAAe,MAAM,UAAU,EAAE,OAAO,QAAQ;AACzD;;;ACzBF,SAAS,cAAc,0BAA0B;AAmB1C,IAAM,aACX,CAAC,YAAY,aAAa,CAAC,MAC3B,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,SAAO,mBAAmB,MAAM,UAAU,EAAE,OAAO,QAAQ;AAC7D;;;ACrBK,IAAM,WAAW,UAAU,OAAO;AAAA,EACvC,MAAM;AAAA,EAEN,cAAc;AACZ,WAAO;AAAA,MACL,GAAG;AAAA,IACL;AAAA,EACF;AACF,CAAC;;;ACbD,SAAS,sBAAsB;AAQxB,IAAM,SAAS,UAAU,OAAO;AAAA,EACrC,MAAM;AAAA,EAEN,SAAS,EAAE,aAAa,qBAAqB,GAAG;AAXlD;AAYI,UAAM,WAAW,MAAM;AAZ3B,UAAAC,KAAAC,KAAAC,KAAA;AAaM,WACE,MAAAA,OAAAD,OAAAD,MAAA,KAAK,OAAO,QAAQ,yBAApB,gBAAAA,IAA0C,WAA1C,gBAAAC,IAAkD,sBAAlD,gBAAAC,IAAA,KAAAD,KAAsE,iBAAtE,YACA,YAAY,QAAQ,SAAS,GAC7B;AACA;AAAA,MACF;AACA,YAAM,kBAAkB,wBAAwB,YAAY,QAAQ,CAAC,aAAa,GAAG,oBAAoB,CAAC;AAC1G,YAAM,UAAU,iBAAiB,eAAe;AAEhD,cAAQ,QAAQ,YAAU;AACxB,YACE,gBAAgB,QAAQ,UAAU,OAAO,SAAS,IAAI,EAAE,gBACxD,gBAAgB,QAAQ,UAAU,OAAO,SAAS,EAAE,EAAE,eACtD;AACA,0BAAgB,OAAO,aAAa,OAAO,SAAS,MAAM,OAAO,SAAS,IAAI,CAAC,MAAM,SAAS;AAC5F,kBAAM,KAAK,OAAO,KAAK,WAAW;AAClC,kBAAM,qBAAqB,OAAO,SAAS,QAAQ,QAAQ,MAAM,OAAO,SAAS;AAEjF,iBAAK,OAAO,KAAK,UAAU;AAAA,cACzB,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,cACA,SAAS,gBAAgB,QAAQ,IAAI,IAAI;AAAA,cACzC,OAAO,gBAAgB,QAAQ,IAAI,EAAE;AAAA,cACrC,cAAc,OAAO;AAAA,cACrB,UAAU,OAAO;AAAA,cACjB,SAAS,CAAC;AAAA,cACV,QAAQ,KAAK;AAAA,cACb;AAAA,cACA,mBAAmB;AAAA,YACrB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,YAAM,UAAU,gBAAgB;AAChC,sBAAgB,MAAM,QAAQ,CAAC,MAAM,UAAU;AAlDrD,YAAAD,KAAAC;AAmDQ,YAAI,gBAAgB,gBAAgB;AAClC,gBAAM,WAAW,QAAQ,MAAM,KAAK,EAAE,IAAI,KAAK,MAAM,EAAE;AACvD,gBAAM,SAAS,QAAQ,MAAM,KAAK,EAAE,IAAI,KAAK,EAAE;AAC/C,gBAAM,WAAW,QAAQ,OAAO,EAAE,IAAI,UAAU,EAAE;AAClD,gBAAM,SAAS,QAAQ,OAAO,EAAE,IAAI,MAAM;AAE1C,gBAAM,mBAAkBD,MAAA,gBAAgB,IAAI,OAAO,WAAW,CAAC,MAAvC,gBAAAA,IAA0C,MAAM,KAAK,UAAQ,KAAK,GAAG,KAAK,IAAI;AACtG,gBAAM,kBAAiBC,MAAA,gBAAgB,IAAI,OAAO,MAAM,MAAjC,gBAAAA,IAAoC,MAAM,KAAK,UAAQ,KAAK,GAAG,KAAK,IAAI;AAE/F,eAAK,OAAO,KAAK,UAAU;AAAA,YACzB,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,YACX,MAAM,KAAK;AAAA,YACX,IAAI,KAAK;AAAA,YACT,cAAc;AAAA,cACZ,MAAM;AAAA,cACN,IAAI;AAAA,YACN;AAAA,YACA,UAAU;AAAA,cACR,MAAM;AAAA,cACN,IAAI;AAAA,YACN;AAAA,YACA,SAAS,QAAQ,kBAAkB,eAAe;AAAA,YAClD,QAAQ,KAAK;AAAA,YACb;AAAA,YACA,mBAAmB;AAAA,UACrB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAI,sBAAK,OAAO,QAAQ,yBAApB,mBAA0C,WAA1C,mBAAkD,UAAlD,YAA2D,MAAM;AACnE,iBAAW,UAAU,CAAC;AAAA,IACxB,OAAO;AACL,eAAS;AAAA,IACX;AAAA,EACF;AACF,CAAC;;;ACxFD,SAAS,UAAAE,SAAQ,aAAAC,kBAAiB;AAI3B,IAAM,OAAO,UAAU,OAAO;AAAA,EACnC,MAAM;AAAA,EAEN,wBAAwB;AACtB,WAAO;AAAA,MACL,IAAIC,QAAO;AAAA,QACT,KAAK,IAAIC,WAAU,YAAY;AAAA,QAE/B,OAAO;AAAA,UACL,YAAY,CAAC,GAAG,GAAG,OAAO,UAAU;AAClC,iBAAK,OAAO,KAAK,QAAQ;AAAA,cACvB,QAAQ,KAAK;AAAA,cACb,OAAO;AAAA,cACP;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACzBD,SAAS,UAAAC,SAAQ,aAAAC,kBAAiB;AAI3B,IAAM,WAAW,UAAU,OAAO;AAAA,EACvC,MAAM;AAAA,EAEN,wBAAwB;AACtB,WAAO;AAAA,MACL,IAAIC,QAAO;AAAA,QACT,KAAK,IAAIC,WAAU,UAAU;AAAA,QAC7B,OAAO;AAAA,UACL,UAAU,MAAM,KAAK,OAAO,QAAQ;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACjBD,SAAS,UAAAC,SAAQ,aAAAC,kBAAiB;AAI3B,IAAM,uBAAuB,IAAIC,WAAU,aAAa;AAExD,IAAM,cAAc,UAAU,OAAO;AAAA,EAC1C,MAAM;AAAA,EAEN,wBAAwB;AACtB,UAAM,EAAE,OAAO,IAAI;AAEnB,WAAO;AAAA,MACL,IAAIC,QAAO;AAAA,QACT,KAAK;AAAA,QACL,OAAO;AAAA,UACL,iBAAiB;AAAA,YACf,OAAO,CAAC,MAAM,UAAiB;AAC7B,qBAAO,YAAY;AAEnB,oBAAM,cAAc,OAAO,MAAM,GAAG,QAAQ,SAAS,EAAE,MAAM,CAAC,EAAE,QAAQ,gBAAgB,KAAK;AAE7F,mBAAK,SAAS,WAAW;AAEzB,qBAAO;AAAA,YACT;AAAA,YACA,MAAM,CAAC,MAAM,UAAiB;AAC5B,qBAAO,YAAY;AAEnB,oBAAM,cAAc,OAAO,MAAM,GAAG,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,QAAQ,gBAAgB,KAAK;AAE5F,mBAAK,SAAS,WAAW;AAEzB,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACxCD,SAAS,UAAAC,SAAQ,aAAAC,YAAW,aAAAC,kBAAiB;AAStC,IAAM,SAAS,UAAU,OAAO;AAAA,EACrC,MAAM;AAAA,EAEN,uBAAuB;AACrB,UAAM,kBAAkB,MACtB,KAAK,OAAO,SAAS,MAAM,CAAC,EAAE,SAAS,MAAM;AAAA,MAC3C,MAAM,SAAS,cAAc;AAAA;AAAA,MAG7B,MACE,SAAS,QAAQ,CAAC,EAAE,GAAG,MAAM;AAC3B,cAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,cAAM,EAAE,OAAO,QAAQ,IAAI;AAC3B,cAAM,EAAE,KAAK,OAAO,IAAI;AACxB,cAAM,aAAa,QAAQ,OAAO,eAAe,MAAM,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,IAAI;AACrF,cAAM,oBAAoB,WAAW,OAAO,KAAK,KAAK;AAEtD,cAAM,YAAY,QAAQ,MAAM,QAAQ;AAExC,cAAM,YACJ,qBAAqB,WAAW,OAAO,eAAe,IAClD,cAAc,QAAQ,MACtBC,WAAU,QAAQ,GAAG,EAAE,SAAS;AAEtC,YACE,CAAC,SACD,CAAC,OAAO,KAAK,eACb,OAAO,YAAY,UACnB,CAAC,aACA,aAAa,QAAQ,OAAO,KAAK,SAAS,aAC3C;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,SAAS,WAAW;AAAA,MAC7B,CAAC;AAAA,MAEH,MAAM,SAAS,gBAAgB;AAAA,MAC/B,MAAM,SAAS,aAAa;AAAA,MAC5B,MAAM,SAAS,mBAAmB;AAAA,IACpC,CAAC;AAEH,UAAM,eAAe,MACnB,KAAK,OAAO,SAAS,MAAM,CAAC,EAAE,SAAS,MAAM;AAAA,MAC3C,MAAM,SAAS,gBAAgB;AAAA,MAC/B,MAAM,SAAS,kBAAkB;AAAA,MACjC,MAAM,SAAS,YAAY;AAAA,MAC3B,MAAM,SAAS,kBAAkB;AAAA,IACnC,CAAC;AAEH,UAAM,cAAc,MAClB,KAAK,OAAO,SAAS,MAAM,CAAC,EAAE,SAAS,MAAM;AAAA,MAC3C,MAAM,SAAS,cAAc;AAAA,MAC7B,MAAM,SAAS,oBAAoB;AAAA,MACnC,MAAM,SAAS,eAAe;AAAA,MAC9B,MAAM,SAAS,WAAW;AAAA,IAC5B,CAAC;AAEH,UAAM,aAAa;AAAA,MACjB,OAAO;AAAA,MACP,aAAa,MAAM,KAAK,OAAO,SAAS,SAAS;AAAA,MACjD,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,mBAAmB;AAAA,MACnB,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,SAAS,MAAM,KAAK,OAAO,SAAS,UAAU;AAAA,IAChD;AAEA,UAAM,WAAW;AAAA,MACf,GAAG;AAAA,IACL;AAEA,UAAM,YAAY;AAAA,MAChB,GAAG;AAAA,MACH,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,cAAc;AAAA,MACd,SAAS;AAAA,MACT,UAAU,MAAM,KAAK,OAAO,SAAS,qBAAqB;AAAA,MAC1D,UAAU,MAAM,KAAK,OAAO,SAAS,mBAAmB;AAAA,IAC1D;AAEA,QAAI,MAAM,KAAK,QAAQ,GAAG;AACxB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,wBAAwB;AACtB,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAML,IAAIC,QAAO;AAAA,QACT,KAAK,IAAIC,WAAU,eAAe;AAAA,QAClC,mBAAmB,CAAC,cAAc,UAAU,aAAa;AACvD,cAAI,aAAa,KAAK,CAAAC,QAAMA,IAAG,QAAQ,aAAa,CAAC,GAAG;AACtD;AAAA,UACF;AAEA,gBAAM,aAAa,aAAa,KAAK,iBAAe,YAAY,UAAU,KAAK,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG;AAE5G,gBAAM,WAAW,aAAa,KAAK,iBAAe,YAAY,QAAQ,sBAAsB,CAAC;AAE7F,cAAI,CAAC,cAAc,UAAU;AAC3B;AAAA,UACF;AAEA,gBAAM,EAAE,OAAO,MAAM,GAAG,IAAI,SAAS;AACrC,gBAAM,UAAUH,WAAU,QAAQ,SAAS,GAAG,EAAE;AAChD,gBAAM,SAASA,WAAU,MAAM,SAAS,GAAG,EAAE;AAC7C,gBAAM,iBAAiB,SAAS,WAAW,OAAO;AAElD,cAAI,SAAS,CAAC,gBAAgB;AAC5B;AAAA,UACF;AAEA,gBAAM,UAAU,YAAY,SAAS,GAAG;AAExC,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AAEA,gBAAM,KAAK,SAAS;AACpB,gBAAM,QAAQ,qBAAqB;AAAA,YACjC,OAAO;AAAA,YACP,aAAa;AAAA,UACf,CAAC;AACD,gBAAM,EAAE,SAAS,IAAI,IAAI,eAAe;AAAA,YACtC,QAAQ,KAAK;AAAA,YACb;AAAA,UACF,CAAC;AAED,mBAAS,WAAW;AAEpB,cAAI,CAAC,GAAG,MAAM,QAAQ;AACpB;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AC/JD,SAAS,UAAAI,SAAQ,aAAAC,kBAAiB;AAI3B,IAAM,QAAQ,UAAU,OAAO;AAAA,EACpC,MAAM;AAAA,EAEN,wBAAwB;AACtB,WAAO;AAAA,MACL,IAAIC,QAAO;AAAA,QACT,KAAK,IAAIC,WAAU,aAAa;AAAA,QAEhC,OAAO;AAAA,UACL,aAAa,CAAC,OAAO,GAAG,UAAU;AAChC,iBAAK,OAAO,KAAK,SAAS;AAAA,cACxB,QAAQ,KAAK;AAAA,cACb,OAAO;AAAA,cACP;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACxBD,SAAS,UAAAC,SAAQ,aAAAC,kBAAiB;AAI3B,IAAM,WAAW,UAAU,OAAO;AAAA,EACvC,MAAM;AAAA,EAEN,wBAAwB;AACtB,WAAO;AAAA,MACL,IAAIC,QAAO;AAAA,QACT,KAAK,IAAIC,WAAU,UAAU;AAAA,QAC7B,OAAO;AAAA,UACL,YAAY,MAAmC,KAAK,OAAO,aAAa,EAAE,UAAU,IAAI,IAAI,CAAC;AAAA,QAC/F;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACZM,IAAM,UAAN,MAAM,SAAQ;AAAA,EAWnB,YAAY,KAAkB,QAAgB,UAAU,OAAO,OAAoB,MAAM;AAOzF,SAAQ,cAA2B;AAUnC,SAAO,cAA6B;AAhBlC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AAAA,EATA,IAAY,OAAe;AACzB,WAAO,KAAK,KAAK,KAAK;AAAA,EACxB;AAAA,EAWA,IAAI,OAAa;AACf,WAAO,KAAK,eAAe,KAAK,YAAY,KAAK;AAAA,EACnD;AAAA,EAEA,IAAI,UAAuB;AACzB,WAAO,KAAK,OAAO,KAAK,SAAS,KAAK,GAAG,EAAE;AAAA,EAC7C;AAAA,EAIA,IAAI,QAAgB;AAnCtB;AAoCI,YAAO,UAAK,gBAAL,YAAoB,KAAK,YAAY;AAAA,EAC9C;AAAA,EAEA,IAAI,MAAc;AAChB,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EAEA,IAAI,UAAoB;AACtB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEA,IAAI,QAAQ,SAAkB;AAC5B,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,KAAK;AAEd,QAAI,KAAK,SAAS;AAChB,UAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,gBAAQ,MAAM,uEAAkE,KAAK,IAAI,OAAO,KAAK,GAAG,EAAE;AAC1G;AAAA,MACF;AAEA,aAAO,KAAK,OAAO;AACnB,WAAK,KAAK,KAAK;AAAA,IACjB;AAEA,SAAK,OAAO,SAAS,gBAAgB,EAAE,MAAM,GAAG,GAAG,OAAO;AAAA,EAC5D;AAAA,EAEA,IAAI,aAAqC;AACvC,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEA,IAAI,cAAsB;AACxB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEA,IAAI,OAAe;AACjB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEA,IAAI,OAAe;AACjB,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK;AAAA,IACd;AAEA,WAAO,KAAK,YAAY,MAAM,KAAK,YAAY,KAAK;AAAA,EACtD;AAAA,EAEA,IAAI,QAAe;AACjB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,IAAI,KAAK;AAAA,IACX;AAAA,EACF;AAAA,EAEA,IAAI,KAAa;AACf,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,MAAM,KAAK;AAAA,IACzB;AAEA,WAAO,KAAK,YAAY,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,KAAK,SAAS,IAAI;AAAA,EAChF;AAAA,EAEA,IAAI,SAAyB;AAC3B,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,KAAK,YAAY,MAAM,KAAK,YAAY,QAAQ,CAAC;AACnE,UAAM,OAAO,KAAK,YAAY,IAAI,QAAQ,SAAS;AAEnD,WAAO,IAAI,SAAQ,MAAM,KAAK,MAAM;AAAA,EACtC;AAAA,EAEA,IAAI,SAAyB;AAC3B,QAAI,OAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,QAAQ,KAAK,UAAU,IAAI,EAAE;AAE1E,QAAI,KAAK,UAAU,KAAK,OAAO;AAC7B,aAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,OAAO,CAAC;AAAA,IACnD;AAEA,WAAO,IAAI,SAAQ,MAAM,KAAK,MAAM;AAAA,EACtC;AAAA,EAEA,IAAI,QAAwB;AAC1B,QAAI,OAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,MAAM,KAAK,UAAU,IAAI,EAAE;AAExE,QAAI,KAAK,UAAU,KAAK,OAAO;AAC7B,aAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,KAAK,CAAC;AAAA,IACjD;AAEA,WAAO,IAAI,SAAQ,MAAM,KAAK,MAAM;AAAA,EACtC;AAAA,EAEA,IAAI,WAAsB;AACxB,UAAM,WAAsB,CAAC;AAE7B,SAAK,KAAK,QAAQ,QAAQ,CAAC,MAAM,WAAW;AAC1C,YAAM,UAAU,KAAK,WAAW,CAAC,KAAK;AACtC,YAAM,gBAAgB,KAAK,UAAU,CAAC,KAAK;AAE3C,YAAM,YAAY,KAAK,MAAM,UAAU,gBAAgB,IAAI;AAG3D,UAAI,YAAY,KAAK,YAAY,KAAK,YAAY,IAAI,WAAW,GAAG;AAClE;AAAA,MACF;AAEA,YAAM,OAAO,KAAK,YAAY,IAAI,QAAQ,SAAS;AAEnD,UAAI,CAAC,WAAW,KAAK,SAAS,KAAK,OAAO;AACxC;AAAA,MACF;AAEA,YAAM,eAAe,IAAI,SAAQ,MAAM,KAAK,QAAQ,SAAS,UAAU,OAAO,IAAI;AAElF,UAAI,SAAS;AACX,qBAAa,cAAc,KAAK,QAAQ;AAAA,MAC1C;AAEA,eAAS,KAAK,IAAI,SAAQ,MAAM,KAAK,QAAQ,SAAS,UAAU,OAAO,IAAI,CAAC;AAAA,IAC9E,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,aAA6B;AAC/B,WAAO,KAAK,SAAS,CAAC,KAAK;AAAA,EAC7B;AAAA,EAEA,IAAI,YAA4B;AAC9B,UAAM,WAAW,KAAK;AAEtB,WAAO,SAAS,SAAS,SAAS,CAAC,KAAK;AAAA,EAC1C;AAAA,EAEA,QAAQ,UAAkB,aAAqC,CAAC,GAAmB;AACjF,QAAI,OAAuB;AAC3B,QAAI,cAAc,KAAK;AAEvB,WAAO,eAAe,CAAC,MAAM;AAC3B,UAAI,YAAY,KAAK,KAAK,SAAS,UAAU;AAC3C,YAAI,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AACtC,gBAAM,iBAAiB,YAAY,KAAK;AACxC,gBAAM,WAAW,OAAO,KAAK,UAAU;AAEvC,mBAAS,QAAQ,GAAG,QAAQ,SAAS,QAAQ,SAAS,GAAG;AACvD,kBAAM,MAAM,SAAS,KAAK;AAE1B,gBAAI,eAAe,GAAG,MAAM,WAAW,GAAG,GAAG;AAC3C;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,oBAAc,YAAY;AAAA,IAC5B;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,cAAc,UAAkB,aAAqC,CAAC,GAAmB;AACvF,WAAO,KAAK,iBAAiB,UAAU,YAAY,IAAI,EAAE,CAAC,KAAK;AAAA,EACjE;AAAA,EAEA,iBAAiB,UAAkB,aAAqC,CAAC,GAAG,gBAAgB,OAAkB;AAC5G,QAAI,QAAmB,CAAC;AAExB,QAAI,CAAC,KAAK,YAAY,KAAK,SAAS,WAAW,GAAG;AAChD,aAAO;AAAA,IACT;AACA,UAAM,WAAW,OAAO,KAAK,UAAU;AAMvC,SAAK,SAAS,QAAQ,cAAY;AAEhC,UAAI,iBAAiB,MAAM,SAAS,GAAG;AACrC;AAAA,MACF;AAEA,UAAI,SAAS,KAAK,KAAK,SAAS,UAAU;AACxC,cAAM,yBAAyB,SAAS,MAAM,SAAO,WAAW,GAAG,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC;AAEjG,YAAI,wBAAwB;AAC1B,gBAAM,KAAK,QAAQ;AAAA,QACrB;AAAA,MACF;AAGA,UAAI,iBAAiB,MAAM,SAAS,GAAG;AACrC;AAAA,MACF;AAEA,cAAQ,MAAM,OAAO,SAAS,iBAAiB,UAAU,YAAY,aAAa,CAAC;AAAA,IACrF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,aAAa,YAAoC;AAC/C,UAAM,EAAE,GAAG,IAAI,KAAK,OAAO;AAE3B,OAAG,cAAc,KAAK,MAAM,QAAW;AAAA,MACrC,GAAG,KAAK,KAAK;AAAA,MACb,GAAG;AAAA,IACL,CAAC;AAED,SAAK,OAAO,KAAK,SAAS,EAAE;AAAA,EAC9B;AACF;;;AC3PO,IAAM,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAd,SAAS,eAAeC,QAAe,OAAgB,QAAmC;AAC/F,QAAM,iBACJ,SAAS,cAAc,0BAA0B,SAAS,IAAI,MAAM,KAAK,EAAE,GAAG;AAGhF,MAAI,mBAAmB,MAAM;AAC3B,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,SAAS,cAAc,OAAO;AAEhD,MAAI,OAAO;AACT,cAAU,aAAa,SAAS,KAAK;AAAA,EACvC;AAEA,YAAU,aAAa,oBAAoB,SAAS,IAAI,MAAM,KAAK,EAAE,IAAI,EAAE;AAC3E,YAAU,YAAYA;AACtB,WAAS,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAY,SAAS;AAE9D,SAAO;AACT;;;AnJgCO,IAAM,SAAN,cAAqB,aAA2B;AAAA,EA6DrD,YAAY,UAAkC,CAAC,GAAG;AAChD,UAAM;AAzDR,SAAQ,MAA+B;AAIvC,SAAQ,aAAgC;AAExC,SAAO,YAAY;AAOnB;AAAA;AAAA;AAAA,SAAO,gBAAgB;AAEvB,SAAO,mBAA4B,CAAC;AAKpC;AAAA;AAAA;AAAA,SAAO,aAAa,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC;AAEzD,SAAO,UAAyB;AAAA,MAC9B,SAAS,OAAO,aAAa,cAAc,SAAS,cAAc,KAAK,IAAI;AAAA,MAC3E,SAAS;AAAA,MACT,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY,CAAC;AAAA,MACb,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa,CAAC;AAAA,MACd,cAAc,CAAC;AAAA,MACf,sBAAsB,CAAC;AAAA,MACvB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB,MAAM;AAAA,MACtB,UAAU,MAAM;AAAA,MAChB,SAAS,MAAM;AAAA,MACf,WAAW,MAAM;AAAA,MACjB,UAAU,MAAM;AAAA,MAChB,mBAAmB,MAAM;AAAA,MACzB,eAAe,MAAM;AAAA,MACrB,SAAS,MAAM;AAAA,MACf,QAAQ,MAAM;AAAA,MACd,WAAW,MAAM;AAAA,MACjB,gBAAgB,CAAC,EAAE,MAAM,MAAM;AAC7B,cAAM;AAAA,MACR;AAAA,MACA,SAAS,MAAM;AAAA,MACf,QAAQ,MAAM;AAAA,MACd,UAAU,MAAM;AAAA,IAClB;AAybA,SAAO,yBAAyB;AAEhC,SAAQ,sBAA0C;AAvbhD,SAAK,WAAW,OAAO;AACvB,SAAK,uBAAuB;AAC5B,SAAK,qBAAqB;AAC1B,SAAK,aAAa;AAClB,SAAK,GAAG,gBAAgB,KAAK,QAAQ,cAAc;AACnD,SAAK,KAAK,gBAAgB,EAAE,QAAQ,KAAK,CAAC;AAC1C,SAAK,GAAG,SAAS,KAAK,QAAQ,OAAO;AACrC,SAAK,GAAG,WAAW,KAAK,QAAQ,SAAS;AACzC,SAAK,GAAG,gBAAgB,KAAK,QAAQ,cAAc;AACnD,SAAK,GAAG,UAAU,KAAK,QAAQ,QAAQ;AACvC,SAAK,GAAG,UAAU,KAAK,QAAQ,QAAQ;AACvC,SAAK,GAAG,mBAAmB,KAAK,QAAQ,iBAAiB;AACzD,SAAK,GAAG,eAAe,KAAK,QAAQ,aAAa;AACjD,SAAK,GAAG,SAAS,KAAK,QAAQ,OAAO;AACrC,SAAK,GAAG,QAAQ,KAAK,QAAQ,MAAM;AACnC,SAAK,GAAG,WAAW,KAAK,QAAQ,SAAS;AACzC,SAAK,GAAG,QAAQ,CAAC,EAAE,OAAO,OAAO,MAAM,MAAM,KAAK,QAAQ,OAAO,OAAO,OAAO,KAAK,CAAC;AACrF,SAAK,GAAG,SAAS,CAAC,EAAE,OAAO,MAAM,MAAM,KAAK,QAAQ,QAAQ,OAAO,KAAK,CAAC;AACzE,SAAK,GAAG,UAAU,KAAK,QAAQ,QAAQ;AAEvC,UAAM,aAAa,KAAK,UAAU;AAClC,UAAM,YAAY,qBAAqB,YAAY,KAAK,QAAQ,SAAS;AAGzE,SAAK,cAAc,YAAY,OAAO;AAAA,MACpC,KAAK;AAAA,MACL,QAAQ,KAAK;AAAA,MACb,WAAW,aAAa;AAAA,IAC1B,CAAC;AAED,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,MAAM,KAAK,QAAQ,OAAO;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKO,MAAM,IAAgD;AAC3D,QAAI,OAAO,aAAa,aAAa;AACnC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,SAAK,WAAW,EAAE;AAClB,SAAK,KAAK,SAAS,EAAE,QAAQ,KAAK,CAAC;AAEnC,WAAO,WAAW,MAAM;AACtB,UAAI,KAAK,aAAa;AACpB;AAAA,MACF;AAEA,WAAK,SAAS,MAAM,KAAK,QAAQ,SAAS;AAC1C,WAAK,KAAK,UAAU,EAAE,QAAQ,KAAK,CAAC;AACpC,WAAK,gBAAgB;AAAA,IACvB,GAAG,CAAC;AAAA,EACN;AAAA;AAAA;AAAA;AAAA,EAKO,UAAU;AACf,QAAI,KAAK,YAAY;AAGnB,YAAM,MAAM,KAAK,WAAW;AAE5B,UAAI,2BAAK,QAAQ;AACf,eAAO,IAAI;AAAA,MACb;AACA,WAAK,WAAW,QAAQ;AAAA,IAC1B;AACA,SAAK,aAAa;AAClB,SAAK,gBAAgB;AAGrB,QAAI,KAAK,KAAK;AACZ,UAAI;AACF,YAAI,OAAO,KAAK,IAAI,WAAW,YAAY;AACzC,eAAK,IAAI,OAAO;AAAA,QAClB,WAAW,KAAK,IAAI,YAAY;AAC9B,eAAK,IAAI,WAAW,YAAY,KAAK,GAAG;AAAA,QAC1C;AAAA,MACF,SAAS,OAAO;AAEd,gBAAQ,KAAK,iCAAiC,KAAK;AAAA,MACrD;AAAA,IACF;AACA,SAAK,MAAM;AACX,SAAK,KAAK,WAAW,EAAE,QAAQ,KAAK,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,UAAmB;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,WAA2B;AACpC,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKO,QAAyB;AAC9B,WAAO,KAAK,eAAe,MAAM;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKO,MAAmB;AACxB,WAAO,KAAK,eAAe,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKQ,YAAkB;AACxB,QAAI,KAAK,QAAQ,aAAa,OAAO,aAAa,aAAa;AAC7D,WAAK,MAAM,eAAe,OAAO,KAAK,QAAQ,WAAW;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,UAAkC,CAAC,GAAS;AAC5D,SAAK,UAAU;AAAA,MACb,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACL;AAEA,QAAI,CAAC,KAAK,cAAc,CAAC,KAAK,SAAS,KAAK,aAAa;AACvD;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,KAAK,SAAS,KAAK,QAAQ,WAAW;AAAA,IAC7C;AAEA,SAAK,KAAK,YAAY,KAAK,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKO,YAAY,UAAmB,aAAa,MAAY;AAC7D,SAAK,WAAW,EAAE,SAAS,CAAC;AAE5B,QAAI,YAAY;AACd,WAAK,KAAK,UAAU,EAAE,QAAQ,MAAM,aAAa,KAAK,MAAM,IAAI,sBAAsB,CAAC,EAAE,CAAC;AAAA,IAC5F;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,aAAsB;AAI/B,WAAO,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK,KAAK;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,OAAmB;AAC5B,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK;AAAA,IACd;AAEA,WAAO,IAAI;AAAA,MACT;AAAA,QACE,OAAO,KAAK;AAAA,QACZ,aAAa,CAAC,UAA8D;AAC1E,eAAK,cAAc;AAAA,QACrB;AAAA,QACA,UAAU,CAAC,OAAwD;AACjE,eAAK,cAAc,KAAK,MAAM,MAAM,EAAE;AAAA,QACxC;AAAA;AAAA,QAGA,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,KAAK,CAAC,KAAK,QAAQ;AAEjB,cAAI,QAAQ,SAAS;AACnB,mBAAO,KAAK;AAAA,UACd;AACA,cAAI,OAAO,KAAK;AACd,mBAAO,QAAQ,IAAI,KAAK,GAAG;AAAA,UAC7B;AAGA,gBAAM,IAAI;AAAA,YACR,yEAAyE,GAAa;AAAA,UACxF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,QAAqB;AAC9B,QAAI,KAAK,YAAY;AACnB,WAAK,cAAc,KAAK,KAAK;AAAA,IAC/B;AAEA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,eACL,QACA,eACa;AACb,UAAM,UAAU,WAAW,aAAa,IACpC,cAAc,QAAQ,CAAC,GAAG,KAAK,MAAM,OAAO,CAAC,IAC7C,CAAC,GAAG,KAAK,MAAM,SAAS,MAAM;AAElC,UAAM,QAAQ,KAAK,MAAM,YAAY,EAAE,QAAQ,CAAC;AAEhD,SAAK,KAAK,YAAY,KAAK;AAE3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,iBACL,yBACyB;AACzB,QAAI,KAAK,aAAa;AACpB,aAAO;AAAA,IACT;AAEA,UAAM,cAAc,KAAK,MAAM;AAC/B,QAAI,UAAU;AAEb,IAAC,CAAC,EAA6B,OAAO,uBAAuB,EAAE,QAAQ,qBAAmB;AAEzF,YAAM,OAAO,OAAO,oBAAoB,WAAW,GAAG,eAAe,MAAM,gBAAgB;AAG3F,gBAAU,QAAQ,OAAO,YAAU,CAAC,OAAO,IAAI,WAAW,IAAI,CAAC;AAAA,IACjE,CAAC;AAED,QAAI,YAAY,WAAW,QAAQ,QAAQ;AAEzC,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,KAAK,MAAM,YAAY;AAAA,MACnC;AAAA,IACF,CAAC;AAED,SAAK,KAAK,YAAY,KAAK;AAE3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,yBAA+B;AApZzC;AAqZI,UAAM,iBAAiB,KAAK,QAAQ,uBAChC;AAAA,MACE;AAAA,MACA,wBAAwB,UAAU;AAAA,QAChC,iBAAgB,gBAAK,QAAQ,yBAAb,mBAAmC,4BAAnC,mBAA4D;AAAA,MAC9E,CAAC;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,OAAO,SAAO;AACd,UAAI,OAAO,KAAK,QAAQ,yBAAyB,UAAU;AACzD,eACE,KAAK,QAAQ,qBAAqB,IAAI,IAAsD,MAAM;AAAA,MAEtG;AACA,aAAO;AAAA,IACT,CAAC,IACD,CAAC;AACL,UAAM,gBAAgB,CAAC,GAAG,gBAAgB,GAAG,KAAK,QAAQ,UAAU,EAAE,OAAO,eAAa;AACxF,aAAO,CAAC,aAAa,QAAQ,MAAM,EAAE,SAAS,uCAAW,IAAI;AAAA,IAC/D,CAAC;AAED,SAAK,mBAAmB,IAAI,iBAAiB,eAAe,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAKQ,uBAA6B;AACnC,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACvC,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKQ,eAAqB;AAC3B,SAAK,SAAS,KAAK,iBAAiB;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKQ,YAA6B;AACnC,QAAI;AAEJ,QAAI;AACF,YAAM,eAAe,KAAK,QAAQ,SAAS,KAAK,QAAQ,KAAK,QAAQ,cAAc;AAAA,QACjF,uBAAuB,KAAK,QAAQ;AAAA,MACtC,CAAC;AAAA,IACH,SAAS,GAAG;AACV,UACE,EAAE,aAAa,UACf,CAAC,CAAC,wCAAwC,sCAAsC,EAAE,SAAS,EAAE,OAAO,GACpG;AAEA,cAAM;AAAA,MACR;AACA,WAAK,KAAK,gBAAgB;AAAA,QACxB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,sBAAsB,MAAM;AAC1B,cACE,mBAAmB,KAAK,WACxB,OAAO,KAAK,QAAQ,kBAAkB,YACtC,KAAK,QAAQ,eACb;AACA;AAAC,YAAC,KAAK,QAAQ,cAAsB,aAAa;AAAA,UACpD;AAEA,eAAK,QAAQ,aAAa,KAAK,QAAQ,WAAW,OAAO,eAAa,UAAU,SAAS,eAAe;AAGxG,eAAK,uBAAuB;AAAA,QAC9B;AAAA,MACF,CAAC;AAGD,YAAM,eAAe,KAAK,QAAQ,SAAS,KAAK,QAAQ,KAAK,QAAQ,cAAc;AAAA,QACjF,uBAAuB;AAAA,MACzB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,WAAW,SAA2D;AAlfhF;AAmfI,SAAK,aAAa,IAAI,WAAW,SAAS;AAAA,MACxC,GAAG,KAAK,QAAQ;AAAA,MAChB,YAAY;AAAA;AAAA,QAEV,MAAM;AAAA,QACN,IAAG,UAAK,QAAQ,gBAAb,mBAA0B;AAAA,MAC/B;AAAA,MACA,qBAAqB,KAAK,oBAAoB,KAAK,IAAI;AAAA,MACvD,OAAO,KAAK;AAAA,MACZ,WAAW,KAAK,iBAAiB;AAAA,MACjC,WAAW,KAAK,iBAAiB;AAAA,IACnC,CAAC;AAID,UAAM,WAAW,KAAK,MAAM,YAAY;AAAA,MACtC,SAAS,KAAK,iBAAiB;AAAA,IACjC,CAAC;AAED,SAAK,KAAK,YAAY,QAAQ;AAE9B,SAAK,aAAa;AAClB,SAAK,UAAU;AAKf,UAAM,MAAM,KAAK,KAAK;AAEtB,QAAI,SAAS;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKO,kBAAwB;AAC7B,QAAI,KAAK,KAAK,aAAa;AACzB;AAAA,IACF;AAEA,SAAK,KAAK,SAAS;AAAA,MACjB,WAAW,KAAK,iBAAiB;AAAA,MACjC,WAAW,KAAK,iBAAiB;AAAA,IACnC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKO,eAAqB;AAC1B,SAAK,KAAK,IAAI,YAAY,UAAU,KAAK,KAAK,IAAI,SAAS;AAAA,EAC7D;AAAA,EAMO,mBAAmB,IAAgB;AACxC,SAAK,yBAAyB;AAC9B,OAAG;AACH,SAAK,yBAAyB;AAE9B,UAAM,KAAK,KAAK;AAEhB,SAAK,sBAAsB;AAE3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,oBAAoB,aAAgC;AAG1D,QAAI,KAAK,KAAK,aAAa;AACzB;AAAA,IACF;AAEA,QAAI,KAAK,wBAAwB;AAC/B,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,sBAAsB;AAE3B;AAAA,MACF;AAEA,kBAAY,MAAM,QAAQ,UAAK;AA3kBrC;AA2kBwC,0BAAK,wBAAL,mBAA0B,KAAK;AAAA,OAAK;AAEtE;AAAA,IACF;AAGA,UAAM,EAAE,OAAO,aAAa,IAAI,KAAK,MAAM,iBAAiB,WAAW;AACvE,UAAM,sBAAsB,CAAC,KAAK,MAAM,UAAU,GAAG,MAAM,SAAS;AACpE,UAAM,mBAAmB,aAAa,SAAS,WAAW;AAC1D,UAAM,YAAY,KAAK;AAEvB,SAAK,KAAK,qBAAqB;AAAA,MAC7B,QAAQ;AAAA,MACR;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAGD,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AAEA,SAAK,KAAK,YAAY,KAAK;AAG3B,SAAK,KAAK,eAAe;AAAA,MACvB,QAAQ;AAAA,MACR;AAAA,MACA,sBAAsB,aAAa,MAAM,CAAC;AAAA,IAC5C,CAAC;AAED,QAAI,qBAAqB;AACvB,WAAK,KAAK,mBAAmB;AAAA,QAC3B,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,oBAAoB,aAAa,SAAS,QAAM,GAAG,QAAQ,OAAO,KAAK,GAAG,QAAQ,MAAM,CAAC;AAC/F,UAAMC,SAAQ,uDAAmB,QAAQ;AACzC,UAAMC,QAAO,uDAAmB,QAAQ;AAExC,QAAID,QAAO;AACT,WAAK,KAAK,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR,OAAOA,OAAM;AAAA;AAAA,QAEb,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAEA,QAAIC,OAAM;AACR,WAAK,KAAK,QAAQ;AAAA,QAChB,QAAQ;AAAA,QACR,OAAOA,MAAK;AAAA;AAAA,QAEZ,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAGA,QACE,YAAY,QAAQ,eAAe,KACnC,CAAC,aAAa,KAAK,QAAM,GAAG,UAAU,KACtC,UAAU,IAAI,GAAG,MAAM,GAAG,GAC1B;AACA;AAAA,IACF;AAEA,SAAK,KAAK,UAAU;AAAA,MAClB,QAAQ;AAAA,MACR;AAAA,MACA,sBAAsB,aAAa,MAAM,CAAC;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKO,cAAc,YAA+D;AAClF,WAAO,cAAc,KAAK,OAAO,UAAU;AAAA,EAC7C;AAAA,EAUO,SAAS,kBAA0B,uBAAqC;AAC7E,UAAM,OAAO,OAAO,qBAAqB,WAAW,mBAAmB;AAEvE,UAAM,aAAa,OAAO,qBAAqB,WAAW,wBAAwB;AAElF,WAAO,SAAS,KAAK,OAAO,MAAM,UAAU;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAKO,UAGL;AACA,WAAO,KAAK,MAAM,IAAI,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKO,UAAkB;AACvB,WAAO,oBAAoB,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAKO,QAAQ,SAAiG;AAC9G,UAAM,EAAE,iBAAiB,QAAQ,kBAAkB,CAAC,EAAE,IAAI,WAAW,CAAC;AAEtE,WAAO,QAAQ,KAAK,MAAM,KAAK;AAAA,MAC7B;AAAA,MACA,iBAAiB;AAAA,QACf,GAAG,6BAA6B,KAAK,MAAM;AAAA,QAC3C,GAAG;AAAA,MACL;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,UAAmB;AAC5B,WAAO,YAAY,KAAK,MAAM,GAAG;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKO,UAAgB;AACrB,SAAK,KAAK,SAAS;AAEnB,SAAK,QAAQ;AAEb,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,cAAuB;AApuBpC;AAquBI,YAAO,gBAAK,eAAL,mBAAiB,gBAAjB,YAAgC;AAAA,EACzC;AAAA,EAEO,MAAM,UAAkB,YAAqD;AAxuBtF;AAyuBI,aAAO,UAAK,SAAL,mBAAW,cAAc,UAAU,gBAAe;AAAA,EAC3D;AAAA,EAEO,OAAO,UAAkB,YAAuD;AA5uBzF;AA6uBI,aAAO,UAAK,SAAL,mBAAW,iBAAiB,UAAU,gBAAe;AAAA,EAC9D;AAAA,EAEO,KAAK,KAAa;AACvB,UAAM,OAAO,KAAK,MAAM,IAAI,QAAQ,GAAG;AAEvC,WAAO,IAAI,QAAQ,MAAM,IAAI;AAAA,EAC/B;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK,KAAK,CAAC;AAAA,EACpB;AACF;;;AoJ5uBO,SAAS,cAAc,QAI3B;AACD,SAAO,IAAI,UAAU;AAAA,IACnB,MAAM,OAAO;AAAA,IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAM,MAAM;AACpC,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,KAAK;AAEtE,UAAI,eAAe,SAAS,eAAe,MAAM;AAC/C,eAAO;AAAA,MACT;AAEA,YAAM,EAAE,GAAG,IAAI;AACf,YAAM,eAAe,MAAM,MAAM,SAAS,CAAC;AAC3C,YAAM,YAAY,MAAM,CAAC;AAEzB,UAAI,cAAc;AAChB,cAAM,cAAc,UAAU,OAAO,IAAI;AACzC,cAAM,YAAY,MAAM,OAAO,UAAU,QAAQ,YAAY;AAC7D,cAAM,UAAU,YAAY,aAAa;AAEzC,cAAM,gBAAgB,gBAAgB,MAAM,MAAM,MAAM,IAAI,MAAM,GAAG,EAClE,OAAO,UAAQ;AAEd,gBAAM,WAAW,KAAK,KAAK,KAAK;AAEhC,iBAAO,SAAS,KAAK,UAAQ,SAAS,OAAO,QAAQ,SAAS,KAAK,KAAK,IAAI;AAAA,QAC9E,CAAC,EACA,OAAO,UAAQ,KAAK,KAAK,SAAS;AAErC,YAAI,cAAc,QAAQ;AACxB,iBAAO;AAAA,QACT;AAEA,YAAI,UAAU,MAAM,IAAI;AACtB,aAAG,OAAO,SAAS,MAAM,EAAE;AAAA,QAC7B;AAEA,YAAI,YAAY,MAAM,MAAM;AAC1B,aAAG,OAAO,MAAM,OAAO,aAAa,SAAS;AAAA,QAC/C;AAEA,cAAM,UAAU,MAAM,OAAO,cAAc,aAAa;AAExD,WAAG,QAAQ,MAAM,OAAO,aAAa,SAAS,OAAO,KAAK,OAAO,cAAc,CAAC,CAAC,CAAC;AAElF,WAAG,iBAAiB,OAAO,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;ACrDO,SAAS,cAAc,QAgB3B;AACD,SAAO,IAAI,UAAU;AAAA,IACnB,MAAM,OAAO;AAAA,IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAM,MAAM;AACpC,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,KAAK,KAAK,CAAC;AAC5E,YAAM,EAAE,GAAG,IAAI;AACf,YAAM,QAAQ,MAAM;AACpB,UAAI,MAAM,MAAM;AAEhB,YAAM,UAAU,OAAO,KAAK,OAAO,UAAU;AAE7C,UAAI,MAAM,CAAC,GAAG;AACZ,cAAM,SAAS,MAAM,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC;AAC5C,YAAI,aAAa,QAAQ;AAEzB,YAAI,aAAa,KAAK;AACpB,uBAAa;AAAA,QACf,OAAO;AACL,gBAAM,aAAa,MAAM,CAAC,EAAE;AAAA,QAC9B;AAGA,cAAM,WAAW,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC;AAE7C,WAAG,WAAW,UAAU,QAAQ,MAAM,CAAC,EAAE,SAAS,CAAC;AAGnD,WAAG,YAAY,YAAY,KAAK,OAAO;AAAA,MACzC,WAAW,MAAM,CAAC,GAAG;AACnB,cAAM,iBAAiB,OAAO,KAAK,WAAW,QAAQ,QAAQ;AAE9D,WAAG,OAAO,gBAAgB,OAAO,KAAK,OAAO,UAAU,CAAC,EAAE,OAAO,GAAG,QAAQ,IAAI,KAAK,GAAG,GAAG,QAAQ,IAAI,GAAG,CAAC;AAAA,MAC7G;AAEA,SAAG,eAAe;AAAA,IACpB;AAAA,EACF,CAAC;AACH;;;ACnDO,SAAS,uBAAuB,QAIpC;AACD,SAAO,IAAI,UAAU;AAAA,IACnB,MAAM,OAAO;AAAA,IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAM,MAAM;AACpC,YAAM,SAAS,MAAM,IAAI,QAAQ,MAAM,IAAI;AAC3C,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,KAAK,KAAK,CAAC;AAE5E,UAAI,CAAC,OAAO,KAAK,EAAE,EAAE,eAAe,OAAO,MAAM,EAAE,GAAG,OAAO,WAAW,EAAE,GAAG,OAAO,IAAI,GAAG;AACzF,eAAO;AAAA,MACT;AAEA,YAAM,GAAG,OAAO,MAAM,MAAM,MAAM,EAAE,EAAE,aAAa,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,UAAU;AAAA,IACpG;AAAA,EACF,CAAC;AACH;;;ACxBO,SAAS,cAAc,QAAoD;AAChF,SAAO,IAAI,UAAU;AAAA,IACnB,MAAM,OAAO;AAAA,IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAM,MAAM;AACpC,UAAI,SAAS,OAAO;AACpB,UAAI,QAAQ,MAAM;AAClB,YAAM,MAAM,MAAM;AAElB,UAAI,MAAM,CAAC,GAAG;AACZ,cAAM,SAAS,MAAM,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC;AAE5C,kBAAU,MAAM,CAAC,EAAE,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM;AACjD,iBAAS;AAET,cAAM,SAAS,QAAQ;AAEvB,YAAI,SAAS,GAAG;AACd,mBAAS,MAAM,CAAC,EAAE,MAAM,SAAS,QAAQ,MAAM,IAAI;AACnD,kBAAQ;AAAA,QACV;AAAA,MACF;AAEA,YAAM,GAAG,WAAW,QAAQ,OAAO,GAAG;AAAA,IACxC;AAAA,EACF,CAAC;AACH;;;AChCA,SAAS,WAAAC,UAAS,oBAAoB;AAuB/B,SAAS,kBAAkB,QAQ/B;AACD,SAAO,IAAI,UAAU;AAAA,IACnB,MAAM,OAAO;AAAA,IACb,SAAS,CAAC,EAAE,OAAO,OAAO,OAAO,MAAM,MAAM;AAC3C,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,KAAK,KAAK,CAAC;AAC5E,YAAM,KAAK,MAAM,GAAG,OAAO,MAAM,MAAM,MAAM,EAAE;AAC/C,YAAM,SAAS,GAAG,IAAI,QAAQ,MAAM,IAAI;AACxC,YAAM,aAAa,OAAO,WAAW;AACrC,YAAM,WAAW,cAAc,aAAa,YAAY,OAAO,MAAM,UAAU;AAE/E,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AAEA,SAAG,KAAK,YAAY,QAAQ;AAE5B,UAAI,OAAO,aAAa,OAAO,QAAQ;AACrC,cAAM,EAAE,WAAW,YAAY,IAAI;AACnC,cAAM,EAAE,gBAAgB,IAAI,OAAO,OAAO;AAC1C,cAAM,QAAQ,eAAgB,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAM;AAElF,YAAI,OAAO;AACT,gBAAM,gBAAgB,MAAM,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEnF,aAAG,YAAY,aAAa;AAAA,QAC9B;AAAA,MACF;AACA,UAAI,OAAO,gBAAgB;AAEzB,cAAM,WACJ,OAAO,KAAK,SAAS,gBAAgB,OAAO,KAAK,SAAS,gBAAgB,aAAa;AAEzF,cAAM,EAAE,iBAAiB,UAAU,UAAU,EAAE,IAAI;AAAA,MACrD;AAEA,YAAM,SAAS,GAAG,IAAI,QAAQ,MAAM,OAAO,CAAC,EAAE;AAE9C,UACE,UACA,OAAO,SAAS,OAAO,QACvBC,SAAQ,GAAG,KAAK,MAAM,OAAO,CAAC,MAC7B,CAAC,OAAO,iBAAiB,OAAO,cAAc,OAAO,MAAM,IAC5D;AACA,WAAG,KAAK,MAAM,OAAO,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AC5CO,SAASC,UAAS,OAAoC;AAC3D,SAAO,MAAM;AACf;AAEO,IAAM,IAAiB,CAAC,KAAK,eAAe;AAEjD,MAAI,QAAQ,QAAQ;AAClB,WAAO;AAAA,EACT;AAGA,MAAI,eAAe,UAAU;AAC3B,WAAO,IAAI,UAAU;AAAA,EACvB;AAEA,QAAM,EAAE,UAAU,GAAG,KAAK,IAAI,kCAAc,CAAC;AAE7C,MAAI,QAAQ,OAAO;AACjB,UAAM,IAAI,MAAM,gFAAgF;AAAA,EAClG;AAGA,SAAO,CAAC,KAAK,MAAM,QAAQ;AAC7B;;;ACzDA,SAA2B,iBAAAC,sBAAqB;AAEzC,SAAS,cAAc,OAAoB,UAA6B;AAC7E,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,MAAM,IAAI;AAGlB,MAAI,qBAAqBA,gBAAe;AACtC,UAAM,QAAQ,MAAM,MAAM;AAC1B,UAAM,SAAS,MAAM;AAGrB,WAAO,OAAO,eAAe,OAAO,QAAQ,GAAG,QAAQ;AAAA,EACzD;AAGA,MAAI,QAAQ,MAAM;AAElB,SAAO,SAAS,GAAG;AACjB,UAAM,QAAQ,MAAM,MAAM,KAAK;AAC/B,UAAM,SAAS,MAAM,KAAK,KAAK;AAC/B,UAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,QAAI,MAAM,UAAU,QAAQ,GAAG;AAC7B,aAAO;AAAA,IACT;AACA,aAAS;AAAA,EACX;AACA,SAAO;AACT;;;AC5BO,SAAS,eAAe,QAAwB;AACrD,SAAO,OAAO,QAAQ,yBAAyB,MAAM;AACvD;;;ACHO,SAAS,SAAS,OAA6B;AACpD,SAAO,OAAO,UAAU;AAC1B;;;ACKO,SAAS,yBAAyB,WAAiB,QAAgB,QAA6B,CAAC,GAAS;AAC/G,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,EAAE,KAAK,GAAG,IAAI;AACpB,QAAM,WAAW;AAEjB,MAAI,YAAY,CAAC,MAAM,QAAQ;AAC7B,UAAM,OAAO,GAAG,QAAQ,IAAI,GAAG;AAC/B,UAAM,KAAK,GAAG,QAAQ,IAAI,GAAG,IAAI,KAAK;AACtC,QAAI,YAAyB;AAG7B,SAAK,MAAM,QAAQ,UAAQ;AACzB,UAAI,SAAS,UAAU;AACrB,eAAO;AAAA,MACT;AAEA,kBAAY;AAAA,IACd,CAAC;AAED,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAGA,QAAI,cAAc;AAClB,WAAO,KAAK,KAAK,EAAE,QAAQ,OAAK;AAC9B,UAAI,MAAM,CAAC,MAAM,UAAW,MAAM,CAAC,GAAG;AACpC,sBAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAED,QAAI,aAAa;AACf,YAAM,cAAc,UAAU,KAAK,OAAO;AAAA,QACxC,GAAG,UAAU;AAAA,QACb,GAAG;AAAA,MACL,CAAC;AAED,SAAG,WAAW,MAAM,IAAI,UAAU,IAAI;AACtC,SAAG,QAAQ,MAAM,IAAI,WAAW;AAAA,IAClC;AAAA,EACF,CAAC;AAED,MAAI,GAAG,YAAY;AACjB,WAAO,KAAK,SAAS,EAAE;AAAA,EACzB;AACF;AAEO,IAAM,WAAN,MAA6F;AAAA,EAOlG,YAAY,WAAsB,OAAsB,SAA4B;AAClF,SAAK,YAAY;AACjB,SAAK,SAAS,MAAM;AACpB,SAAK,UAAU,EAAE,GAAG,QAAQ;AAC5B,SAAK,OAAO,MAAM;AAClB,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA,EAEA,IAAI,MAAmB;AACrB,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAAA,EAEA,IAAI,aAAiC;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,OAA4B,WAAwB;AACnE,6BAAyB,aAAa,KAAK,MAAM,KAAK,QAAQ,KAAK;AAAA,EACrE;AAAA,EAEA,eAAe,UAAuC;AACpD,QAAI,CAAC,KAAK,OAAO,CAAC,KAAK,YAAY;AACjC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,QAAQ,mBAAmB,YAAY;AACrD,aAAO,KAAK,QAAQ,eAAe,EAAE,SAAS,CAAC;AAAA,IACjD;AAEA,QAAI,SAAS,SAAS,aAAa;AACjC,aAAO;AAAA,IACT;AAEA,QACE,KAAK,IAAI,SAAS,SAAS,MAAM,KACjC,SAAS,SAAS,gBACjB,MAAM,KAAK,UAAU,MACtB,KAAK,OAAO,WACZ;AACA,YAAM,eAAe,CAAC,GAAG,MAAM,KAAK,SAAS,UAAU,GAAG,GAAG,MAAM,KAAK,SAAS,YAAY,CAAC;AAE9F,UAAI,aAAa,MAAM,UAAQ,KAAK,iBAAiB,GAAG;AACtD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,KAAK,eAAe,SAAS,UAAU,SAAS,SAAS,cAAc;AACzE,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,WAAW,SAAS,SAAS,MAAM,GAAG;AAC7C,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF;;;AC0NO,IAAMC,QAAN,MAAM,cAA2C,WAA2D;AAAA,EAA5G;AAAA;AACL,gBAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMP,OAAO,OAAyB,SAAwE,CAAC,GAAG;AAE1G,UAAM,iBAAiB,OAAO,WAAW,aAAa,OAAO,IAAI;AACjE,WAAO,IAAI,MAAW,cAAc;AAAA,EACtC;AAAA,EAEA,UAAU,SAA4B;AACpC,WAAO,MAAM,UAAU,OAAO;AAAA,EAChC;AAAA,EAEA,OAKE,gBAUwC;AAExC,UAAM,iBAAiB,OAAO,mBAAmB,aAAa,eAAe,IAAI;AACjF,WAAO,MAAM,OAAO,cAAc;AAAA,EACpC;AACF;;;ACxXA,SAAS,iBAAAC,sBAAqB;AAYvB,IAAM,WAAN,MAKP;AAAA,EAuBE,YAAY,WAAsB,OAA8B,SAA4B;AAF5F,sBAAa;AAGX,SAAK,YAAY;AACjB,SAAK,SAAS,MAAM;AACpB,SAAK,UAAU;AAAA,MACb,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACL;AACA,SAAK,YAAY,MAAM;AACvB,SAAK,OAAO,MAAM;AAClB,SAAK,cAAc,MAAM;AACzB,SAAK,mBAAmB,MAAM;AAC9B,SAAK,OAAO,MAAM;AAClB,SAAK,iBAAiB,MAAM;AAC5B,SAAK,SAAS,MAAM;AACpB,SAAK,MAAM;AAAA,EACb;AAAA,EAEA,QAAQ;AAEN;AAAA,EACF;AAAA,EAEA,IAAI,MAAmB;AACrB,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAAA,EAEA,IAAI,aAAiC;AACnC,WAAO;AAAA,EACT;AAAA,EAEA,YAAY,OAAkB;AAvEhC;AAwEI,UAAM,EAAE,KAAK,IAAI,KAAK;AACtB,UAAM,SAAS,MAAM;AAIrB,UAAM,aACJ,OAAO,aAAa,KAAI,YAAO,kBAAP,mBAAsB,QAAQ,wBAAwB,OAAO,QAAQ,oBAAoB;AAEnH,QAAI,CAAC,KAAK,SAAO,UAAK,eAAL,mBAAiB,SAAS,YAAW,CAAC,YAAY;AACjE;AAAA,IACF;AAEA,QAAI,IAAI;AACR,QAAI,IAAI;AAGR,QAAI,KAAK,QAAQ,YAAY;AAC3B,YAAM,SAAS,KAAK,IAAI,sBAAsB;AAC9C,YAAM,YAAY,WAAW,sBAAsB;AAGnD,YAAM,WAAU,WAAM,YAAN,aAAkB,WAAc,gBAAd,mBAA2B;AAC7D,YAAM,WAAU,WAAM,YAAN,aAAkB,WAAc,gBAAd,mBAA2B;AAE7D,UAAI,UAAU,IAAI,OAAO,IAAI;AAC7B,UAAI,UAAU,IAAI,OAAO,IAAI;AAAA,IAC/B;AAEA,UAAM,aAAa,KAAK,IAAI,UAAU,IAAI;AAI1C,QAAI;AACF,YAAM,SAAS,KAAK,IAAI,sBAAsB;AAC9C,iBAAW,MAAM,QAAQ,GAAG,KAAK,MAAM,OAAO,KAAK,CAAC;AACpD,iBAAW,MAAM,SAAS,GAAG,KAAK,MAAM,OAAO,MAAM,CAAC;AACtD,iBAAW,MAAM,YAAY;AAE7B,iBAAW,MAAM,gBAAgB;AAAA,IACnC,QAAQ;AAAA,IAER;AAKA,QAAI,mBAAuC;AAE3C,QAAI;AACF,yBAAmB,SAAS,cAAc,KAAK;AAC/C,uBAAiB,MAAM,WAAW;AAClC,uBAAiB,MAAM,MAAM;AAC7B,uBAAiB,MAAM,OAAO;AAC9B,uBAAiB,MAAM,gBAAgB;AACvC,uBAAiB,YAAY,UAAU;AACvC,eAAS,KAAK,YAAY,gBAAgB;AAE1C,kBAAM,iBAAN,mBAAoB,aAAa,YAAY,GAAG;AAAA,IAClD,UAAE;AAGA,UAAI,kBAAkB;AACpB,mBAAW,MAAM;AACf,cAAI;AACF,iEAAkB;AAAA,UACpB,QAAQ;AAAA,UAER;AAAA,QACF,GAAG,CAAC;AAAA,MACN;AAAA,IACF;AAEA,UAAM,MAAM,KAAK,OAAO;AAExB,QAAI,OAAO,QAAQ,UAAU;AAC3B;AAAA,IACF;AAGA,UAAM,YAAYC,eAAc,OAAO,KAAK,MAAM,KAAK,GAAG;AAC1D,UAAM,cAAc,KAAK,MAAM,GAAG,aAAa,SAAS;AAExD,SAAK,SAAS,WAAW;AAAA,EAC3B;AAAA,EAEA,UAAU,OAAc;AA7J1B;AA8JI,QAAI,CAAC,KAAK,KAAK;AACb,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,QAAQ,cAAc,YAAY;AAChD,aAAO,KAAK,QAAQ,UAAU,EAAE,MAAM,CAAC;AAAA,IACzC;AAEA,UAAM,SAAS,MAAM;AACrB,UAAM,cAAc,KAAK,IAAI,SAAS,MAAM,KAAK,GAAC,UAAK,eAAL,mBAAiB,SAAS;AAG5E,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AAEA,UAAM,cAAc,MAAM,KAAK,WAAW,MAAM;AAChD,UAAM,cAAc,MAAM,SAAS;AACnC,UAAM,UAAU,CAAC,SAAS,UAAU,UAAU,UAAU,EAAE,SAAS,OAAO,OAAO,KAAK,OAAO;AAG7F,QAAI,WAAW,CAAC,eAAe,CAAC,aAAa;AAC3C,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,WAAW,IAAI,KAAK;AAC5B,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,cAAc,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK;AAC1C,UAAM,eAAeA,eAAc,aAAa,KAAK,IAAI;AACzD,UAAM,cAAc,MAAM,SAAS;AACnC,UAAM,eAAe,MAAM,SAAS;AACpC,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,eAAe,MAAM,SAAS;AAKpC,QAAI,CAAC,eAAe,gBAAgB,eAAe,MAAM,WAAW,KAAK,KAAK;AAC5E,YAAM,eAAe;AAAA,IACvB;AAEA,QAAI,eAAe,eAAe,CAAC,cAAc,MAAM,WAAW,KAAK,KAAK;AAC1E,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AAGA,QAAI,eAAe,cAAc,CAAC,cAAc,cAAc;AAC5D,YAAM,aAAa,OAAO,QAAQ,oBAAoB;AACtD,YAAM,oBAAoB,eAAe,KAAK,QAAQ,cAAc,KAAK,IAAI,SAAS,UAAU;AAEhG,UAAI,mBAAmB;AACrB,aAAK,aAAa;AAElB,iBAAS;AAAA,UACP;AAAA,UACA,MAAM;AACJ,iBAAK,aAAa;AAAA,UACpB;AAAA,UACA,EAAE,MAAM,KAAK;AAAA,QACf;AAEA,iBAAS;AAAA,UACP;AAAA,UACA,MAAM;AACJ,iBAAK,aAAa;AAAA,UACpB;AAAA,UACA,EAAE,MAAM,KAAK;AAAA,QACf;AAEA,iBAAS;AAAA,UACP;AAAA,UACA,MAAM;AACJ,iBAAK,aAAa;AAAA,UACpB;AAAA,UACA,EAAE,MAAM,KAAK;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAGA,QAAI,cAAc,eAAe,eAAe,gBAAgB,cAAe,gBAAgB,cAAe;AAC5G,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAA8B;AAC3C,QAAI,CAAC,KAAK,OAAO,CAAC,KAAK,YAAY;AACjC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,QAAQ,mBAAmB,YAAY;AACrD,aAAO,KAAK,QAAQ,eAAe,EAAE,SAAS,CAAC;AAAA,IACjD;AAIA,QAAI,KAAK,KAAK,UAAU,KAAK,KAAK,QAAQ;AACxC,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,SAAS,aAAa;AACjC,aAAO;AAAA,IACT;AAOA,QACE,KAAK,IAAI,SAAS,SAAS,MAAM,KACjC,SAAS,SAAS,gBACjB,MAAM,KAAK,UAAU,MACtB,KAAK,OAAO,WACZ;AACA,YAAM,eAAe,CAAC,GAAG,MAAM,KAAK,SAAS,UAAU,GAAG,GAAG,MAAM,KAAK,SAAS,YAAY,CAAC;AAI9F,UAAI,aAAa,MAAM,UAAQ,KAAK,iBAAiB,GAAG;AACtD,eAAO;AAAA,MACT;AAAA,IACF;AAIA,QAAI,KAAK,eAAe,SAAS,UAAU,SAAS,SAAS,cAAc;AACzE,aAAO;AAAA,IACT;AAGA,QAAI,KAAK,WAAW,SAAS,SAAS,MAAM,GAAG;AAC7C,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,YAAuC;AACtD,SAAK,OAAO,SAAS,QAAQ,CAAC,EAAE,GAAG,MAAM;AACvC,YAAM,MAAM,KAAK,OAAO;AAExB,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO;AAAA,MACT;AAEA,SAAG,cAAc,KAAK,QAAW;AAAA,QAC/B,GAAG,KAAK,KAAK;AAAA,QACb,GAAG;AAAA,MACL,CAAC;AAED,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,aAAmB;AACjB,UAAM,OAAO,KAAK,OAAO;AAEzB,QAAI,OAAO,SAAS,UAAU;AAC5B;AAAA,IACF;AACA,UAAM,KAAK,OAAO,KAAK,KAAK;AAE5B,SAAK,OAAO,SAAS,YAAY,EAAE,MAAM,GAAG,CAAC;AAAA,EAC/C;AACF;;;ACrUO,SAAS,cAAc,QAQ3B;AACD,SAAO,IAAI,UAAU;AAAA,IACnB,MAAM,OAAO;AAAA,IACb,SAAS,CAAC,EAAE,OAAO,OAAO,OAAO,WAAW,MAAM;AAChD,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,OAAO,UAAU;AAElF,UAAI,eAAe,SAAS,eAAe,MAAM;AAC/C,eAAO;AAAA,MACT;AAEA,YAAM,EAAE,GAAG,IAAI;AACf,YAAM,eAAe,MAAM,MAAM,SAAS,CAAC;AAC3C,YAAM,YAAY,MAAM,CAAC;AACzB,UAAI,UAAU,MAAM;AAEpB,UAAI,cAAc;AAChB,cAAM,cAAc,UAAU,OAAO,IAAI;AACzC,cAAM,YAAY,MAAM,OAAO,UAAU,QAAQ,YAAY;AAC7D,cAAM,UAAU,YAAY,aAAa;AAEzC,cAAM,gBAAgB,gBAAgB,MAAM,MAAM,MAAM,IAAI,MAAM,GAAG,EAClE,OAAO,UAAQ;AAEd,gBAAM,WAAW,KAAK,KAAK,KAAK;AAEhC,iBAAO,SAAS,KAAK,UAAQ,SAAS,OAAO,QAAQ,SAAS,KAAK,KAAK,IAAI;AAAA,QAC9E,CAAC,EACA,OAAO,UAAQ,KAAK,KAAK,SAAS;AAErC,YAAI,cAAc,QAAQ;AACxB,iBAAO;AAAA,QACT;AAEA,YAAI,UAAU,MAAM,IAAI;AACtB,aAAG,OAAO,SAAS,MAAM,EAAE;AAAA,QAC7B;AAEA,YAAI,YAAY,MAAM,MAAM;AAC1B,aAAG,OAAO,MAAM,OAAO,aAAa,SAAS;AAAA,QAC/C;AAEA,kBAAU,MAAM,OAAO,cAAc,aAAa;AAElD,WAAG,QAAQ,MAAM,OAAO,aAAa,SAAS,OAAO,KAAK,OAAO,cAAc,CAAC,CAAC,CAAC;AAElF,WAAG,iBAAiB,OAAO,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AC1DO,SAAS,cAAc,QAS3B;AACD,SAAO,IAAI,UAAU;AAAA,IACnB,MAAM,OAAO;AAAA,IACb,QAAQ,EAAE,OAAO,OAAO,OAAO,WAAW,GAAG;AAC3C,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,OAAO,UAAU;AAClF,YAAM,UAAU,aAAa,OAAO,YAAY,QAAW,UAAU;AAErE,UAAI,eAAe,SAAS,eAAe,MAAM;AAC/C,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,EAAE,MAAM,OAAO,KAAK,MAAM,OAAO,WAAW;AAEzD,UAAI,SAAS;AACX,aAAK,UAAU;AAAA,MACjB;AAEA,UAAI,MAAM,OAAO;AACf,cAAM,EAAE,YAAY,KAAK,EAAE,gBAAgB,MAAM,MAAM,IAAI;AAAA,MAC7D;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;ACnCO,SAAS,cAAc,QAAoD;AAChF,SAAO,IAAI,UAAU;AAAA,IACnB,MAAM,OAAO;AAAA,IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAM,MAAM;AACpC,UAAI,SAAS,OAAO;AACpB,UAAI,QAAQ,MAAM;AAClB,YAAM,MAAM,MAAM;AAElB,UAAI,MAAM,CAAC,GAAG;AACZ,cAAM,SAAS,MAAM,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC;AAE5C,kBAAU,MAAM,CAAC,EAAE,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM;AACjD,iBAAS;AAET,cAAM,SAAS,QAAQ;AAEvB,YAAI,SAAS,GAAG;AACd,mBAAS,MAAM,CAAC,EAAE,MAAM,SAAS,QAAQ,MAAM,IAAI;AACnD,kBAAQ;AAAA,QACV;AAAA,MACF;AAEA,YAAM,GAAG,WAAW,QAAQ,OAAO,GAAG;AAAA,IACxC;AAAA,EACF,CAAC;AACH;;;AC1BO,IAAM,UAAN,MAAc;AAAA,EAKnB,YAAY,aAA0B;AACpC,SAAK,cAAc;AACnB,SAAK,cAAc,KAAK,YAAY,MAAM;AAAA,EAC5C;AAAA,EAEA,IAAI,UAAiC;AACnC,QAAI,UAAU;AAEd,UAAM,iBAAiB,KAAK,YAAY,MAAM,MAAM,KAAK,WAAW,EAAE,OAAO,CAAC,aAAa,SAAS;AAClG,YAAM,YAAY,KAAK,OAAO,EAAE,UAAU,WAAW;AAErD,UAAI,UAAU,SAAS;AACrB,kBAAU;AAAA,MACZ;AAEA,aAAO,UAAU;AAAA,IACnB,GAAG,QAAQ;AAEX,WAAO;AAAA,MACL,UAAU;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACF;", "names": ["command", "run", "<PERSON><PERSON><PERSON>", "style", "_a", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Node", "Node", "node", "output", "mark", "range", "TextSelection", "TextSelection", "Selection", "Fragment", "Fragment", "Fragment", "Plugin", "run", "Plugin", "Fragment", "from", "to", "Plugin", "Plugin", "range", "TextSelection", "TextSelection", "TextSelection", "Fragment", "Fragment", "isTextSelection", "joinPoint", "originalCommand", "isActive", "document", "NodeSelection", "NodeSelection", "TextSelection", "TextSelection", "NodeSelection", "TextSelection", "NodeSelection", "TextSelection", "first", "Fragment", "TextSelection", "canSplit", "Fragment", "newNextTypeAttributes", "nextType", "TextSelection", "canSplit", "isActive", "isActive", "isActive", "_a", "_b", "_c", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Selection", "Selection", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "focus", "blur", "canJoin", "canJoin", "Fragment", "NodeSelection", "Node", "NodeSelection", "NodeSelection"]}