import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { getProjects, createProject } from "../services/api";
import { manuscriptTemplate } from "../utils/manuscriptTemplate";
import "./ProjectsListPage.css"; // We'll create a separate CSS file

const ProjectsListPage = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [creating, setCreating] = useState(false);
  const navigate = useNavigate();

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await getProjects();
      console.log("Projects API response:", response.data);
      // Handle paginated response structure
      const projectsData = response.data.results || response.data;
      setProjects(Array.isArray(projectsData) ? projectsData : []);
      setError("");
    } catch (err) {
      console.error("Failed to fetch projects:", err);
      setError(err.response?.data?.message || "Could not load your projects.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const handleCreateNewProject = async () => {
    const title = prompt("Please enter a title for your new project:");

    if (!title) return;
    if (title.length < 3) {
      alert("Project title must be at least 3 characters long.");
      return;
    }

    try {
      setCreating(true);
      const projectData = {
        title: title,
        description: `Research project: ${title}`,
        content: manuscriptTemplate
      };
      console.log("Creating project with data:", projectData);
      const response = await createProject(projectData);
      console.log("Project creation response:", response.data);
      const newProjectId = response.data.id;

      // Refresh the projects list after creation
      await fetchProjects();

      navigate(`/projects/${newProjectId}`);
    } catch (err) {
      console.error("Failed to create new project:", err);
      const errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || "Error: Could not create the project.";
      alert(errorMessage);
    } finally {
      setCreating(false);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading-spinner"></div>
        <p>Loading your projects...</p>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="page-header">
        <h1>My Projects</h1>
        <button onClick={handleCreateNewProject} className="btn-primary" disabled={creating}>
          {creating ? "Creating..." : "+ Start New Project"}
        </button>
      </div>

      {error && (
        <div className="error-alert">
          <span>{error}</span>
          <button onClick={() => window.location.reload()} className="btn-link">
            Try Again
          </button>
        </div>
      )}

      {projects.length > 0 ? (
        <div className="projects-grid">
          {projects.map((project) => (
            <ProjectCard key={project.id} project={project} formatDate={formatDate} />
          ))}
        </div>
      ) : (
        <div className="empty-state">
          <h2>No projects yet</h2>
          <p>Get started by creating your first research project</p>
          <button onClick={handleCreateNewProject} className="btn-primary">
            Create Your First Project
          </button>
        </div>
      )}
    </div>
  );
};

// Extracted Project Card as a separate component for better readability
const ProjectCard = ({ project, formatDate }) => (
  <div className="project-card">
    <Link to={`/projects/${project.id}`} className="project-link">
      <div className="project-header">
        <h3>{project.title}</h3>
        <span className={`status-badge status-${project.status ? String(project.status).toLowerCase() : 'unknown'}`}>{project.status || 'Unknown'}</span>
      </div>
      <div className="project-details">
        <p className="research-group">
          <span className="label">Research Group:</span>
          {project.research_group_name || 'Unknown'}
        </p>
        {project.updated_at && (
          <p className="last-updated">
            <span className="label">Last updated:</span>
            {formatDate(project.updated_at)}
          </p>
        )}
      </div>
    </Link>
  </div>
);

export default ProjectsListPage;



