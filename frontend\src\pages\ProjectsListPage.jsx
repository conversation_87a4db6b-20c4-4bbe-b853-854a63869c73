import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { getProjects, createProject } from "../services/api";
import { manuscriptTemplate } from "../utils/manuscriptTemplate";
import ProjectCard from "../components/ProjectCard";
// import "./ProjectsListPage.css"; // Assuming you will create a CSS file for this page

const ProjectsListPage = () => {
  // --- STATE MANAGEMENT ---
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [isCreating, setIsCreating] = useState(false); // To disable buttons during creation
  const navigate = useNavigate();

  // --- DATA FETCHING ---
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getProjects();
      // Robustly handle both paginated (e.g., {results: [...]}) and non-paginated (e.g., [...]) API responses
      const projectsData = Array.isArray(response.data) ? response.data : response.data?.results || [];
      setProjects(projectsData);
    } catch (err) {
      console.error("Failed to fetch projects:", err);
      setError(err.response?.data?.detail || "Could not load your projects.");
    } finally {
      setLoading(false);
    }
  }, []);

  // Effect to fetch projects on initial component mount
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // --- EVENT HANDLERS ---
  const handleCreateNewProject = async () => {
    const title = prompt("Please enter a title for your new research project:");
    if (!title || title.trim().length < 3) {
      if (title !== null) {
        // Only show alert if user didn't click "Cancel"
        alert("Project title must be at least 3 characters long.");
      }
      return;
    }

    setIsCreating(true);
    try {
      // Create the new project with the boilerplate template
      const response = await createProject({
        title: title,
        description: `A new research project: ${title}`,
        content: manuscriptTemplate,
        project_type: "RESEARCH", // Default type for user-created projects
        status: "DRAFT",
      });
      const newProject = response.data;

      // Optimistic UI Update: add the new project to the top of the list
      // without needing to re-fetch the entire list from the server.
      setProjects((prevProjects) => [newProject, ...prevProjects]);

      // Navigate to the detail page for the newly created project
      navigate(`/projects/${newProject.id}`);
    } catch (err) {
      console.error("Failed to create new project:", err);
      const errorMessage =
        err.response?.data?.title?.[0] || err.response?.data?.detail || "Could not create the project.";
      alert(`Error: ${errorMessage}`);
    } finally {
      setIsCreating(false);
    }
  };

  // --- RENDER LOGIC ---
  // A sub-component to keep the main return statement clean
  const renderContent = () => {
    if (loading) {
      // You can replace this with a more sophisticated spinner component
      return <div>Loading your projects...</div>;
    }
    if (error) {
      return (
        <div style={{ color: "red", textAlign: "center", padding: "20px" }}>
          <span>{error}</span>
          <button onClick={fetchProjects} style={{ marginLeft: "10px" }}>
            Try Again
          </button>
        </div>
      );
    }
    if (projects.length > 0) {
      return (
        <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))", gap: "20px" }}>
          {projects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
      );
    }
    // Render a helpful message if the user has no projects
    return (
      <div style={{ textAlign: "center", padding: "50px", background: "#f8fafc", borderRadius: "8px" }}>
        <h2>You have no projects yet</h2>
        <p>Get started by creating your first collaborative research project.</p>
        <button onClick={handleCreateNewProject} disabled={isCreating} style={styles.button}>
          {isCreating ? "Creating..." : "Create Your First Project"}
        </button>
      </div>
    );
  };

  return (
    <div className="container" style={{ maxWidth: "1200px", margin: "20px auto" }}>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "30px" }}>
        <h1>My Projects</h1>
        {/* Hide the button if the projects list is empty, as it's shown in the empty state */}
        {projects.length > 0 && (
          <button onClick={handleCreateNewProject} disabled={isCreating} style={styles.button}>
            {isCreating ? "Creating..." : "+ Start New Project"}
          </button>
        )}
      </div>
      {renderContent()}
    </div>
  );
};

// Simple inline styles for demonstration
const styles = {
  button: {
    background: "#3b82f6",
    color: "white",
    border: "none",
    padding: "10px 15px",
    borderRadius: "6px",
    cursor: "pointer",
    fontWeight: "bold",
  },
};

export default ProjectsListPage;





// import React, { useState, useEffect } from "react";
// import { Link, useNavigate } from "react-router-dom";
// import { getProjects, createProject } from "../services/api";
// import { manuscriptTemplate } from "../utils/manuscriptTemplate";
// import "./ProjectsListPage.css"; // We'll create a separate CSS file

// const ProjectsListPage = () => {
//   const [projects, setProjects] = useState([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState("");
//   const [creating, setCreating] = useState(false);
//   const navigate = useNavigate();

//   const fetchProjects = async () => {
//     try {
//       setLoading(true);
//       const response = await getProjects();
//       console.log("Projects API response:", response.data);
//       // Handle paginated response structure
//       const projectsData = response.data.results || response.data;
//       setProjects(Array.isArray(projectsData) ? projectsData : []);
//       setError("");
//     } catch (err) {
//       console.error("Failed to fetch projects:", err);
//       setError(err.response?.data?.message || "Could not load your projects.");
//     } finally {
//       setLoading(false);
//     }
//   };

//   useEffect(() => {
//     fetchProjects();
//   }, []);

//   const handleCreateNewProject = async () => {
//     const title = prompt("Please enter a title for your new project:");

//     if (!title) return;
//     if (title.length < 3) {
//       alert("Project title must be at least 3 characters long.");
//       return;
//     }

//     try {
//       setCreating(true);
//       const projectData = {
//         title: title,
//         description: `Research project: ${title}`,
//         content: manuscriptTemplate
//       };
//       console.log("Creating project with data:", projectData);
//       const response = await createProject(projectData);
//       console.log("Project creation response:", response.data);
//       const newProjectId = response.data.id;

//       // Refresh the projects list after creation
//       await fetchProjects();

//       navigate(`/projects/${newProjectId}`);
//     } catch (err) {
//       console.error("Failed to create new project:", err);
//       const errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || "Error: Could not create the project.";
//       alert(errorMessage);
//     } finally {
//       setCreating(false);
//     }
//   };

//   const formatDate = (dateString) => {
//     const options = { year: "numeric", month: "short", day: "numeric" };
//     return new Date(dateString).toLocaleDateString(undefined, options);
//   };

//   if (loading) {
//     return (
//       <div className="container">
//         <div className="loading-spinner"></div>
//         <p>Loading your projects...</p>
//       </div>
//     );
//   }

//   return (
//     <div className="container">
//       <div className="page-header">
//         <h1>My Projects</h1>
//         <button onClick={handleCreateNewProject} className="btn-primary" disabled={creating}>
//           {creating ? "Creating..." : "+ Start New Project"}
//         </button>
//       </div>

//       {error && (
//         <div className="error-alert">
//           <span>{error}</span>
//           <button onClick={() => window.location.reload()} className="btn-link">
//             Try Again
//           </button>
//         </div>
//       )}

//       {projects.length > 0 ? (
//         <div className="projects-grid">
//           {projects.map((project) => (
//             <ProjectCard key={project.id} project={project} formatDate={formatDate} />
//           ))}
//         </div>
//       ) : (
//         <div className="empty-state">
//           <h2>No projects yet</h2>
//           <p>Get started by creating your first research project</p>
//           <button onClick={handleCreateNewProject} className="btn-primary">
//             Create Your First Project
//           </button>
//         </div>
//       )}
//     </div>
//   );
// };

// // Extracted Project Card as a separate component for better readability
// const ProjectCard = ({ project, formatDate }) => (
//   <div className="project-card">
//     <Link to={`/projects/${project.id}`} className="project-link">
//       <div className="project-header">
//         <h3>{project.title}</h3>
//         <span className={`status-badge status-${project.status ? String(project.status).toLowerCase() : 'unknown'}`}>{project.status || 'Unknown'}</span>
//       </div>
//       <div className="project-details">
//         <p className="research-group">
//           <span className="label">Research Group:</span>
//           {project.research_group_name || 'Unknown'}
//         </p>
//         {project.updated_at && (
//           <p className="last-updated">
//             <span className="label">Last updated:</span>
//             {formatDate(project.updated_at)}
//           </p>
//         )}
//       </div>
//     </Link>
//   </div>
// );

// export default ProjectsListPage;
