{"version": 3, "sources": ["../../src/table/index.ts", "../../src/table/table.ts", "../../src/table/utilities/colStyle.ts", "../../src/table/TableView.ts", "../../src/table/utilities/createColGroup.ts", "../../src/table/utilities/createCell.ts", "../../src/table/utilities/getTableNodeTypes.ts", "../../src/table/utilities/createTable.ts", "../../src/table/utilities/deleteTableWhenAllCellsSelected.ts", "../../src/table/utilities/isCellSelection.ts"], "sourcesContent": ["export * from './table.js'\nexport * from './utilities/createColGroup.js'\nexport * from './utilities/createTable.js'\n", "import '../types.js'\n\nimport { callOrReturn, getExtensionField, mergeAttributes, Node } from '@tiptap/core'\nimport type { DOMOutputSpec, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\nimport {\n  addColumnAfter,\n  addColumnBefore,\n  addRowAfter,\n  addRowBefore,\n  CellSelection,\n  columnResizing,\n  deleteColumn,\n  deleteRow,\n  deleteTable,\n  fixTables,\n  goToNextCell,\n  mergeCells,\n  setCellAttr,\n  splitCell,\n  tableEditing,\n  toggleHeader,\n  toggleHeaderCell,\n} from '@tiptap/pm/tables'\nimport type { EditorView, NodeView } from '@tiptap/pm/view'\n\nimport { TableView } from './TableView.js'\nimport { createColGroup } from './utilities/createColGroup.js'\nimport { createTable } from './utilities/createTable.js'\nimport { deleteTableWhenAllCellsSelected } from './utilities/deleteTableWhenAllCellsSelected.js'\n\nexport interface TableOptions {\n  /**\n   * HTML attributes for the table element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * Enables the resizing of tables.\n   * @default false\n   * @example true\n   */\n  resizable: boolean\n\n  /**\n   * The width of the resize handle.\n   * @default 5\n   * @example 10\n   */\n  handleWidth: number\n\n  /**\n   * The minimum width of a cell.\n   * @default 25\n   * @example 50\n   */\n  cellMinWidth: number\n\n  /**\n   * The node view to render the table.\n   * @default TableView\n   */\n  View: (new (node: ProseMirrorNode, cellMinWidth: number, view: EditorView) => NodeView) | null\n\n  /**\n   * Enables the resizing of the last column.\n   * @default true\n   * @example false\n   */\n  lastColumnResizable: boolean\n\n  /**\n   * Allow table node selection.\n   * @default false\n   * @example true\n   */\n  allowTableNodeSelection: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    table: {\n      /**\n       * Insert a table\n       * @param options The table attributes\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.insertTable({ rows: 3, cols: 3, withHeaderRow: true })\n       */\n      insertTable: (options?: { rows?: number; cols?: number; withHeaderRow?: boolean }) => ReturnType\n\n      /**\n       * Add a column before the current column\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.addColumnBefore()\n       */\n      addColumnBefore: () => ReturnType\n\n      /**\n       * Add a column after the current column\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.addColumnAfter()\n       */\n      addColumnAfter: () => ReturnType\n\n      /**\n       * Delete the current column\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.deleteColumn()\n       */\n      deleteColumn: () => ReturnType\n\n      /**\n       * Add a row before the current row\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.addRowBefore()\n       */\n      addRowBefore: () => ReturnType\n\n      /**\n       * Add a row after the current row\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.addRowAfter()\n       */\n      addRowAfter: () => ReturnType\n\n      /**\n       * Delete the current row\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.deleteRow()\n       */\n      deleteRow: () => ReturnType\n\n      /**\n       * Delete the current table\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.deleteTable()\n       */\n      deleteTable: () => ReturnType\n\n      /**\n       * Merge the currently selected cells\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.mergeCells()\n       */\n      mergeCells: () => ReturnType\n\n      /**\n       * Split the currently selected cell\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.splitCell()\n       */\n      splitCell: () => ReturnType\n\n      /**\n       * Toggle the header column\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.toggleHeaderColumn()\n       */\n      toggleHeaderColumn: () => ReturnType\n\n      /**\n       * Toggle the header row\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.toggleHeaderRow()\n       */\n      toggleHeaderRow: () => ReturnType\n\n      /**\n       * Toggle the header cell\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.toggleHeaderCell()\n       */\n      toggleHeaderCell: () => ReturnType\n\n      /**\n       * Merge or split the currently selected cells\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.mergeOrSplit()\n       */\n      mergeOrSplit: () => ReturnType\n\n      /**\n       * Set a cell attribute\n       * @param name The attribute name\n       * @param value The attribute value\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.setCellAttribute('align', 'right')\n       */\n      setCellAttribute: (name: string, value: any) => ReturnType\n\n      /**\n       * Moves the selection to the next cell\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.goToNextCell()\n       */\n      goToNextCell: () => ReturnType\n\n      /**\n       * Moves the selection to the previous cell\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.goToPreviousCell()\n       */\n      goToPreviousCell: () => ReturnType\n\n      /**\n       * Try to fix the table structure if necessary\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.fixTables()\n       */\n      fixTables: () => ReturnType\n\n      /**\n       * Set a cell selection inside the current table\n       * @param position The cell position\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.setCellSelection({ anchorCell: 1, headCell: 2 })\n       */\n      setCellSelection: (position: { anchorCell: number; headCell?: number }) => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create tables.\n * @see https://www.tiptap.dev/api/nodes/table\n */\nexport const Table = Node.create<TableOptions>({\n  name: 'table',\n\n  // @ts-ignore\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      resizable: false,\n      handleWidth: 5,\n      cellMinWidth: 25,\n      // TODO: fix\n      View: TableView,\n      lastColumnResizable: true,\n      allowTableNodeSelection: false,\n    }\n  },\n\n  content: 'tableRow+',\n\n  tableRole: 'table',\n\n  isolating: true,\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'table' }]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    const { colgroup, tableWidth, tableMinWidth } = createColGroup(node, this.options.cellMinWidth)\n\n    const table: DOMOutputSpec = [\n      'table',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {\n        style: tableWidth ? `width: ${tableWidth}` : `min-width: ${tableMinWidth}`,\n      }),\n      colgroup,\n      ['tbody', 0],\n    ]\n\n    return table\n  },\n\n  addCommands() {\n    return {\n      insertTable:\n        ({ rows = 3, cols = 3, withHeaderRow = true } = {}) =>\n        ({ tr, dispatch, editor }) => {\n          const node = createTable(editor.schema, rows, cols, withHeaderRow)\n\n          if (dispatch) {\n            const offset = tr.selection.from + 1\n\n            tr.replaceSelectionWith(node)\n              .scrollIntoView()\n              .setSelection(TextSelection.near(tr.doc.resolve(offset)))\n          }\n\n          return true\n        },\n      addColumnBefore:\n        () =>\n        ({ state, dispatch }) => {\n          return addColumnBefore(state, dispatch)\n        },\n      addColumnAfter:\n        () =>\n        ({ state, dispatch }) => {\n          return addColumnAfter(state, dispatch)\n        },\n      deleteColumn:\n        () =>\n        ({ state, dispatch }) => {\n          return deleteColumn(state, dispatch)\n        },\n      addRowBefore:\n        () =>\n        ({ state, dispatch }) => {\n          return addRowBefore(state, dispatch)\n        },\n      addRowAfter:\n        () =>\n        ({ state, dispatch }) => {\n          return addRowAfter(state, dispatch)\n        },\n      deleteRow:\n        () =>\n        ({ state, dispatch }) => {\n          return deleteRow(state, dispatch)\n        },\n      deleteTable:\n        () =>\n        ({ state, dispatch }) => {\n          return deleteTable(state, dispatch)\n        },\n      mergeCells:\n        () =>\n        ({ state, dispatch }) => {\n          return mergeCells(state, dispatch)\n        },\n      splitCell:\n        () =>\n        ({ state, dispatch }) => {\n          return splitCell(state, dispatch)\n        },\n      toggleHeaderColumn:\n        () =>\n        ({ state, dispatch }) => {\n          return toggleHeader('column')(state, dispatch)\n        },\n      toggleHeaderRow:\n        () =>\n        ({ state, dispatch }) => {\n          return toggleHeader('row')(state, dispatch)\n        },\n      toggleHeaderCell:\n        () =>\n        ({ state, dispatch }) => {\n          return toggleHeaderCell(state, dispatch)\n        },\n      mergeOrSplit:\n        () =>\n        ({ state, dispatch }) => {\n          if (mergeCells(state, dispatch)) {\n            return true\n          }\n\n          return splitCell(state, dispatch)\n        },\n      setCellAttribute:\n        (name, value) =>\n        ({ state, dispatch }) => {\n          return setCellAttr(name, value)(state, dispatch)\n        },\n      goToNextCell:\n        () =>\n        ({ state, dispatch }) => {\n          return goToNextCell(1)(state, dispatch)\n        },\n      goToPreviousCell:\n        () =>\n        ({ state, dispatch }) => {\n          return goToNextCell(-1)(state, dispatch)\n        },\n      fixTables:\n        () =>\n        ({ state, dispatch }) => {\n          if (dispatch) {\n            fixTables(state)\n          }\n\n          return true\n        },\n      setCellSelection:\n        position =>\n        ({ tr, dispatch }) => {\n          if (dispatch) {\n            const selection = CellSelection.create(tr.doc, position.anchorCell, position.headCell)\n\n            // @ts-ignore\n            tr.setSelection(selection)\n          }\n\n          return true\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Tab: () => {\n        if (this.editor.commands.goToNextCell()) {\n          return true\n        }\n\n        if (!this.editor.can().addRowAfter()) {\n          return false\n        }\n\n        return this.editor.chain().addRowAfter().goToNextCell().run()\n      },\n      'Shift-Tab': () => this.editor.commands.goToPreviousCell(),\n      Backspace: deleteTableWhenAllCellsSelected,\n      'Mod-Backspace': deleteTableWhenAllCellsSelected,\n      Delete: deleteTableWhenAllCellsSelected,\n      'Mod-Delete': deleteTableWhenAllCellsSelected,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    const isResizable = this.options.resizable && this.editor.isEditable\n\n    return [\n      ...(isResizable\n        ? [\n            columnResizing({\n              handleWidth: this.options.handleWidth,\n              cellMinWidth: this.options.cellMinWidth,\n              defaultCellMinWidth: this.options.cellMinWidth,\n              View: this.options.View,\n              lastColumnResizable: this.options.lastColumnResizable,\n            }),\n          ]\n        : []),\n      tableEditing({\n        allowTableNodeSelection: this.options.allowTableNodeSelection,\n      }),\n    ]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      tableRole: callOrReturn(getExtensionField(extension, 'tableRole', context)),\n    }\n  },\n})\n", "export function getColStyleDeclaration(minWidth: number, width: number | undefined): [string, string] {\n  if (width) {\n    // apply the stored width unless it is below the configured minimum cell width\n    return ['width', `${Math.max(width, minWidth)}px`]\n  }\n\n  // set the minimum with on the column if it has no stored width\n  return ['min-width', `${minWidth}px`]\n}\n", "import type { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport type { NodeView, ViewMutationRecord } from '@tiptap/pm/view'\n\nimport { getColStyleDeclaration } from './utilities/colStyle.js'\n\nexport function updateColumns(\n  node: ProseMirrorNode,\n  colgroup: HTMLTableColElement, // <colgroup> has the same prototype as <col>\n  table: HTMLTableElement,\n  cellMinWidth: number,\n  overrideCol?: number,\n  overrideValue?: number,\n) {\n  let totalWidth = 0\n  let fixedWidth = true\n  let nextDOM = colgroup.firstChild\n  const row = node.firstChild\n\n  if (row !== null) {\n    for (let i = 0, col = 0; i < row.childCount; i += 1) {\n      const { colspan, colwidth } = row.child(i).attrs\n\n      for (let j = 0; j < colspan; j += 1, col += 1) {\n        const hasWidth = overrideCol === col ? overrideValue : ((colwidth && colwidth[j]) as number | undefined)\n        const cssWidth = hasWidth ? `${hasWidth}px` : ''\n\n        totalWidth += hasWidth || cellMinWidth\n\n        if (!hasWidth) {\n          fixedWidth = false\n        }\n\n        if (!nextDOM) {\n          const colElement = document.createElement('col')\n\n          const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth)\n\n          colElement.style.setProperty(propertyKey, propertyValue)\n\n          colgroup.appendChild(colElement)\n        } else {\n          if ((nextDOM as HTMLTableColElement).style.width !== cssWidth) {\n            const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth)\n\n            ;(nextDOM as HTMLTableColElement).style.setProperty(propertyKey, propertyValue)\n          }\n\n          nextDOM = nextDOM.nextSibling\n        }\n      }\n    }\n  }\n\n  while (nextDOM) {\n    const after = nextDOM.nextSibling\n\n    nextDOM.parentNode?.removeChild(nextDOM)\n    nextDOM = after\n  }\n\n  if (fixedWidth) {\n    table.style.width = `${totalWidth}px`\n    table.style.minWidth = ''\n  } else {\n    table.style.width = ''\n    table.style.minWidth = `${totalWidth}px`\n  }\n}\n\nexport class TableView implements NodeView {\n  node: ProseMirrorNode\n\n  cellMinWidth: number\n\n  dom: HTMLDivElement\n\n  table: HTMLTableElement\n\n  colgroup: HTMLTableColElement\n\n  contentDOM: HTMLTableSectionElement\n\n  constructor(node: ProseMirrorNode, cellMinWidth: number) {\n    this.node = node\n    this.cellMinWidth = cellMinWidth\n    this.dom = document.createElement('div')\n    this.dom.className = 'tableWrapper'\n    this.table = this.dom.appendChild(document.createElement('table'))\n    this.colgroup = this.table.appendChild(document.createElement('colgroup'))\n    updateColumns(node, this.colgroup, this.table, cellMinWidth)\n    this.contentDOM = this.table.appendChild(document.createElement('tbody'))\n  }\n\n  update(node: ProseMirrorNode) {\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    this.node = node\n    updateColumns(node, this.colgroup, this.table, this.cellMinWidth)\n\n    return true\n  }\n\n  ignoreMutation(mutation: ViewMutationRecord) {\n    return mutation.type === 'attributes' && (mutation.target === this.table || this.colgroup.contains(mutation.target))\n  }\n}\n", "import type { DOMOutputSpec, Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { getColStyleDeclaration } from './colStyle.js'\n\nexport type ColGroup =\n  | {\n      colgroup: DOMOutputSpec\n      tableWidth: string\n      tableMinWidth: string\n    }\n  | Record<string, never>\n\n/**\n * Creates a colgroup element for a table node in ProseMirror.\n *\n * @param node - The ProseMirror node representing the table.\n * @param cellMinWidth - The minimum width of a cell in the table.\n * @param overrideCol - (Optional) The index of the column to override the width of.\n * @param overrideValue - (Optional) The width value to use for the overridden column.\n * @returns An object containing the colgroup element, the total width of the table, and the minimum width of the table.\n */\nexport function createColGroup(node: ProseMirrorNode, cellMinWidth: number): ColGroup\nexport function createColGroup(\n  node: ProseMirrorNode,\n  cellMinWidth: number,\n  overrideCol: number,\n  overrideValue: number,\n): ColGroup\nexport function createColGroup(\n  node: ProseMirrorNode,\n  cellMinWidth: number,\n  overrideCol?: number,\n  overrideValue?: number,\n): ColGroup {\n  let totalWidth = 0\n  let fixedWidth = true\n  const cols: DOMOutputSpec[] = []\n  const row = node.firstChild\n\n  if (!row) {\n    return {}\n  }\n\n  for (let i = 0, col = 0; i < row.childCount; i += 1) {\n    const { colspan, colwidth } = row.child(i).attrs\n\n    for (let j = 0; j < colspan; j += 1, col += 1) {\n      const hasWidth = overrideCol === col ? overrideValue : colwidth && (colwidth[j] as number | undefined)\n\n      totalWidth += hasWidth || cellMinWidth\n\n      if (!hasWidth) {\n        fixedWidth = false\n      }\n\n      const [property, value] = getColStyleDeclaration(cellMinWidth, hasWidth)\n\n      cols.push(['col', { style: `${property}: ${value}` }])\n    }\n  }\n\n  const tableWidth = fixedWidth ? `${totalWidth}px` : ''\n  const tableMinWidth = fixedWidth ? '' : `${totalWidth}px`\n\n  const colgroup: DOMOutputSpec = ['colgroup', {}, ...cols]\n\n  return { colgroup, tableWidth, tableMinWidth }\n}\n", "import type { Fragment, Node as ProsemirrorNode, NodeType } from '@tiptap/pm/model'\n\nexport function createCell(\n  cellType: NodeType,\n  cellContent?: Fragment | ProsemirrorNode | Array<ProsemirrorNode>,\n): ProsemirrorNode | null | undefined {\n  if (cellContent) {\n    return cellType.createChecked(null, cellContent)\n  }\n\n  return cellType.createAndFill()\n}\n", "import type { NodeType, Schema } from '@tiptap/pm/model'\n\nexport function getTableNodeTypes(schema: Schema): { [key: string]: NodeType } {\n  if (schema.cached.tableNodeTypes) {\n    return schema.cached.tableNodeTypes\n  }\n\n  const roles: { [key: string]: NodeType } = {}\n\n  Object.keys(schema.nodes).forEach(type => {\n    const nodeType = schema.nodes[type]\n\n    if (nodeType.spec.tableRole) {\n      roles[nodeType.spec.tableRole] = nodeType\n    }\n  })\n\n  schema.cached.tableNodeTypes = roles\n\n  return roles\n}\n", "import type { Fragment, Node as ProsemirrorN<PERSON>, Schema } from '@tiptap/pm/model'\n\nimport { createCell } from './createCell.js'\nimport { getTableNodeTypes } from './getTableNodeTypes.js'\n\nexport function createTable(\n  schema: Schema,\n  rowsCount: number,\n  colsCount: number,\n  withHeaderRow: boolean,\n  cellContent?: Fragment | ProsemirrorNode | Array<ProsemirrorNode>,\n): ProsemirrorNode {\n  const types = getTableNodeTypes(schema)\n  const headerCells: ProsemirrorNode[] = []\n  const cells: ProsemirrorNode[] = []\n\n  for (let index = 0; index < colsCount; index += 1) {\n    const cell = createCell(types.cell, cellContent)\n\n    if (cell) {\n      cells.push(cell)\n    }\n\n    if (withHeaderRow) {\n      const headerCell = createCell(types.header_cell, cellContent)\n\n      if (headerCell) {\n        headerCells.push(headerCell)\n      }\n    }\n  }\n\n  const rows: ProsemirrorNode[] = []\n\n  for (let index = 0; index < rowsCount; index += 1) {\n    rows.push(types.row.createChecked(null, withHeaderRow && index === 0 ? headerCells : cells))\n  }\n\n  return types.table.createChecked(null, rows)\n}\n", "import type { KeyboardShortcutCommand } from '@tiptap/core'\nimport { findParentNodeClosestToPos } from '@tiptap/core'\n\nimport { isCellSelection } from './isCellSelection.js'\n\nexport const deleteTableWhenAllCellsSelected: KeyboardShortcutCommand = ({ editor }) => {\n  const { selection } = editor.state\n\n  if (!isCellSelection(selection)) {\n    return false\n  }\n\n  let cellCount = 0\n  const table = findParentNodeClosestToPos(selection.ranges[0].$from, node => {\n    return node.type.name === 'table'\n  })\n\n  table?.node.descendants(node => {\n    if (node.type.name === 'table') {\n      return false\n    }\n\n    if (['tableCell', 'tableHeader'].includes(node.type.name)) {\n      cellCount += 1\n    }\n  })\n\n  const allCellsSelected = cellCount === selection.ranges.length\n\n  if (!allCellsSelected) {\n    return false\n  }\n\n  editor.commands.deleteTable()\n\n  return true\n}\n", "import { CellSelection } from '@tiptap/pm/tables'\n\nexport function isCellSelection(value: unknown): value is CellSelection {\n  return value instanceof CellSelection\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,IAAAA,eAAuE;AAEvE,mBAA8B;AAC9B,IAAAC,iBAkBO;;;ACvBA,SAAS,uBAAuB,UAAkB,OAA6C;AACpG,MAAI,OAAO;AAET,WAAO,CAAC,SAAS,GAAG,KAAK,IAAI,OAAO,QAAQ,CAAC,IAAI;AAAA,EACnD;AAGA,SAAO,CAAC,aAAa,GAAG,QAAQ,IAAI;AACtC;;;ACHO,SAAS,cACd,MACA,UACA,OACA,cACA,aACA,eACA;AAZF;AAaE,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,MAAI,UAAU,SAAS;AACvB,QAAM,MAAM,KAAK;AAEjB,MAAI,QAAQ,MAAM;AAChB,aAAS,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,YAAY,KAAK,GAAG;AACnD,YAAM,EAAE,SAAS,SAAS,IAAI,IAAI,MAAM,CAAC,EAAE;AAE3C,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG,OAAO,GAAG;AAC7C,cAAM,WAAW,gBAAgB,MAAM,gBAAkB,YAAY,SAAS,CAAC;AAC/E,cAAM,WAAW,WAAW,GAAG,QAAQ,OAAO;AAE9C,sBAAc,YAAY;AAE1B,YAAI,CAAC,UAAU;AACb,uBAAa;AAAA,QACf;AAEA,YAAI,CAAC,SAAS;AACZ,gBAAM,aAAa,SAAS,cAAc,KAAK;AAE/C,gBAAM,CAAC,aAAa,aAAa,IAAI,uBAAuB,cAAc,QAAQ;AAElF,qBAAW,MAAM,YAAY,aAAa,aAAa;AAEvD,mBAAS,YAAY,UAAU;AAAA,QACjC,OAAO;AACL,cAAK,QAAgC,MAAM,UAAU,UAAU;AAC7D,kBAAM,CAAC,aAAa,aAAa,IAAI,uBAAuB,cAAc,QAAQ;AAEjF,YAAC,QAAgC,MAAM,YAAY,aAAa,aAAa;AAAA,UAChF;AAEA,oBAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,SAAS;AACd,UAAM,QAAQ,QAAQ;AAEtB,kBAAQ,eAAR,mBAAoB,YAAY;AAChC,cAAU;AAAA,EACZ;AAEA,MAAI,YAAY;AACd,UAAM,MAAM,QAAQ,GAAG,UAAU;AACjC,UAAM,MAAM,WAAW;AAAA,EACzB,OAAO;AACL,UAAM,MAAM,QAAQ;AACpB,UAAM,MAAM,WAAW,GAAG,UAAU;AAAA,EACtC;AACF;AAEO,IAAM,YAAN,MAAoC;AAAA,EAazC,YAAY,MAAuB,cAAsB;AACvD,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,MAAM,SAAS,cAAc,KAAK;AACvC,SAAK,IAAI,YAAY;AACrB,SAAK,QAAQ,KAAK,IAAI,YAAY,SAAS,cAAc,OAAO,CAAC;AACjE,SAAK,WAAW,KAAK,MAAM,YAAY,SAAS,cAAc,UAAU,CAAC;AACzE,kBAAc,MAAM,KAAK,UAAU,KAAK,OAAO,YAAY;AAC3D,SAAK,aAAa,KAAK,MAAM,YAAY,SAAS,cAAc,OAAO,CAAC;AAAA,EAC1E;AAAA,EAEA,OAAO,MAAuB;AAC5B,QAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AAChC,aAAO;AAAA,IACT;AAEA,SAAK,OAAO;AACZ,kBAAc,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,YAAY;AAEhE,WAAO;AAAA,EACT;AAAA,EAEA,eAAe,UAA8B;AAC3C,WAAO,SAAS,SAAS,iBAAiB,SAAS,WAAW,KAAK,SAAS,KAAK,SAAS,SAAS,SAAS,MAAM;AAAA,EACpH;AACF;;;AC/EO,SAAS,eACd,MACA,cACA,aACA,eACU;AACV,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,QAAM,OAAwB,CAAC;AAC/B,QAAM,MAAM,KAAK;AAEjB,MAAI,CAAC,KAAK;AACR,WAAO,CAAC;AAAA,EACV;AAEA,WAAS,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,YAAY,KAAK,GAAG;AACnD,UAAM,EAAE,SAAS,SAAS,IAAI,IAAI,MAAM,CAAC,EAAE;AAE3C,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG,OAAO,GAAG;AAC7C,YAAM,WAAW,gBAAgB,MAAM,gBAAgB,YAAa,SAAS,CAAC;AAE9E,oBAAc,YAAY;AAE1B,UAAI,CAAC,UAAU;AACb,qBAAa;AAAA,MACf;AAEA,YAAM,CAAC,UAAU,KAAK,IAAI,uBAAuB,cAAc,QAAQ;AAEvE,WAAK,KAAK,CAAC,OAAO,EAAE,OAAO,GAAG,QAAQ,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,IACvD;AAAA,EACF;AAEA,QAAM,aAAa,aAAa,GAAG,UAAU,OAAO;AACpD,QAAM,gBAAgB,aAAa,KAAK,GAAG,UAAU;AAErD,QAAM,WAA0B,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI;AAExD,SAAO,EAAE,UAAU,YAAY,cAAc;AAC/C;;;ACjEO,SAAS,WACd,UACA,aACoC;AACpC,MAAI,aAAa;AACf,WAAO,SAAS,cAAc,MAAM,WAAW;AAAA,EACjD;AAEA,SAAO,SAAS,cAAc;AAChC;;;ACTO,SAAS,kBAAkB,QAA6C;AAC7E,MAAI,OAAO,OAAO,gBAAgB;AAChC,WAAO,OAAO,OAAO;AAAA,EACvB;AAEA,QAAM,QAAqC,CAAC;AAE5C,SAAO,KAAK,OAAO,KAAK,EAAE,QAAQ,UAAQ;AACxC,UAAM,WAAW,OAAO,MAAM,IAAI;AAElC,QAAI,SAAS,KAAK,WAAW;AAC3B,YAAM,SAAS,KAAK,SAAS,IAAI;AAAA,IACnC;AAAA,EACF,CAAC;AAED,SAAO,OAAO,iBAAiB;AAE/B,SAAO;AACT;;;ACfO,SAAS,YACd,QACA,WACA,WACA,eACA,aACiB;AACjB,QAAM,QAAQ,kBAAkB,MAAM;AACtC,QAAM,cAAiC,CAAC;AACxC,QAAM,QAA2B,CAAC;AAElC,WAAS,QAAQ,GAAG,QAAQ,WAAW,SAAS,GAAG;AACjD,UAAM,OAAO,WAAW,MAAM,MAAM,WAAW;AAE/C,QAAI,MAAM;AACR,YAAM,KAAK,IAAI;AAAA,IACjB;AAEA,QAAI,eAAe;AACjB,YAAM,aAAa,WAAW,MAAM,aAAa,WAAW;AAE5D,UAAI,YAAY;AACd,oBAAY,KAAK,UAAU;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAA0B,CAAC;AAEjC,WAAS,QAAQ,GAAG,QAAQ,WAAW,SAAS,GAAG;AACjD,SAAK,KAAK,MAAM,IAAI,cAAc,MAAM,iBAAiB,UAAU,IAAI,cAAc,KAAK,CAAC;AAAA,EAC7F;AAEA,SAAO,MAAM,MAAM,cAAc,MAAM,IAAI;AAC7C;;;ACtCA,kBAA2C;;;ACD3C,oBAA8B;AAEvB,SAAS,gBAAgB,OAAwC;AACtE,SAAO,iBAAiB;AAC1B;;;ADCO,IAAM,kCAA2D,CAAC,EAAE,OAAO,MAAM;AACtF,QAAM,EAAE,UAAU,IAAI,OAAO;AAE7B,MAAI,CAAC,gBAAgB,SAAS,GAAG;AAC/B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY;AAChB,QAAM,YAAQ,wCAA2B,UAAU,OAAO,CAAC,EAAE,OAAO,UAAQ;AAC1E,WAAO,KAAK,KAAK,SAAS;AAAA,EAC5B,CAAC;AAED,iCAAO,KAAK,YAAY,UAAQ;AAC9B,QAAI,KAAK,KAAK,SAAS,SAAS;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,aAAa,aAAa,EAAE,SAAS,KAAK,KAAK,IAAI,GAAG;AACzD,mBAAa;AAAA,IACf;AAAA,EACF;AAEA,QAAM,mBAAmB,cAAc,UAAU,OAAO;AAExD,MAAI,CAAC,kBAAkB;AACrB,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,YAAY;AAE5B,SAAO;AACT;;;APgMO,IAAM,QAAQ,kBAAK,OAAqB;AAAA,EAC7C,MAAM;AAAA;AAAA,EAGN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,MACjB,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA;AAAA,MAEd,MAAM;AAAA,MACN,qBAAqB;AAAA,MACrB,yBAAyB;AAAA,IAC3B;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,EAET,WAAW;AAAA,EAEX,WAAW;AAAA,EAEX,OAAO;AAAA,EAEP,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,QAAQ,CAAC;AAAA,EAC1B;AAAA,EAEA,WAAW,EAAE,MAAM,eAAe,GAAG;AACnC,UAAM,EAAE,UAAU,YAAY,cAAc,IAAI,eAAe,MAAM,KAAK,QAAQ,YAAY;AAE9F,UAAM,QAAuB;AAAA,MAC3B;AAAA,UACA,8BAAgB,KAAK,QAAQ,gBAAgB,gBAAgB;AAAA,QAC3D,OAAO,aAAa,UAAU,UAAU,KAAK,cAAc,aAAa;AAAA,MAC1E,CAAC;AAAA,MACD;AAAA,MACA,CAAC,SAAS,CAAC;AAAA,IACb;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,aACE,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,gBAAgB,KAAK,IAAI,CAAC,MACjD,CAAC,EAAE,IAAI,UAAU,OAAO,MAAM;AAC5B,cAAM,OAAO,YAAY,OAAO,QAAQ,MAAM,MAAM,aAAa;AAEjE,YAAI,UAAU;AACZ,gBAAM,SAAS,GAAG,UAAU,OAAO;AAEnC,aAAG,qBAAqB,IAAI,EACzB,eAAe,EACf,aAAa,2BAAc,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,CAAC;AAAA,QAC5D;AAEA,eAAO;AAAA,MACT;AAAA,MACF,iBACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,gCAAgB,OAAO,QAAQ;AAAA,MACxC;AAAA,MACF,gBACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,+BAAe,OAAO,QAAQ;AAAA,MACvC;AAAA,MACF,cACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,6BAAa,OAAO,QAAQ;AAAA,MACrC;AAAA,MACF,cACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,6BAAa,OAAO,QAAQ;AAAA,MACrC;AAAA,MACF,aACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,4BAAY,OAAO,QAAQ;AAAA,MACpC;AAAA,MACF,WACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,0BAAU,OAAO,QAAQ;AAAA,MAClC;AAAA,MACF,aACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,4BAAY,OAAO,QAAQ;AAAA,MACpC;AAAA,MACF,YACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,2BAAW,OAAO,QAAQ;AAAA,MACnC;AAAA,MACF,WACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,0BAAU,OAAO,QAAQ;AAAA,MAClC;AAAA,MACF,oBACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,6BAAa,QAAQ,EAAE,OAAO,QAAQ;AAAA,MAC/C;AAAA,MACF,iBACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,6BAAa,KAAK,EAAE,OAAO,QAAQ;AAAA,MAC5C;AAAA,MACF,kBACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,iCAAiB,OAAO,QAAQ;AAAA,MACzC;AAAA,MACF,cACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,gBAAI,2BAAW,OAAO,QAAQ,GAAG;AAC/B,iBAAO;AAAA,QACT;AAEA,mBAAO,0BAAU,OAAO,QAAQ;AAAA,MAClC;AAAA,MACF,kBACE,CAAC,MAAM,UACP,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,4BAAY,MAAM,KAAK,EAAE,OAAO,QAAQ;AAAA,MACjD;AAAA,MACF,cACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,6BAAa,CAAC,EAAE,OAAO,QAAQ;AAAA,MACxC;AAAA,MACF,kBACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,mBAAO,6BAAa,EAAE,EAAE,OAAO,QAAQ;AAAA,MACzC;AAAA,MACF,WACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,YAAI,UAAU;AACZ,wCAAU,KAAK;AAAA,QACjB;AAEA,eAAO;AAAA,MACT;AAAA,MACF,kBACE,cACA,CAAC,EAAE,IAAI,SAAS,MAAM;AACpB,YAAI,UAAU;AACZ,gBAAM,YAAY,6BAAc,OAAO,GAAG,KAAK,SAAS,YAAY,SAAS,QAAQ;AAGrF,aAAG,aAAa,SAAS;AAAA,QAC3B;AAEA,eAAO;AAAA,MACT;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,KAAK,MAAM;AACT,YAAI,KAAK,OAAO,SAAS,aAAa,GAAG;AACvC,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,OAAO,IAAI,EAAE,YAAY,GAAG;AACpC,iBAAO;AAAA,QACT;AAEA,eAAO,KAAK,OAAO,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,IAAI;AAAA,MAC9D;AAAA,MACA,aAAa,MAAM,KAAK,OAAO,SAAS,iBAAiB;AAAA,MACzD,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,cAAc;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,UAAM,cAAc,KAAK,QAAQ,aAAa,KAAK,OAAO;AAE1D,WAAO;AAAA,MACL,GAAI,cACA;AAAA,YACE,+BAAe;AAAA,UACb,aAAa,KAAK,QAAQ;AAAA,UAC1B,cAAc,KAAK,QAAQ;AAAA,UAC3B,qBAAqB,KAAK,QAAQ;AAAA,UAClC,MAAM,KAAK,QAAQ;AAAA,UACnB,qBAAqB,KAAK,QAAQ;AAAA,QACpC,CAAC;AAAA,MACH,IACA,CAAC;AAAA,UACL,6BAAa;AAAA,QACX,yBAAyB,KAAK,QAAQ;AAAA,MACxC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,iBAAiB,WAAW;AAC1B,UAAM,UAAU;AAAA,MACd,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,SAAS,UAAU;AAAA,IACrB;AAEA,WAAO;AAAA,MACL,eAAW,+BAAa,gCAAkB,WAAW,aAAa,OAAO,CAAC;AAAA,IAC5E;AAAA,EACF;AACF,CAAC;", "names": ["import_core", "import_tables"]}