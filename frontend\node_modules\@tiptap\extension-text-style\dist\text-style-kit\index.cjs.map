{"version": 3, "sources": ["../../src/text-style-kit/index.ts", "../../src/text-style/index.ts", "../../src/background-color/background-color.ts", "../../src/color/color.ts", "../../src/font-family/font-family.ts", "../../src/font-size/font-size.ts", "../../src/line-height/line-height.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\n\nimport type { BackgroundColorOptions } from '../background-color/index.js'\nimport { BackgroundColor } from '../background-color/index.js'\nimport type { ColorOptions } from '../color/index.js'\nimport { Color } from '../color/index.js'\nimport type { FontFamilyOptions } from '../font-family/index.js'\nimport { FontFamily } from '../font-family/index.js'\nimport type { FontSizeOptions } from '../font-size/index.js'\nimport { FontSize } from '../font-size/index.js'\nimport type { LineHeightOptions } from '../line-height/index.js'\nimport { LineHeight } from '../line-height/index.js'\nimport type { TextStyleOptions } from '../text-style/index.js'\nimport { TextStyle } from '../text-style/index.js'\n\nexport interface TextStyleKitOptions {\n  /**\n   * If set to false, the background color extension will not be registered\n   * @example backgroundColor: false\n   */\n  backgroundColor: Partial<BackgroundColorOptions> | false\n  /**\n   * If set to false, the color extension will not be registered\n   * @example color: false\n   */\n  color: Partial<ColorOptions> | false\n  /**\n   * If set to false, the font family extension will not be registered\n   * @example fontFamily: false\n   */\n  fontFamily: Partial<FontFamilyOptions> | false\n  /**\n   * If set to false, the font size extension will not be registered\n   * @example fontSize: false\n   */\n  fontSize: Partial<FontSizeOptions> | false\n  /**\n   * If set to false, the line height extension will not be registered\n   * @example lineHeight: false\n   */\n  lineHeight: Partial<LineHeightOptions> | false\n  /**\n   * If set to false, the text style extension will not be registered (required for other text style extensions)\n   * @example textStyle: false\n   */\n  textStyle: Partial<TextStyleOptions> | false\n}\n\n/**\n * The table kit is a collection of table editor extensions.\n *\n * It’s a good starting point for building your own table in Tiptap.\n */\nexport const TextStyleKit = Extension.create<TextStyleKitOptions>({\n  name: 'textStyleKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.backgroundColor !== false) {\n      extensions.push(BackgroundColor.configure(this.options.backgroundColor))\n    }\n\n    if (this.options.color !== false) {\n      extensions.push(Color.configure(this.options.color))\n    }\n\n    if (this.options.fontFamily !== false) {\n      extensions.push(FontFamily.configure(this.options.fontFamily))\n    }\n\n    if (this.options.fontSize !== false) {\n      extensions.push(FontSize.configure(this.options.fontSize))\n    }\n\n    if (this.options.lineHeight !== false) {\n      extensions.push(LineHeight.configure(this.options.lineHeight))\n    }\n\n    if (this.options.textStyle !== false) {\n      extensions.push(TextStyle.configure(this.options.textStyle))\n    }\n\n    return extensions\n  },\n})\n", "import { Mark, mergeAttributes } from '@tiptap/core'\n\nimport type { TextStyleAttributes } from '../index.js'\n\nexport interface TextStyleOptions {\n  /**\n   * HTML attributes to add to the span element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n  /**\n   * When enabled, merges the styles of nested spans into the child span during HTML parsing.\n   * This prioritizes the style of the child span.\n   * Used when parsing content created in other editors.\n   * (Fix for ProseMirror's default behavior.)\n   * @default true\n   */\n  mergeNestedSpanStyles: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textStyle: {\n      /**\n       * Remove spans without inline style attributes.\n       * @example editor.commands.removeEmptyTextStyle()\n       */\n      removeEmptyTextStyle: () => ReturnType\n      /**\n       * Toggle a text style\n       * @param attributes The text style attributes\n       * @example editor.commands.toggleTextStyle({ fontWeight: 'bold' })\n       */\n      toggleTextStyle: (attributes?: TextStyleAttributes) => ReturnType\n    }\n  }\n}\n\nconst mergeNestedSpanStyles = (element: HTMLElement) => {\n  if (!element.children.length) {\n    return\n  }\n  const childSpans = element.querySelectorAll('span')\n\n  if (!childSpans) {\n    return\n  }\n\n  childSpans.forEach(childSpan => {\n    const childStyle = childSpan.getAttribute('style')\n    const closestParentSpanStyleOfChild = childSpan.parentElement?.closest('span')?.getAttribute('style')\n\n    childSpan.setAttribute('style', `${closestParentSpanStyleOfChild};${childStyle}`)\n  })\n}\n\n/**\n * This extension allows you to create text styles. It is required by default\n * for the `text-color` and `font-family` extensions.\n * @see https://www.tiptap.dev/api/marks/text-style\n */\nexport const TextStyle = Mark.create<TextStyleOptions>({\n  name: 'textStyle',\n\n  priority: 101,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      mergeNestedSpanStyles: true,\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'span',\n        consuming: false,\n        getAttrs: element => {\n          const hasStyles = (element as HTMLElement).hasAttribute('style')\n\n          if (!hasStyles) {\n            return false\n          }\n\n          if (this.options.mergeNestedSpanStyles) {\n            mergeNestedSpanStyles(element)\n          }\n\n          return {}\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleTextStyle:\n        attributes =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name, attributes)\n        },\n      removeEmptyTextStyle:\n        () =>\n        ({ tr }) => {\n          const { selection } = tr\n\n          // Gather all of the nodes within the selection range.\n          // We would need to go through each node individually\n          // to check if it has any inline style attributes.\n          // Otherwise, calling commands.unsetMark(this.name)\n          // removes everything from all the nodes\n          // within the selection range.\n          tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {\n            // Check if it's a paragraph element, if so, skip this node as we apply\n            // the text style to inline text nodes only (span).\n            if (node.isTextblock) {\n              return true\n            }\n\n            // Check if the node has no inline style attributes.\n            // Filter out non-`textStyle` marks.\n            if (\n              !node.marks\n                .filter(mark => mark.type === this.type)\n                .some(mark => Object.values(mark.attrs).some(value => !!value))\n            ) {\n              // Proceed with the removal of the `textStyle` mark for this node only\n              tr.removeMark(pos, pos + node.nodeSize, this.type)\n            }\n          })\n\n          return true\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type BackgroundColorOptions = {\n  /**\n   * The types where the color can be applied\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    backgroundColor: {\n      /**\n       * Set the text color\n       * @param backgroundColor The color to set\n       * @example editor.commands.setColor('red')\n       */\n      setBackgroundColor: (backgroundColor: string) => ReturnType\n\n      /**\n       * Unset the text backgroundColor\n       * @example editor.commands.unsetBackgroundColor()\n       */\n      unsetBackgroundColor: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    backgroundColor?: string | null\n  }\n}\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/background-color\n */\nexport const BackgroundColor = Extension.create<BackgroundColorOptions>({\n  name: 'backgroundColor',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          backgroundColor: {\n            default: null,\n            parseHTML: element => {\n              // Prefer the raw inline `style` attribute so we preserve\n              // the original format (e.g. `#rrggbb`) instead of the\n              // computed `rgb(...)` value returned by `element.style.backgroundColor`.\n              // When nested spans are merged the style attribute may contain\n              // multiple `background-color:` declarations (parent;child). We should pick\n              // the last declaration so the child's background color takes priority.\n              const styleAttr = element.getAttribute('style')\n              if (styleAttr) {\n                const decls = styleAttr\n                  .split(';')\n                  .map(s => s.trim())\n                  .filter(Boolean)\n                for (let i = decls.length - 1; i >= 0; i -= 1) {\n                  const parts = decls[i].split(':')\n                  if (parts.length >= 2) {\n                    const prop = parts[0].trim().toLowerCase()\n                    const val = parts.slice(1).join(':').trim()\n                    if (prop === 'background-color') {\n                      return val.replace(/['\"]+/g, '')\n                    }\n                  }\n                }\n              }\n\n              return element.style.backgroundColor?.replace(/['\"]+/g, '')\n            },\n            renderHTML: attributes => {\n              if (!attributes.backgroundColor) {\n                return {}\n              }\n\n              return {\n                style: `background-color: ${attributes.backgroundColor}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setBackgroundColor:\n        backgroundColor =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { backgroundColor }).run()\n        },\n      unsetBackgroundColor:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { backgroundColor: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type ColorOptions = {\n  /**\n   * The types where the color can be applied\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    color: {\n      /**\n       * Set the text color\n       * @param color The color to set\n       * @example editor.commands.setColor('red')\n       */\n      setColor: (color: string) => ReturnType\n\n      /**\n       * Unset the text color\n       * @example editor.commands.unsetColor()\n       */\n      unsetColor: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    color?: string | null\n  }\n}\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/color\n */\nexport const Color = Extension.create<ColorOptions>({\n  name: 'color',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          color: {\n            default: null,\n            parseHTML: element => {\n              // Prefer the raw inline `style` attribute so we preserve\n              // the original format (e.g. `#rrggbb`) instead of the\n              // computed `rgb(...)` value returned by `element.style.color`.\n              // When nested spans are merged the style attribute may contain\n              // multiple `color:` declarations (parent;child). We should pick\n              // the last declaration so the child's color takes priority.\n              const styleAttr = element.getAttribute('style')\n              if (styleAttr) {\n                const decls = styleAttr\n                  .split(';')\n                  .map(s => s.trim())\n                  .filter(Boolean)\n                for (let i = decls.length - 1; i >= 0; i -= 1) {\n                  const parts = decls[i].split(':')\n                  if (parts.length >= 2) {\n                    const prop = parts[0].trim().toLowerCase()\n                    const val = parts.slice(1).join(':').trim()\n                    if (prop === 'color') {\n                      return val.replace(/['\"]+/g, '')\n                    }\n                  }\n                }\n              }\n\n              return element.style.color?.replace(/['\"]+/g, '')\n            },\n            renderHTML: attributes => {\n              if (!attributes.color) {\n                return {}\n              }\n\n              return {\n                style: `color: ${attributes.color}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setColor:\n        color =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { color }).run()\n        },\n      unsetColor:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { color: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type FontFamilyOptions = {\n  /**\n   * A list of node names where the font family can be applied.\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    fontFamily: {\n      /**\n       * Set the font family\n       * @param fontFamily The font family\n       * @example editor.commands.setFontFamily('Arial')\n       */\n      setFontFamily: (fontFamily: string) => ReturnType\n      /**\n       * Unset the font family\n       * @example editor.commands.unsetFontFamily()\n       */\n      unsetFontFamily: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    fontFamily?: string | null\n  }\n}\n\n/**\n * This extension allows you to set a font family for text.\n * @see https://www.tiptap.dev/api/extensions/font-family\n */\nexport const FontFamily = Extension.create<FontFamilyOptions>({\n  name: 'fontFamily',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          fontFamily: {\n            default: null,\n            parseHTML: element => element.style.fontFamily,\n            renderHTML: attributes => {\n              if (!attributes.fontFamily) {\n                return {}\n              }\n\n              return {\n                style: `font-family: ${attributes.fontFamily}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setFontFamily:\n        fontFamily =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { fontFamily }).run()\n        },\n      unsetFontFamily:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { fontFamily: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type FontSizeOptions = {\n  /**\n   * A list of node names where the font size can be applied.\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    fontSize: {\n      /**\n       * Set the font size\n       * @param fontSize The font size\n       * @example editor.commands.setFontSize('Arial')\n       */\n      setFontSize: (fontSize: string) => ReturnType\n      /**\n       * Unset the font size\n       * @example editor.commands.unsetFontSize()\n       */\n      unsetFontSize: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    fontSize?: string | null\n  }\n}\n\n/**\n * This extension allows you to set a font size for text.\n * @see https://www.tiptap.dev/api/extensions/font-size\n */\nexport const FontSize = Extension.create<FontSizeOptions>({\n  name: 'fontSize',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          fontSize: {\n            default: null,\n            parseHTML: element => element.style.fontSize,\n            renderHTML: attributes => {\n              if (!attributes.fontSize) {\n                return {}\n              }\n\n              return {\n                style: `font-size: ${attributes.fontSize}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setFontSize:\n        fontSize =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { fontSize }).run()\n        },\n      unsetFontSize:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { fontSize: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type LineHeightOptions = {\n  /**\n   * A list of node names where the line height can be applied.\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    lineHeight: {\n      /**\n       * Set the line height\n       * @param lineHeight The line height\n       * @example editor.commands.setLineHeight('1.5')\n       */\n      setLineHeight: (lineHeight: string) => ReturnType\n      /**\n       * Unset the line height\n       * @example editor.commands.unsetLineHeight()\n       */\n      unsetLineHeight: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    lineHeight?: string | null\n  }\n}\n\n/**\n * This extension allows you to set the line-height for text.\n * @see https://www.tiptap.dev/api/extensions/line-height\n */\nexport const LineHeight = Extension.create<LineHeightOptions>({\n  name: 'lineHeight',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          lineHeight: {\n            default: null,\n            parseHTML: element => element.style.lineHeight,\n            renderHTML: attributes => {\n              if (!attributes.lineHeight) {\n                return {}\n              }\n\n              return {\n                style: `line-height: ${attributes.lineHeight}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setLineHeight:\n        lineHeight =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { lineHeight }).run()\n        },\n      unsetLineHeight:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { lineHeight: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA,eAA0B;;;ACA1B,kBAAsC;AAuCtC,IAAM,wBAAwB,CAAC,YAAyB;AACtD,MAAI,CAAC,QAAQ,SAAS,QAAQ;AAC5B;AAAA,EACF;AACA,QAAM,aAAa,QAAQ,iBAAiB,MAAM;AAElD,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AAEA,aAAW,QAAQ,eAAa;AAjDlC;AAkDI,UAAM,aAAa,UAAU,aAAa,OAAO;AACjD,UAAM,iCAAgC,qBAAU,kBAAV,mBAAyB,QAAQ,YAAjC,mBAA0C,aAAa;AAE7F,cAAU,aAAa,SAAS,GAAG,6BAA6B,IAAI,UAAU,EAAE;AAAA,EAClF,CAAC;AACH;AAOO,IAAM,YAAY,iBAAK,OAAyB;AAAA,EACrD,MAAM;AAAA,EAEN,UAAU;AAAA,EAEV,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,MACjB,uBAAuB;AAAA,IACzB;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU,aAAW;AACnB,gBAAM,YAAa,QAAwB,aAAa,OAAO;AAE/D,cAAI,CAAC,WAAW;AACd,mBAAO;AAAA,UACT;AAEA,cAAI,KAAK,QAAQ,uBAAuB;AACtC,kCAAsB,OAAO;AAAA,UAC/B;AAEA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,YAAQ,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EACjF;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,iBACE,gBACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,MAAM,UAAU;AAAA,MAClD;AAAA,MACF,sBACE,MACA,CAAC,EAAE,GAAG,MAAM;AACV,cAAM,EAAE,UAAU,IAAI;AAQtB,WAAG,IAAI,aAAa,UAAU,MAAM,UAAU,IAAI,CAAC,MAAM,QAAQ;AAG/D,cAAI,KAAK,aAAa;AACpB,mBAAO;AAAA,UACT;AAIA,cACE,CAAC,KAAK,MACH,OAAO,UAAQ,KAAK,SAAS,KAAK,IAAI,EACtC,KAAK,UAAQ,OAAO,OAAO,KAAK,KAAK,EAAE,KAAK,WAAS,CAAC,CAAC,KAAK,CAAC,GAChE;AAEA,eAAG,WAAW,KAAK,MAAM,KAAK,UAAU,KAAK,IAAI;AAAA,UACnD;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;AC3ID,IAAAC,eAA0B;AAyCnB,IAAM,kBAAkB,uBAAU,OAA+B;AAAA,EACtE,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,OAAO,CAAC,WAAW;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,sBAAsB;AACpB,WAAO;AAAA,MACL;AAAA,QACE,OAAO,KAAK,QAAQ;AAAA,QACpB,YAAY;AAAA,UACV,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,WAAW,aAAW;AA3DlC;AAkEc,oBAAM,YAAY,QAAQ,aAAa,OAAO;AAC9C,kBAAI,WAAW;AACb,sBAAM,QAAQ,UACX,MAAM,GAAG,EACT,IAAI,OAAK,EAAE,KAAK,CAAC,EACjB,OAAO,OAAO;AACjB,yBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC7C,wBAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,GAAG;AAChC,sBAAI,MAAM,UAAU,GAAG;AACrB,0BAAM,OAAO,MAAM,CAAC,EAAE,KAAK,EAAE,YAAY;AACzC,0BAAM,MAAM,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,EAAE,KAAK;AAC1C,wBAAI,SAAS,oBAAoB;AAC/B,6BAAO,IAAI,QAAQ,UAAU,EAAE;AAAA,oBACjC;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAEA,sBAAO,aAAQ,MAAM,oBAAd,mBAA+B,QAAQ,UAAU;AAAA,YAC1D;AAAA,YACA,YAAY,gBAAc;AACxB,kBAAI,CAAC,WAAW,iBAAiB;AAC/B,uBAAO,CAAC;AAAA,cACV;AAEA,qBAAO;AAAA,gBACL,OAAO,qBAAqB,WAAW,eAAe;AAAA,cACxD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,oBACE,qBACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,gBAAgB,CAAC,EAAE,IAAI;AAAA,MAC/D;AAAA,MACF,sBACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,iBAAiB,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;AAAA,MAC5F;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;ACjHD,IAAAC,eAA0B;AAyCnB,IAAM,QAAQ,uBAAU,OAAqB;AAAA,EAClD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,OAAO,CAAC,WAAW;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,sBAAsB;AACpB,WAAO;AAAA,MACL;AAAA,QACE,OAAO,KAAK,QAAQ;AAAA,QACpB,YAAY;AAAA,UACV,OAAO;AAAA,YACL,SAAS;AAAA,YACT,WAAW,aAAW;AA3DlC;AAkEc,oBAAM,YAAY,QAAQ,aAAa,OAAO;AAC9C,kBAAI,WAAW;AACb,sBAAM,QAAQ,UACX,MAAM,GAAG,EACT,IAAI,OAAK,EAAE,KAAK,CAAC,EACjB,OAAO,OAAO;AACjB,yBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC7C,wBAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,GAAG;AAChC,sBAAI,MAAM,UAAU,GAAG;AACrB,0BAAM,OAAO,MAAM,CAAC,EAAE,KAAK,EAAE,YAAY;AACzC,0BAAM,MAAM,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,EAAE,KAAK;AAC1C,wBAAI,SAAS,SAAS;AACpB,6BAAO,IAAI,QAAQ,UAAU,EAAE;AAAA,oBACjC;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAEA,sBAAO,aAAQ,MAAM,UAAd,mBAAqB,QAAQ,UAAU;AAAA,YAChD;AAAA,YACA,YAAY,gBAAc;AACxB,kBAAI,CAAC,WAAW,OAAO;AACrB,uBAAO,CAAC;AAAA,cACV;AAEA,qBAAO;AAAA,gBACL,OAAO,UAAU,WAAW,KAAK;AAAA,cACnC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,UACE,WACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,MAAM,CAAC,EAAE,IAAI;AAAA,MACrD;AAAA,MACF,YACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,OAAO,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;AAAA,MAClF;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;ACjHD,IAAAC,eAA0B;AAwCnB,IAAM,aAAa,uBAAU,OAA0B;AAAA,EAC5D,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,OAAO,CAAC,WAAW;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,sBAAsB;AACpB,WAAO;AAAA,MACL;AAAA,QACE,OAAO,KAAK,QAAQ;AAAA,QACpB,YAAY;AAAA,UACV,YAAY;AAAA,YACV,SAAS;AAAA,YACT,WAAW,aAAW,QAAQ,MAAM;AAAA,YACpC,YAAY,gBAAc;AACxB,kBAAI,CAAC,WAAW,YAAY;AAC1B,uBAAO,CAAC;AAAA,cACV;AAEA,qBAAO;AAAA,gBACL,OAAO,gBAAgB,WAAW,UAAU;AAAA,cAC9C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,eACE,gBACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,CAAC,EAAE,IAAI;AAAA,MAC1D;AAAA,MACF,iBACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,YAAY,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;AAAA,MACvF;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;ACtFD,IAAAC,eAA0B;AAwCnB,IAAM,WAAW,uBAAU,OAAwB;AAAA,EACxD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,OAAO,CAAC,WAAW;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,sBAAsB;AACpB,WAAO;AAAA,MACL;AAAA,QACE,OAAO,KAAK,QAAQ;AAAA,QACpB,YAAY;AAAA,UACV,UAAU;AAAA,YACR,SAAS;AAAA,YACT,WAAW,aAAW,QAAQ,MAAM;AAAA,YACpC,YAAY,gBAAc;AACxB,kBAAI,CAAC,WAAW,UAAU;AACxB,uBAAO,CAAC;AAAA,cACV;AAEA,qBAAO;AAAA,gBACL,OAAO,cAAc,WAAW,QAAQ;AAAA,cAC1C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,aACE,cACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,SAAS,CAAC,EAAE,IAAI;AAAA,MACxD;AAAA,MACF,eACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,UAAU,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;AAAA,MACrF;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;ACtFD,IAAAC,eAA0B;AAwCnB,IAAM,aAAa,uBAAU,OAA0B;AAAA,EAC5D,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,OAAO,CAAC,WAAW;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,sBAAsB;AACpB,WAAO;AAAA,MACL;AAAA,QACE,OAAO,KAAK,QAAQ;AAAA,QACpB,YAAY;AAAA,UACV,YAAY;AAAA,YACV,SAAS;AAAA,YACT,WAAW,aAAW,QAAQ,MAAM;AAAA,YACpC,YAAY,gBAAc;AACxB,kBAAI,CAAC,WAAW,YAAY;AAC1B,uBAAO,CAAC;AAAA,cACV;AAEA,qBAAO;AAAA,gBACL,OAAO,gBAAgB,WAAW,UAAU;AAAA,cAC9C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,eACE,gBACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,CAAC,EAAE,IAAI;AAAA,MAC1D;AAAA,MACF,iBACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,YAAY,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;AAAA,MACvF;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;ANnCM,IAAM,eAAe,uBAAU,OAA4B;AAAA,EAChE,MAAM;AAAA,EAEN,gBAAgB;AACd,UAAM,aAAa,CAAC;AAEpB,QAAI,KAAK,QAAQ,oBAAoB,OAAO;AAC1C,iBAAW,KAAK,gBAAgB,UAAU,KAAK,QAAQ,eAAe,CAAC;AAAA,IACzE;AAEA,QAAI,KAAK,QAAQ,UAAU,OAAO;AAChC,iBAAW,KAAK,MAAM,UAAU,KAAK,QAAQ,KAAK,CAAC;AAAA,IACrD;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;AAAA,IAC/D;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;AAAA,IAC3D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;AAAA,IAC/D;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;AAAA,IAC7D;AAEA,WAAO;AAAA,EACT;AACF,CAAC;", "names": ["import_core", "import_core", "import_core", "import_core", "import_core", "import_core"]}